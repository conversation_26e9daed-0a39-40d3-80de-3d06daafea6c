import React from 'react';
import {
  BaseButton,
  LongPressGestureHandler,
  State,
} from 'react-native-gesture-handler';
import {View as NativeView} from 'react-native';

import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  FadeIn,
} from 'react-native-reanimated';

const BaseButtonContext = React.createContext({isValid: false});
BaseButtonContext.displayName = 'BaseButtonContext';

const SPRING_CONFIG = {
  damping: 800,
  stiffness: 100,
  mass: 0.001,
};

function ViewChildren({children = null, style = {}}) {
  const {isValid} = React.useContext(BaseButtonContext);

  if (!isValid) {
    throw new Error('View Children out of Touch Aware Button Context');
  }

  return (
    <NativeView style={[style]} accessible accessibilityRole="button">
        {children}
    </NativeView>
  );
}

function TouchAwareButton({
  style = {},
  children: _children = null,
  onPress = () => null,
  onLongPress = () => null,
  disabled = false,
  scaleAnimationThreshold = 0.92,
  disableTapAnimation = false,
  disableLongPressAnimation = false,
  containerStyle = {},
}) {
  const children = [];
  const viewChildren = [];

  const scale = useSharedValue(1);
  const transform = useAnimatedStyle(() => ({
    transform: [{scale: scale.value}],
  }));

  React.Children.forEach(_children, child => {
    if (child?.type === ViewChildren) {
      viewChildren.push(child);
    } else {
      children.push(child);
    }
  });

  const handleOnPress = React.useCallback(
    evt => {
      if (!disableTapAnimation) {
        scale.value = withSequence(
          withSpring(scaleAnimationThreshold, SPRING_CONFIG),
          withSpring(1),
        );
      }
      onPress(evt);
    },
    [onPress, scaleAnimationThreshold],
  );

  const handleLongPress = React.useCallback(
    event => {
      const state = event.nativeEvent.state;
      if (state === State.ACTIVE) {
        if (!disableLongPressAnimation) {
          scale.value = withSpring(scaleAnimationThreshold);
        }
        onLongPress(event);
      } else {
        if (!disableLongPressAnimation) {
          scale.value = withSpring(1);
        }
      }
    },
    [scaleAnimationThreshold, scale, onLongPress, disableLongPressAnimation],
  );

  return (
    <BaseButtonContext.Provider value={{isValid: true}}>
      <LongPressGestureHandler
        minDurationMs={200}
        onHandlerStateChange={handleLongPress}>
        <Animated.View
          style={[transform, containerStyle]}
          entering={FadeIn.duration(200)}>
          <BaseButton
            style={[{opacity: disabled ? 0.5 : 1}, style]}
            onPress={handleOnPress}
            enabled={!disabled}
            rippleColor="transparent">
            {viewChildren}
            {children}
          </BaseButton>
        </Animated.View>
      </LongPressGestureHandler>
    </BaseButtonContext.Provider>
  );
}

TouchAwareButton.View = ViewChildren;

export default TouchAwareButton;
