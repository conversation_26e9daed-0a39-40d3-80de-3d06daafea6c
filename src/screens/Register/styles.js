import {StyleSheet, Dimensions, Platform} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

const {width: WIDTH} = Dimensions.get('window');

const isIOS = Platform.OS == 'ios';

export const styles = StyleSheet.create({
  srollViewWrapper: {
    flexGrow: 1,
    backgroundColor: colors.white,
  },
  container: {
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  loginContainer: {
    width: WIDTH * 0.9,
    backgroundColor: colors.white,
    alignItems: 'center',
    paddingVertical: 30,
  },
  submitButton: {
    backgroundColor: colors.primary,
    height: spacings.xxxl,
    marginTop: spacings.xl,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacings.sm,
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSize.md,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  titleWrapper: {
    width: '100%',
    marginBottom: spacings.sm,
  },
  titleStyle: {
    fontSize: fontSize.lg,
    fontFamily: 'Gugi-Regular',
    color: colors.black,
  },
  inputCard: {
    marginVertical: spacings.md,
  },
  inputLabel: {
    fontSize: fontSize.md,
    color: colors.black,
    marginBottom: 5,
    fontFamily: 'Inter-Regular',
  },
  codeExpires: {
    fontSize: fontSize.md,
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
  codeExpiresWrapper: {
    marginVertical: spacings.md,
    marginBottom: 18,
  },
  inputField: {
    paddingLeft: spacings.md,
    color: colors.black,
    borderRadius: spacings.sm,
    fontSize: fontSize.md,
    // ADDED
    ...(isIOS ? {padding: 15} : {}),
  },
  passwordWrapper: {
    width: '100%',
    justifyContent: 'center',
    position: 'relative',
  },
  togglePassword: {
    position: 'absolute',
    right: 0,
    height: spacings.xxxl,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
  },
  togglePasswordText: {
    fontSize: fontSize.sm,
    color: colors.black,
  },
  codeNotReceived: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
    fontFamily: 'Inter-Medium',
  },
  resendCodeWrapper: {
    paddingHorizontal: spacings.sm,
    // marginVertical: spacings.md,
  },
  resendCode: {
    fontSize: fontSize.md,
    color: colors.primary,
    fontWeight: 'bold',
  },
  forgotPasswordSection: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  loginButtonText: {
    paddingVertical: spacings.md,
    paddingLeft: spacings.lg,
    fontSize: fontSize.sm,
    color: colors.black,
    textDecorationLine: 'underline',
  },
  loginButtonSection: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginVertical: spacings.sm,
    width: '100%',
  },
  errorText: {
    fontSize: 11,
    color: colors.warning,
    width: '100%',
    position: 'absolute',
  },
  errorWrapper: {
    width: '100%',
    marginTop: 5,
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
    color: colors.black,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalView: {
    margin: 20,
    backgroundColor: colors.white,
    padding: spacings.xxxl,
    borderRadius: 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  buttonClose: {
    backgroundColor: colors.black,
    borderRadius: spacings.xxxl,
  },
  textStyle: {
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  closeIcon: {
    padding: 10,
    position: 'absolute',
    left: 0,
  },
  infoText: {
    fontSize: 12,
    color: colors.darkGrey,
    textAlign: 'left',
  },
  infoSubtext: {
    fontWeight: '700',
    textDecorationLine: 'underline',
  },
});
