import {View, Text} from 'react-native';
import React from 'react';

import locale from '../../assets/locale.json';
import colors from '../../assets/colors';
import Lottie from '../Lottie/index';

export default function DefaultEmptyComponent({
  containerStyle = {},
  lottieStyle = {},
  textStyle = {},
  text = locale.LooksLikeItsEmpty,
}) {
  return (
    <View style={containerStyle}>
      <Lottie
        name="empty"
        style={[{width: 150, height: 150, alignSelf: 'center'}, lottieStyle]}
        loop={false}
        delayInMs={100}
      />
      <Text
        style={[
          {
            fontSize: 16,
            fontFamily: 'Inter-Medium',
            color: colors.darkBadge,
          },
          textStyle,
        ]}>
        {text}
      </Text>
    </View>
  );
}
