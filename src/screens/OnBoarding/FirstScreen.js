import React from 'react';
import {StyleSheet, Text, View, Image, Dimensions} from 'react-native';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const Index = ({item}) => {
  return (
    <View style={styles.container}>
      <View style={styles.section1}>
        <Image style={styles.auth8Logo} source={item?.image} />
      </View>
      <View style={styles.section2}>
        <View style={styles.defunkdWrapper}>
          <Text style={styles.subtitle}>{item.title}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.black,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  auth8Logo: {
    height: WIDTH * 0.7,
    width: WIDTH * 0.7,
    resizeMode: 'contain',
  },
  defunkdLogo: {
    height: 30,
    width: WIDTH / 2,
    resizeMode: 'contain',
  },
  section1: {
    width: WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 5 / 10,
    padding: spacings.lg,
  },
  section2: {
    width: WIDTH,
    alignItems: 'center',
    flex: 7 / 10,
    padding: 15,
  },
  defunkdWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.xxl,
  },
  button: {
    backgroundColor: colors.primary,
    width: '100%',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: fontSize.md,
    fontWeight: 'bold',
    color: colors.white,
  },
  loginQuestion: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacings.md,
    height: 50,
  },
  question: {
    color: colors.white,
  },
  login: {
    color: colors.primary,
    textDecorationLine: 'underline',
    paddingHorizontal: spacings.md,
    textAlignVertical: 'center',
    height: 50,
  },
  subtitle: {
    textAlign: 'center',
    color: colors.white,
    fontFamily: 'Inter-Medium',
    fontSize: 24,
    lineHeight: 30,
  },
});

export default Index;
