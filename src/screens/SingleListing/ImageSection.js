import React, {useCallback, useMemo} from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import {styles} from './styles';
import {fontSize} from '../../assets/font';
import colors from '../../assets/colors';
import {
  singleListingScreen,
  IMAGE_TYPES,
  icons,
  images as imagesString,
  screens,
} from '../../assets/strings';
import Icon from '../../components/Icon';
import FastImage from '../../components/FastImage';
import ImageViewer from '../../components/ImageViewer/index';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ToolTip from '../../components/Tooltip';
import locale from '../../assets/locale.json';

const isIOS = Platform.OS === 'ios';

const ImageSection = ({data}) => {
  const navigation = useNavigation();
  const [imageViewerVisible, setImageViewerVisible] = React.useState(false);

  const image = data?.getPost?.data?.images?.front;
  const images = data?.getPost?.data?.images;

  const decision = React.useMemo(
    () => data?.getPost.data?.authentication_result?.expert_result?.decision,
    [data?.getPost.data?.authentication_result?.expert_result?.decision],
  );

  const isAuthenticationPending = React.useMemo(
    () => data?.getPost?.data?.authentication_status === 'PENDING',
    [data?.getPost?.data?.authentication_status === 'PENDING'],
  );

  const galleryImages = useMemo(() => {
    let objs = [];
    if (!(images == null) && typeof images === 'object') {
      for (const [key, value] of Object.entries(images)) {
        if (key in IMAGE_TYPES && value?.length > 0) {
          objs.push({image: value, key: key});
        }
      }
    }
    return objs;
  }, [images]);

  const handleDecision = useMemo(() => {
    if (isAuthenticationPending) {
      return 'PENDING';
    }
    switch (decision) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDETERMINED':
        return 'UNDETERMINED';
      default:
        return 'UNDECIDED';
    }
  }, [decision, isAuthenticationPending]);

  const handleColor = useCallback(() => {
    switch (decision) {
      case 'REAL':
        return {
          backgroundColor: colors.greenBadge,
        };
      case 'FAKE':
        return {
          backgroundColor: colors.redBadge,
        };

      default:
        return {
          backgroundColor: colors.darkBadge,
        };
    }
  }, [decision]);

  const handleImageViewing = React.useCallback(() => {
    setImageViewerVisible(true);
  }, []);

  return (
    <>
      <View style={styles.mainImageContainer}>
        <TouchAwareButton
          disableLongPressAnimation
          disableTapAnimation
          onPress={handleImageViewing}>
          <FastImage
            source={image ? {uri: image} : imagesString.PLACEHOLDER}
            style={styles.mainImage}
          />
        </TouchAwareButton>

        {!isAuthenticationPending ? (
          <View style={styles.topImageSection}>
            <TouchableOpacity
              style={[styles.button, {margin: 20}]}
              onPress={() =>
                navigation.navigate(screens.POST_GALLERY, {
                  postId: data?.getPost?.data?.id,
                })
              }>
              <Text style={styles.buttonText}>
                {singleListingScreen.VIEW_GALLERY}({galleryImages?.length ?? 0})
              </Text>
            </TouchableOpacity>
          </View>
        ) : null}
        <View style={[styles.bottomImageSection, {padding: 20}]}>
          <View style={[{flexDirection: 'row', alignItems: 'center'}]}>
            <View style={[styles.button, handleColor()]}>
              <Icon name="person_icon" tint={colors.white} size={fontSize.md} />
              <Text style={[styles.buttonText, {marginLeft: 5}]}>
                {handleDecision}
              </Text>
            </View>
            {isAuthenticationPending ? (
              <ToolTip
                tint={colors.white}
                containerStyle={{
                  marginLeft: 5,
                  borderRadius: 25,
                  padding: 5,
                  ...(isIOS ? {} : {backgroundColor: 'rgba(0, 0, 0, 0.3)'}),
                }}
                enableSheetContent
                title={locale.Pending}
                description={
                  "This submission has yet to be approved for visibility in our feed. You'll receive a notification when it has."
                }
                size={20}
              />
            ) : null}
          </View>
          {!isAuthenticationPending ? (
            <TouchableOpacity
              style={styles.button}
              onPress={() =>
                navigation.navigate(screens.COMMENTS, {
                  postId: data?.getPost?.data?.id,
                })
              }>
              <Icon
                name={icons.COMMENT_ICON}
                tint={colors.white}
                size={fontSize.md}
                style={{marginTop: 4}}
              />
              <Text style={[styles.buttonText, {marginHorizontal: 5}]}>
                {data?.getPost?.data?.number_of_post_comments ?? 0}
              </Text>

              <Text style={[styles.buttonText]}>Expert Comments</Text>
            </TouchableOpacity>
          ) : null}
        </View>
        <ImageViewer
          visible={imageViewerVisible}
          setVisible={setImageViewerVisible}
          images={galleryImages?.map(img => ({url: img?.image, id: img?.key}))}
          keyExtractor={img => img?.key}
        />
      </View>
    </>
  );
};

export default ImageSection;
