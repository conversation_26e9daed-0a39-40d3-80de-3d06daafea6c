const imagePlaceholders = Object.freeze({
  front: require('./Images/authentication-image-placeholders/front.png'),
  closeup_front: require('./Images/authentication-image-placeholders/closeup_front.png'),
  back: require('./Images/authentication-image-placeholders/back.png'),
  closeup_back: require('./Images/authentication-image-placeholders/closeup_back.png'),
  front_tag: require('./Images/authentication-image-placeholders/front_tag.png'),
  back_tag: require('./Images/authentication-image-placeholders/back_tag.png'),
  lower_hem_stitching: require('./Images/authentication-image-placeholders/lower_hem_stitching.png'),
  arm_hem_stitching: require('./Images/authentication-image-placeholders/arm_hem_stitching.png'),
  copyright: require('./Images/authentication-image-placeholders/copyright.png'),
  extra: require('./Images/authentication-image-placeholders/extra.png'),
});

export const communityVerificationData = {
  header: 'Upload photos or take pictures for Community Votes:',
  required: [
    {
      id: 1,
      name: 'Front of Shirt',
      value: 'front_image',
      placeholder: imagePlaceholders.front,
    },
    {
      id: 2,
      name: 'Front Close Up',
      value: 'closeup_front_image',
      placeholder: imagePlaceholders.closeup_front,
    },
    {
      id: 4,
      name: 'Tag or Neck Hem',
      value: 'front_tag_image',
      placeholder: imagePlaceholders.front_tag,
    },
  ],

  optional: [
    {
      id: 3,
      name: 'Back of Shirt',
      value: 'back_image',
      placeholder: imagePlaceholders.back,
    },
    {
      id: 7,
      name: 'Back Close Up',
      value: 'closeup_back_image',
      placeholder: imagePlaceholders.closeup_back,
    },
    {
      id: 5,
      name: 'Back of Tag',
      value: 'back_tag_image',
      placeholder: imagePlaceholders.back_tag,
    },
    {
      id: 6,
      name: 'Close Up Copyright',
      value: 'copyright_image',
      placeholder: imagePlaceholders.copyright,
    },

    {
      id: 8,
      name: 'Lower Hem Stitch',
      value: 'lower_hem_stitching_image',
      placeholder: imagePlaceholders.lower_hem_stitching,
    },
    {
      id: 9,
      name: 'Arm Hem Stitch',
      value: 'arm_hem_stitching_image',
      placeholder: imagePlaceholders.arm_hem_stitching,
    },
    {
      id: 10,
      name: 'Additional Info',
      value: 'extra',
      placeholder: imagePlaceholders.extra,
    },
  ],
};

export const ExpertFeedbackVerificationData = {
  header: 'Upload photos or take pictures for Expert Feedback:',
  required: [
    {
      id: 1,
      name: 'Front of Shirt',
      value: 'front_image',
      placeholder: imagePlaceholders.front,
    },
    {
      id: 2,
      name: 'Front Close Up',
      value: 'closeup_front_image',
      placeholder: imagePlaceholders.closeup_front,
    },

    {
      id: 4,
      name: 'Tag or Neck Hem',
      value: 'front_tag_image',
      placeholder: imagePlaceholders.front_tag,
    },
  ],

  optional: [
    {
      id: 3,
      name: 'Back of Shirt',
      value: 'back_image',
      placeholder: imagePlaceholders.back,
    },
    {
      id: 7,
      name: 'Back Close Up',
      value: 'closeup_back_image',
      placeholder: imagePlaceholders.closeup_back,
    },
    {
      id: 5,
      name: 'Back of Tag',
      value: 'back_tag_image',
      placeholder: imagePlaceholders.back_tag,
    },
    {
      id: 6,
      name: 'Close Up Copyright',
      value: 'copyright_image',
      placeholder: imagePlaceholders.copyright,
    },

    {
      id: 8,
      name: 'Lower Hem Stitch',
      value: 'lower_hem_stitching_image',
      placeholder: imagePlaceholders.lower_hem_stitching,
    },
    {
      id: 9,
      name: 'Arm Hem Stitch',
      value: 'arm_hem_stitching_image',
      placeholder: imagePlaceholders.arm_hem_stitching,
    },
    {
      id: 10,
      name: 'Additional Info',
      value: 'extra',
      placeholder: imagePlaceholders.extra,
    },
  ],
};

export const expertVerificationData = {
  header: 'Take photos of your t-shirt for Expert Certification:',
  required: [
    {
      id: 1,
      name: 'Front of Shirt',
      value: 'front_image',
      placeholder: imagePlaceholders.front,
    },
    {
      id: 2,
      name: 'Front Close Up',
      value: 'closeup_front_image',
      placeholder: imagePlaceholders.closeup_front,
    },
    {
      id: 5,
      name: 'Tag or Neck Hem',
      value: 'front_tag_image',
      placeholder: imagePlaceholders.front_tag,
    },
    {
      id: 6,
      name: 'Lower Hem Stitch',
      value: 'lower_hem_stitching_image',
      placeholder: imagePlaceholders.lower_hem_stitching,
    },
    {
      id: 7,
      name: 'Arm Hem Stitch',
      value: 'arm_hem_stitching_image',
      placeholder: imagePlaceholders.arm_hem_stitching,
    },
  ],
  optional: [
    {
      id: 3,
      name: 'Back of Shirt',
      value: 'back_image',
      placeholder: imagePlaceholders.back,
    },
    {
      id: 4,
      name: 'Back Close Up',
      value: 'closeup_back_image',
      placeholder: imagePlaceholders.closeup_back,
    },
    {
      id: 8,
      name: 'Back of Tag',
      value: 'back_tag_image',
      placeholder: imagePlaceholders.back_tag,
    },
    {
      id: 9,
      name: 'Close Up Copyright',
      value: 'copyright_image',
      placeholder: imagePlaceholders.copyright,
    },
    {
      id: 10,
      name: 'Additional Info',
      value: 'extra',
      placeholder: imagePlaceholders.extra,
    },
  ],
};
export const expertVerificationWithNFTData = {
  header: 'Take photos of your t-shirt for Expert NFT Certification:',
  required: [
    {
      id: 1,
      name: 'Front of Shirt',
      value: 'front_image',
      placeholder: imagePlaceholders.front,
    },
    {
      id: 2,
      name: 'Front Close Up',
      value: 'closeup_front_image',
      placeholder: imagePlaceholders.closeup_front,
    },
    {
      id: 5,
      name: 'Tag or Neck Hem',
      value: 'front_tag_image',
      placeholder: imagePlaceholders.front_tag,
    },
    {
      id: 6,
      name: 'Lower Hem Stitch',
      value: 'lower_hem_stitching_image',
      placeholder: imagePlaceholders.lower_hem_stitching,
    },
    {
      id: 7,
      name: 'Arm Hem Stitch',
      value: 'arm_hem_stitching_image',
      placeholder: imagePlaceholders.arm_hem_stitching,
    },
  ],
  optional: [
    {
      id: 3,
      name: 'Back of Shirt',
      value: 'back_image',
      placeholder: imagePlaceholders.back,
    },
    {
      id: 4,
      name: 'Back Close Up',
      value: 'closeup_back_image',
      placeholder: imagePlaceholders.closeup_back,
    },
    {
      id: 8,
      name: 'Back of Tag',
      value: 'back_tag_image',
      placeholder: imagePlaceholders.back_tag,
    },
    {
      id: 9,
      name: 'Close Up Copyright',
      value: 'copyright_image',
      placeholder: imagePlaceholders.copyright,
    },
    {
      id: 10,
      name: 'Additional Info',
      value: 'extra',
      placeholder: imagePlaceholders.extra,
    },
  ],
};

export const tagVerificationData = {
  header:
    'Upload photos or take pictures with your phone to contribute vintage t-shirt tags to our growing database:',
  required: [
    {
      id: 1,
      name: 'Tag or Neck Hem',
      value: 'front_tag_image',
      placeholder: imagePlaceholders.front_tag,
    },
    {
      id: 2,
      name: 'Back of Tag',
      value: 'back_tag_image',
      placeholder: imagePlaceholders.back_tag,
    },
  ],
};
