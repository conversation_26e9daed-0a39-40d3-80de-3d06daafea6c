/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const NotificationsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.5}
          marginBottom={30}
          marginTop={10}
          borderRadius={4}
          height={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            borderRadius={4}
            height={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.2}
            borderRadius={10}
            height={20}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            borderRadius={4}
            height={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.2}
            borderRadius={10}
            height={20}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            borderRadius={4}
            height={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.2}
            borderRadius={10}
            height={20}
          />
        </SkeletonPlaceholder.Item>

        <SkeletonPlaceholder.Item
          width={WIDTH * 0.5}
          marginBottom={30}
          marginTop={30}
          borderRadius={4}
          height={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            borderRadius={4}
            height={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.2}
            borderRadius={10}
            height={20}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            borderRadius={4}
            height={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.2}
            borderRadius={10}
            height={20}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            borderRadius={4}
            height={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.2}
            borderRadius={10}
            height={20}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default NotificationsLoader;
