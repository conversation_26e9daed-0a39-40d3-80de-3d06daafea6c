import React from 'react';
import {StyleSheet, Text, View, FlatList, Dimensions} from 'react-native';
import {useQuery} from '@apollo/client';

import AllCategoriesLoader from '../../components/Loaders/AllCategoriesLoader';
import {GET_CATEGORIES} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {screens, images} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import QueryManager from '../../components/QueryManager';
import locale from '../../assets/locale.json';
import RefreshControl from '../../components/RefreshControl';
import FastImage from '../../components/FastImage';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {useInteractionManager} from '../../tools/hooks';

const {width} = Dimensions.get('window');
const WIDTH = width - spacings.lg;
const CARD_WIDTH = WIDTH / 2.2;

const AllCategories = ({navigation}) => {
  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const [refreshing, setRefreshing] = React.useState(false);

  const {data, loading, error, refetch} = useQuery(GET_CATEGORIES);

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('tabPress', e => {
      try {
        refetch?.();
      } catch (error) {}
    });

    return unsubscribe;
  }, [navigation, refetch]);

  const handleCategoryClick = React.useCallback(
    item =>
      navigation.push(screens.SINGLE_CATEGORY_SCREEN, {
        params: item,
      }),
    [navigation.push],
  );

  const renderItem = React.useCallback(
    ({item}) => (
      <TouchAwareButton
        style={styles.card}
        onPress={handleCategoryClick.bind(null, item)}>
        <TouchAwareButton.View style={styles.imageWrapper}>
          <FastImage
            source={
              item?.image_url?.length ? {uri: item?.image_url} : images.logo1
            }
            style={styles.imageStyle}
          />
        </TouchAwareButton.View>
        <Text
          style={[
            styles.titleStyle,
            {fontFamily: 'Inter-Medium', width: CARD_WIDTH - 10},
          ]}
          ellipsizeMode="clip"
          numberOfLines={2}>
          {item.name}
        </Text>
      </TouchAwareButton>
    ),
    [handleCategoryClick],
  );
  const keyExtractor = React.useCallback(item => item.id.toString(), []);

  return (
    <View style={styles.container}>
      <QueryManager
        {...{
          data: !(data == null) && isInteractionsComplete,
          error,
          refetch,
          loading: loading || !isInteractionsComplete,
        }}>
        <QueryManager.Data>
          <FlatList
            data={data?.getCategories?.data}
            renderItem={renderItem}
            numColumns={2}
            columnWrapperStyle={styles.contentContainer}
            keyExtractor={keyExtractor}
            refreshControl={<RefreshControl {...{onRefresh, refreshing}} />}
            initialNumToRender={8}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <AllCategoriesLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale['SomethingWentWrong.']}
        />
      </QueryManager>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingTop: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  imageWrapper: {
    height: CARD_WIDTH / 1.8,
    width: CARD_WIDTH,
    backgroundColor: colors.lightGrey,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderRadius: 2,
    borderColor: colors.lightGrey,
    marginBottom: spacings.sm,
  },
  imageStyle: {
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
    backgroundColor: 'black',
    borderRadius: 5,
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  card: {
    marginBottom: spacings.lg,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});

export default AllCategories;
