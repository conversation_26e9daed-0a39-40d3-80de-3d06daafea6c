import React, {useCallback} from 'react';
import {StyleSheet, FlatList, Text, View} from 'react-native';
import {useQuery} from '@apollo/react-hooks';
import {useFocusEffect} from '@react-navigation/native';

import VotesLoader from '../../components/Loaders/VotesLoader';
import {GET_POST_VOTES} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {screens} from '../../assets/strings';
import ProfileImage from '../../components/ProfileImage';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import RefreshControl from '../../components/RefreshControl';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import {useWindowDimensions} from '../../tools/hooks';

const LIMIT = 100;

const Votes = ({route, navigation}) => {
  const {postId} = route?.params;
  const scrollRef = React.useRef();

  const {
    window: {height},
  } = useWindowDimensions();

  const [refreshing, setRefreshing] = React.useState(false);

  const {data, loading, refetch} = useQuery(GET_POST_VOTES, {
    variables: {
      postId: +postId,
      limit: LIMIT,
    },
  });

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (_) {}
    }, [refetch]),
  );

  const renderItem = useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          style={styles.card}
          onPress={() => {
            navigation.push(screens.PUBLIC_PROFILES, {
              params: item?.user?.id,
            });
          }}>
          <View style={styles.rightSection}>
            <ProfileImage url={item?.user?.image} size={45} />
            <Text style={styles.name}>{item?.user?.username}</Text>
          </View>
          <Text
            style={[
              styles.voteSection,
              {
                color:
                  item?.decision === 'REAL' ? colors.green : colors.warning,
              },
            ]}>
            {item?.decision === 'REAL' ? 'Pass' : 'Fail'}
          </Text>
        </TouchAwareButton>
      );
    },
    [navigation.push],
  );

  return (
    <View style={styles.container}>
      <FlatList
        contentContainerStyle={{paddingTop: 10, paddingBottom: 30}}
        data={data?.getPostVotes?.data?.edges}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        initialNumToRender={10}
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
        ListFooterComponent={loading && data == null ? <VotesLoader /> : null}
        ListEmptyComponent={
          !loading ? (
            <View
              style={{
                flex: 1,
                alignItems: 'center',
                height: height / 1.2,
                justifyContent: 'center',
              }}>
              <DefaultEmptyComponent lottieStyle={{width: 100, height: 100}} />
            </View>
          ) : null
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  card: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageStyles: {
    height: 50,
    width: 50,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: colors.lightGrey,
  },
  voteSection: {
    fontSize: 13,
    backgroundColor: '#F2F2F2',
    padding: 5,
    paddingHorizontal: 10,
    borderRadius: 4,
    fontFamily: 'Inter-Medium',
  },
  name: {
    marginLeft: spacings.md,
    fontSize: 16,
    color: colors.black,
    fontFamily: 'Inter-SemiBold',
  },
});

export default Votes;
