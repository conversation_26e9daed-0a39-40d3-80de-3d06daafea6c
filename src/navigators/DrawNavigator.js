import React, {useCallback} from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  TouchableOpacity,
  Text,
} from 'react-native';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {DrawerActions, useFocusEffect} from '@react-navigation/native';
import {useQuery} from '@apollo/client';
import {useNavigation} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';

import locale from '../assets/locale.json';
import {
  GET_UNREAD_CHAT_COUNT,
  GET_UNREAD_NOTIFICATION_COUNT,
} from '../apollo/queries';
import Icon from '../components/Icon';
import OurExperts from '../screens/OurExperts';
import Profile from '../screens/Profile';
import ConnectWallet from '../screens/ConnectWallet';
import Notifications from '../screens/Notifications';
import Billing from '../screens/Billing';
import HowItWorks from '../screens/HowItWorks';
import CustomDrawerContent from '../screens/CustomDrawerContent';
import {screens, icons, navLabels} from '../assets/strings';
import TabNavigator from './TabNavigator';
import colors from '../assets/colors';
import {fontSize} from '../assets/font';
import {spacings} from '../assets/spacing';
import Header from '../components/Header';
import {useCredential} from '../tools/hooks/useUser';
import ContactUs from '../screens/ContactUs';
import FAQs from '../screens/FAQs';

const Draw = createDrawerNavigator();

const DrawNavigator = ({navigation}) => {
  const credential = useCredential();

  const isExpert = React.useMemo(() => credential?.role_id == 3, [
    credential?.role_id,
  ]);

  const handleDrawerToggle = () => {
    navigation.dispatch(DrawerActions.toggleDrawer());
  };

  return (
    <Draw.Navigator
      screenOptions={{
        drawerType: 'slide',
        drawerStyle: {
          backgroundColor: colors.black,
          width: '100%',
        },
        headerStyle: {
          backgroundColor: colors.black,
        },
        swipeEnabled: false,
        drawerActiveBackgroundColor: colors.dark,
        drawerActiveTintColor: colors.white,
        drawerInactiveBackgroundColor: colors.black,
        drawerInactiveTintColor: colors.grey,
        drawerLabelStyle: styles.drawTextStyles,
        header: ({route}) => {
          const routeName = route?.name;
          return routeName === screens.TAB_NAVIGATOR ? (
            <SafeAreaView
              edges={['top']}
              style={{
                flexDirection: 'row',
                paddingVertical: 10,
                alignItems: 'center',
              }}>
              <Pressable
                style={styles.leftIconWrapper}
                onPress={handleDrawerToggle}>
                <Icon
                  name={icons.DRAW_MENU}
                  size={fontSize.xxl}
                  tint={colors.white}
                />
              </Pressable>
              <Pressable
                onPress={() => navigation.navigate(screens.SEARCH_SCREEN)}
                style={[styles.searchBarButton, {flex: 1, marginLeft: 15}]}>
                <Icon
                  name={icons.SEARCH_ICON}
                  size={fontSize.xl}
                  tint={colors.darkGrey}
                />
                <Text style={styles.searchButtonText}>{locale.Search}</Text>
              </Pressable>
              <View style={[styles.iconWrapper, {marginLeft: 'auto'}]}>
                {/* {isExpert ? null : <UnreadNotifications />} */}
                <UnreadNotifications />
                <UnreadMessage />
              </View>
            </SafeAreaView>
          ) : null;
        },
      }}
      drawerContent={props => <CustomDrawerContent {...props} />}>
      <Draw.Screen
        name={screens.TAB_NAVIGATOR}
        component={TabNavigator}
        options={{
          drawerLabel: navLabels.HOME_TABS,
          drawerItemStyle: [styles.itemSeparator, {display: 'none'}],
          drawerIcon: () => (
            <View style={styles.drawIconStyles}>
              <Icon name={icons.HOME_ICON} size={25} tint={'#9E9E9E'} />
            </View>
          ),
          headerTitleStyle: {fontFamily: 'Gugi-Regular'},
        }}
      />
      <Draw.Screen
        name={screens.PROFILE}
        component={Profile}
        options={{
          headerTitleStyle: {fontFamily: 'Gugi-Regular'},
          drawerLabel: navLabels.PROFILE,
          drawerIcon: () => (
            <View style={styles.drawIconStyles}>
              <Icon name={icons.PERSON_ICON} size={25} tint={'#9E9E9E'} />
            </View>
          ),
          header: () => (
            <Header left="back" title={locale.Profile} enableTopSafeAreaInset />
          ),
        }}
      />
      <Draw.Screen
            name={screens.NOTIFICATIONS}
            component={Notifications}
            options={{
              headerTitleStyle: {fontFamily: 'Gugi-Regular'},
              drawerLabel: navLabels.NOTIFICATIONS,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon
                    name={icons.NOTIFICATIONS_READ}
                    size={25}
                    tint={'#9E9E9E'}
                  />
                </View>
              ),
              header: () => (
                <Header
                  left="back"
                  title={locale.Notifications}
                  enableTopSafeAreaInset
                />
              ),
            }}
          />
      {!isExpert ? (
        <>
          {/* <Draw.Screen
            name={screens.NOTIFICATIONS}
            component={Notifications}
            options={{
              headerTitleStyle: {fontFamily: 'Gugi-Regular'},
              drawerLabel: navLabels.NOTIFICATIONS,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon
                    name={icons.NOTIFICATIONS_READ}
                    size={25}
                    tint={'#9E9E9E'}
                  />
                </View>
              ),
              header: () => (
                <Header
                  left="back"
                  title={locale.Notifications}
                  enableTopSafeAreaInset
                />
              ),
            }}
          /> */}
          <Draw.Screen
            name={screens.BILLING}
            component={Billing}
            options={{
              headerTitleStyle: {fontFamily: 'Gugi-Regular'},
              gestureEnabled: false,
              drawerLabel: navLabels.BILLING,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon name={icons.CREDIT_CARD} size={25} tint={'#9E9E9E'} />
                </View>
              ),
              header: () => (
                <Header
                  left="back"
                  title={locale.Billing}
                  enableTopSafeAreaInset
                />
              ),
            }}
          />
          <Draw.Screen
            name={screens.CONNECT_WALLET}
            component={ConnectWallet}
            options={{
              headerTitleStyle: {fontFamily: 'Gugi-Regular'},
              gestureEnabled: false,
              drawerLabel: navLabels.CONNECT_WALLET,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon name={icons.WALLET} size={25} tint={'#9E9E9E'} />
                </View>
              ),
              header: () => (
                <Header
                  left="back"
                  title={locale.ConnectWallet}
                  enableTopSafeAreaInset
                />
              ),
            }}
          />
          <Draw.Screen
            name={screens.OUR_EXPERTS}
            component={OurExperts}
            options={{
              headerTitleStyle: {fontFamily: 'Gugi-Regular'},
              drawerItemStyle: styles.itemSeparator,
              drawerLabel: navLabels.OUR_EXPERTS,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon name={icons.OUR_EXPERTS} size={25} tint={'#9E9E9E'} />
                </View>
              ),
              header: () => (
                <Header
                  left="back"
                  title={locale.OurExperts}
                  enableTopSafeAreaInset
                />
              ),
            }}
          />
          <Draw.Screen
            name={screens.FREQUENTLY_ASKED_QUESTIONS}
            component={FAQs}
            options={{
              headerTitleStyle: {
                fontFamily: 'Gugi-Regular',
              },
              drawerLabelStyle: [
                styles.drawTextStyles,
                {textTransform: 'none'},
              ],
              drawerLabel: locale.FAQs,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon name="faq" size={25} tint={'#9E9E9E'} />
                </View>
              ),
              header: () => {
                return (
                  <Header
                    left="back"
                    title={locale.FAQs}
                    enableTopSafeAreaInset
                  />
                );
              },
            }}
          />
          <Draw.Screen
            name={screens.HOWITWORKS}
            component={HowItWorks}
            options={{
              headerTitleStyle: {fontFamily: 'Gugi-Regular'},
              drawerLabel: navLabels.HOWITWORKS,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon
                    name={icons.QUESTIONMARK_CIRCLE}
                    size={25}
                    tint={'#9E9E9E'}
                  />
                </View>
              ),
              header: () => (
                <Header
                  left="back"
                  title={locale.HowItWorks}
                  enableTopSafeAreaInset
                />
              ),
            }}
          />
          <Draw.Screen
            name={screens.CONTACT_US}
            component={ContactUs}
            options={{
              headerTitleStyle: {fontFamily: 'Gugi-Regular'},
              drawerLabel: navLabels.CONTACT_US,
              unmountOnBlur: true,
              drawerIcon: () => (
                <View style={styles.drawIconStyles}>
                  <Icon name="contactUs" size={22} tint="#9E9E9E" />
                </View>
              ),
              header: () => (
                <Header
                  left="back"
                  title={locale.ContactUs}
                  enableTopSafeAreaInset
                />
              ),
            }}
          />
        </>
      ) : null}
    </Draw.Navigator>
  );
};

function UnreadNotifications() {
  const navigation = useNavigation();

  const {
    data: notificationCount,
    refetch,
    loading: notificationLoading,
    error: notificationError,
    startPolling,
    stopPolling,
  } = useQuery(GET_UNREAD_NOTIFICATION_COUNT);

  useFocusEffect(
    useCallback(() => {
      try {
        refetch?.();
      } catch (error) {}
    }, [refetch]),
  );

  React.useEffect(() => {
    startPolling?.(5000);

    return () => {
      stopPolling?.();
    };
  }, [startPolling, stopPolling]);

  return (
    <TouchableOpacity
      style={{marginLeft: 15}}
      onPress={() => navigation.navigate(screens.NOTIFICATION_MESSAGES)}>
      {notificationCount?.getUnreadNotificationsCount?.data < 1 ||
      notificationLoading ||
      notificationError ? (
        <Icon
          name={icons.NOTIFICATIONS_READ}
          tint={colors.white}
          size={fontSize.xxl}
        />
      ) : (
        <Icon
          name={icons.NOTIFICATIONS_UNREAD}
          tint={colors.white}
          size={fontSize.xxl}
        />
      )}
    </TouchableOpacity>
  );
}
function UnreadMessage() {
  const navigation = useNavigation();

  const {data, refetch, loading, error, startPolling, stopPolling} = useQuery(
    GET_UNREAD_CHAT_COUNT,
  );

  useFocusEffect(
    useCallback(() => {
      try {
        refetch?.();
      } catch (error) {}
    }, [refetch]),
  );

  React.useEffect(() => {
    startPolling?.(5000);

    return () => {
      stopPolling?.();
    };
  }, [startPolling, stopPolling]);

  return (
    <TouchableOpacity
      style={[styles.iconStyles, {marginRight: spacings.md}]}
      onPress={() => navigation.navigate(screens.MESSENGER)}>
      {data?.getUnreadChatCount?.data < 1 || loading || error ? (
        <Icon
          name={icons.MESSENGER_READ}
          tint={colors.white}
          size={fontSize.xxl}
        />
      ) : (
        <Icon
          name={icons.MESSENGER_UNREAD}
          tint={colors.white}
          size={fontSize.xxl}
        />
      )}
    </TouchableOpacity>
  );
}
const styles = StyleSheet.create({
  drawIconStyles: {
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawTextStyles: {
    fontFamily: 'Inter-SemiBold',
    fontSize: fontSize.lg,
    textTransform: 'capitalize',
  },
  iconWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconStyles: {
    padding: spacings.md,
    marginLeft: spacings.md,
  },
  itemSeparator: {
    borderTopColor: colors.darkGrey,
    borderTopWidth: 1,
    paddingTop: 10,
  },
  searchBarButton: {
    paddingVertical: 9,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.veryLightGrey,
    backgroundColor: colors.secondaryGrey,
    color: colors.white,
    borderRadius: spacings.lg,
    paddingHorizontal: 15,
  },
  searchButtonText: {
    color: colors.darkGrey,
    marginLeft: spacings.sm,
    marginRight: spacings.lg,
    letterSpacing: 1,
  },
  leftIconWrapper: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
    paddingRight: 5,
  },
});

export default DrawNavigator;
