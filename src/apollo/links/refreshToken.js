import {Observable, ApolloLink} from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {persistent, screens, errorCodes} from '../../assets/strings';
import * as api from '../../tools/api';
import NavigationService from '../../navigators/NavigationService';

const logoutAndResetNavigation = async () => {
  try {
    const canForceLogout =
      ((await AsyncStorage.getItem(persistent.CAN_FORCE_LOGOUT)) ?? '1') == '1';

    if (!canForceLogout) {
      return;
    }
    NavigationService.navigate(screens.FORCE_LOGOUT);
    return;
  } catch (_) {}
};

const refreshToken = new ApolloLink((operation, forward) => {
  return new Observable(observer => {
    let handler = forward(operation).subscribe({
      next: (...args) => {
        const {errors} = args[0];
        if (
          errors?.some(error => {
            return (
              error?.extensions?.code === errorCodes.ACCOUNT_DOES_NOT_EXISTS
            );
          })
        ) {
          logoutAndResetNavigation();
        }
        return observer.next.apply(observer, args);
      },
      error: async networkError => {
        const {result} = networkError;
        const graphQLErrors = result?.errors;
        if (graphQLErrors) {
          if (
            graphQLErrors.some(error => {
              return error?.extensions?.code === errorCodes.INVALID_TOKEN;
            })
          ) {
            let newTokens;
            try {
              const token = await AsyncStorage.getItem(
                persistent.REFRESH_TOKEN,
              );
              if (!token) {
                logoutAndResetNavigation();
                return;
              }

              const response = await api.REFRESH_TOKEN(token);
              newTokens = response?.data?.data;

              if (!newTokens?.access_token || !newTokens?.refresh_token) {
                logoutAndResetNavigation();
                return;
              }
              await AsyncStorage.setItem(
                persistent.ACCESS_TOKEN,
                newTokens.access_token,
              );
              await AsyncStorage.setItem(
                persistent.REFRESH_TOKEN,
                newTokens.refresh_token,
              );
            } catch (error) {
              const isInvalid =
                error?.response?.data?.code ===
                errorCodes.INVALID_REFRESH_TOKEN;
              // Logout
              try {
                if (isInvalid) {
                  logoutAndResetNavigation();
                }
              } catch (__) {}

              observer.error(networkError);
              return;
            }

            // update context
            operation.setContext({
              headers: {
                authorization: `Bearer ${newTokens.access_token}`,
              },
            });

            // retry request
            forward(operation).subscribe(observer);
          }
        }
        observer.error(networkError);
      },
      complete: observer.complete.bind(observer),
    });

    return () => {
      if (handler) {
        handler.unsubscribe();
      }
    };
  });
});

export default refreshToken;
