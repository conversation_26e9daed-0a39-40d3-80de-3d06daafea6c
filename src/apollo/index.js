import React from 'react';
import {Platform} from 'react-native';
import {ApolloClient, InMemoryCache} from '@apollo/client';
import {AsyncStorageWrapper, CachePersistor} from 'apollo3-cache-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

import links from './links';
import {createPersistLink} from './persistence';
import typePolicies from './typePolicies';

const defaultOptions = {restoreCache: true};

export const useApolloClient = (options = defaultOptions) => {
  options = {...defaultOptions, ...options};
  const [client, setClient] = React.useState();

  const [persistor, setPersistor] = React.useState(null);

  React.useEffect(() => {
    (async function () {
      const cache = new InMemoryCache({
        typePolicies,
      });

      const cachePersistor = new CachePersistor({
        cache,
        storage: new AsyncStorageWrapper(AsyncStorage),
        maxSize: Platform.OS === 'android' ? 2097152 : false, // 2MB Limitation per key of AsyncStorage in Android
        debug: __DEV__,
      });
      let persistLink;

      try {
        if (options.restoreCache) {
          await cachePersistor.restore();
        }
        setPersistor(cachePersistor);
        persistLink = createPersistLink();
      } catch (_) {
        await cachePersistor.purge();
      }

      const clientStore = new ApolloClient({
        link: persistLink == null ? links : persistLink.concat(links),
        cache,
      });
      clientStore.persistor = cachePersistor;

      setClient(clientStore);
    })();
  }, [options.restoreCache]);

  return {client, persistor};
};
