import React, {useLayoutEffect} from 'react';
import {StyleSheet, Text, Pressable, View} from 'react-native';
import {DrawerActions} from '@react-navigation/native';

import Icon from '../../components/Icon';
import {icons} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import locale from '../../assets/locale.json';
import Layout from '../../layout';

const Index = ({navigation}) => {
  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: 'How It Works',
      headerTintColor: colors.white,
      headerRight: null,
      headerLeft: () => (
        <Pressable
          style={styles.leftIconWrapper}
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}>
          <Icon
            name={icons.ARROW_LEFT}
            tint={colors.white}
            size={fontSize.xxxl}
          />
        </Pressable>
      ),
    });
  }, []);

  return (
    <Layout contentContainerStyle={styles.container}>
      <Text style={styles.textStyle}>
        {locale['Legiteem8IsTheWorldsLargestVintage...']}
      </Text>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: 20,
  },
  title: {
    color: colors.black,
    fontSize: 18,
    fontFamily: 'Inter-Medium',
  },
  textStyle: {
    color: colors.black,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  desc: {
    marginTop: spacings.md,
  },
  card: {
    paddingHorizontal: spacings.lg,
    marginVertical: spacings.lg,
  },
  leftIconWrapper: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
  },
});

export default Index;
