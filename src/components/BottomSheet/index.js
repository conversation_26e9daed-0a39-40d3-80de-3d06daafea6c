import React, {useRef} from 'react';
import {BackHand<PERSON>, Keyboard} from 'react-native';
import {
  BottomSheetView,
  BottomSheetModal,
  BottomSheetBackdrop,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import {
  useWindowDimensions,
  useIsKeyboardVisible,
} from '../../tools/hooks/index';
import colors from '../../assets/colors';
import IconButton from '../../components/Icon';

const BottomSheet = ({
  visible,
  setVisible = () => null,
  onDismiss = () => null,
  children = null,
  automaticallyAdjustHeight = false, // Prop 'key' with unique value as string needs to be passed to calculate the layout changes every time.
  backdropComponent = null,
  enableHandle = true,
  panToDismiss = true,
  responsive = true,
  snapPoints: _snapPoints = ['25%', '50%'],
  index = 1,
  modalStyle = {},
  showKeyboardDismissButton = false,
}) => {
  const bottomSheetRef = useRef(null);
  const {bottom: safeAreaBottom} = useSafeAreaInsets();

  const {window} = useWindowDimensions();

  const MAX_WIDTH = 600;
  const marginHorizontal =
    window.width > MAX_WIDTH ? (window.width - MAX_WIDTH) / 2 : 0;

  const snapPoints = automaticallyAdjustHeight
    ? ['CONTENT_HEIGHT']
    : _snapPoints;

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(snapPoints);

  React.useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.dismiss();
    }
  }, [visible]);

  const renderActualChildren = automaticallyAdjustHeight ? (
    <BottomSheetViewComponent
      handleContentLayout={handleContentLayout}
      key="actualChildren">
      {children}
    </BottomSheetViewComponent>
  ) : (
    children
  );

  const handleDismiss = React.useCallback(() => {
    setVisible(false);
    onDismiss?.();
  }, [onDismiss, setVisible]);

  const backPressed = React.useCallback(() => {
    setVisible(false);
    return true;
  }, [setVisible]);

  React.useEffect(() => {
    if (visible) {
      BackHandler.addEventListener('hardwareBackPress', backPressed);
    } else {
      BackHandler.removeEventListener('hardwareBackPress', backPressed);
    }
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', backPressed);
    };
  }, [visible, backPressed]);

  return visible ? (
    <BottomSheetModal
      style={[
        {paddingBottom: safeAreaBottom},
        responsive ? {marginHorizontal} : {},
        modalStyle,
      ]}
      name="bottomsheet"
      ref={bottomSheetRef}
      enableHandlePanningGesture
      onDismiss={handleDismiss}
      enableContentPanningGesture={panToDismiss}
      keyboardBlurBehavior="restore"
      android_keyboardInputMode="adjustResize"
      backdropComponent={props => <Backdrop {...props} />}
      {...(automaticallyAdjustHeight ? {} : {index})}
      {...(backdropComponent !== null ? {backdropComponent} : {})}
      {...(!enableHandle ? {handleComponent: null} : {})}
      {...(automaticallyAdjustHeight
        ? {
            snapPoints: animatedSnapPoints,
            handleHeight: animatedHandleHeight,
            contentHeight: animatedContentHeight,
            key: `Modal${
              automaticallyAdjustHeight ? animatedContentHeight.value : ''
            }`,
          }
        : {snapPoints: _snapPoints})}>
      {renderActualChildren}
      {visible && showKeyboardDismissButton ? (
        <KeyboardDismissOnInputFocus />
      ) : null}
    </BottomSheetModal>
  ) : null;
};

function BottomSheetViewComponent({
  handleContentLayout = () => null,
  children = null,
}) {
  const {bottom: safeAreaBottom} = useSafeAreaInsets();

  return (
    <BottomSheetView
      onLayout={handleContentLayout}
      style={{
        paddingBottom: safeAreaBottom,
      }}>
      {children}
    </BottomSheetView>
  );
}

function Backdrop({...props}) {
  const isKeyboardVisible = useIsKeyboardVisible();

  return (
    <BottomSheetBackdrop
      key="backdrop"
      {...props}
      pressBehavior={isKeyboardVisible ? 'none' : 'close'}
      appearsOnIndex={0}
      disappearsOnIndex={-1}
    />
  );
}

function KeyboardDismissOnInputFocus() {
  const isKeyboardVisible = useIsKeyboardVisible();

  const handleKeyboardDismiss = () => {
    Keyboard.dismiss();
  };
  return isKeyboardVisible ? (
    <IconButton
      clickable
      containerStyle={{
        position: 'absolute',
        right: 20,
        bottom: 10,
        marginRight: 20,
        marginVertical: 10,
        backgroundColor: colors.black,
        borderRadius: 20,
        padding: 10,
        transform: [{rotate: '90deg'}],
      }}
      name="arrow_right"
      tint={colors.white}
      size={20}
      onPress={handleKeyboardDismiss}
    />
  ) : null;
}

export default BottomSheet;
