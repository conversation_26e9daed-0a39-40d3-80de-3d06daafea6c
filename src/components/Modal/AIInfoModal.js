import React from 'react';
import {View, Text, StyleSheet} from 'react-native';

import colors from '../../assets/colors';
import locale from '../../assets/locale.json';
import CommonModal from './index';
import Lottie from '../Lottie';
import Button from '../Button';
import TextWithImage from './TextWithImage';

const popupInitialData = {
  state: false,
  isError: false,
  data: {
    title: '',
    description: '',
    list: []
  },
};

export default function AIInfoModal({
  children = null,
  popUp = popupInitialData,
  setVisible = () => null,
  showDefaultButton = true,
  defaultButtonStyle = {},
  width = null,
  blurBackground = false,
  showLottieIcon = true,
  delayInMs = 0,
}) {
  popUp = {...popupInitialData, ...popUp};

  return (
    <CommonModal
      visible={popUp?.state}
      setVisible={setVisible}
      blurBackground={blurBackground}
      delayInMs={delayInMs}
      {...(width == null ? {} : {})}
      width={280}>
      {showLottieIcon ? (
        <Lottie
          name={popUp?.isError ? 'error' : 'success'}
          style={{width: popUp?.isError ? 80 : 90}}
          loop={false}
          colorFilterPresetName="primary"
        />
      ) : null}
      <View style={{marginTop: 10}}>
        <Text style={styles.title}>{popUp?.data?.title}</Text>
        {popUp?.data.list.map((item, index) => (
          <TextWithImage
            key={index}
            textBefore={item.textBefore}
            imageUri={item.imageUri}
            textAfter={item.textAfter}
          />
        ))}
      </View>

      {showDefaultButton ? (
        <View style={styles.buttonContainer}>
          <Button
            text={locale.OK}
            onPress={setVisible}
            style={{borderRadius: 10, marginTop:20, ...defaultButtonStyle}}
          />
        </View>
      ) : null}
      {children}
    </CommonModal>
  );
}
const styles = StyleSheet.create({
  title: {
    fontSize: 22,
    textAlign: 'center',
    fontFamily: 'Gugi-Regular',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 15,
    fontFamily: 'Inter-Medium',
  },
  buttonContainer: {flexGrow: 1, justifyContent: 'flex-end'},
  button: {
    width: 150,
    minHeight: 50,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    
  },
  buttonText: {
    color: colors.white,
    fontSize: 18,
  },
});



