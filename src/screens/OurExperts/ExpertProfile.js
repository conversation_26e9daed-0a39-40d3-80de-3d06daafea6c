import React, {useCallback} from 'react';
import {
  StyleSheet,
  FlatList,
  Dimensions,
  Text,
  View,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import ExpertsProfileLoader from '../../components/Loaders/ExpertsProfileLoader';
import {GET_EXPERT_FEED_ACTIVITY, GET_EXPERT} from '../../apollo/queries';
import UserActivityCard from '../../components/UserActivityCard';
import Header from './Header';
import {icons, screens} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import colors from '../../assets/colors';
import Icon from '../../components/Icon';
import RefreshControl from '../../components/RefreshControl';
import BottomSheet from '../../components/BottomSheet';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import locale from '../../assets/locale.json';
import {useCredential} from '../../tools/hooks/useUser';
import TouchAwareButton from '../../components/Button/TouchAwareButton';

const {height: SCREEN_HEIGHT} = Dimensions.get('screen');

const LIMIT = 100;
// Add scrollRef condition here as well. Only refetch if it is at top

const filterData = [
  {id: 1, name: 'Newest First', value: null},
  {id: 2, name: 'Voted only | Fail', value: 'FAKE'},
  {id: 3, name: 'Voted only | Pass', value: 'REAL'},
];

const ExpertProfile = ({route, navigation}) => {
  const expertId = route?.params?.params;
  const {expertUserId} = route?.params;

  const credential = useCredential();
  const scrollRef = React.useRef();

  const isMyProfile = React.useMemo(() => expertUserId == credential?.user_id, [
    expertUserId,
    credential?.user_id,
  ]);

  const [refreshing, setRefreshing] = React.useState(false);
  const [selectedFilter, setSelectedFilter] = React.useState(filterData[0]);

  const {data: profileData, loading: profileLoading, refetch} = useQuery(
    GET_EXPERT,
    {
      variables: {
        ...(!(expertUserId == null) ? {userId: expertUserId} : {id: expertId}),
      },
    },
  );

  const {data, loading, refetch: refetchActivity} = useQuery(
    GET_EXPERT_FEED_ACTIVITY,
    {
      variables: {
        userId: +expertUserId,
        ...(selectedFilter?.id == 3 || selectedFilter?.id == 2
          ? {filters: {decision: selectedFilter?.value}}
          : {}),
        limit: LIMIT,
      },
    },
  );

  const renderItem = useCallback(
    ({item}) => (
      <TouchAwareButton
        onPress={() =>
          navigation.push(screens.SINGLE_LISTING, {params: item?.post?.id})
        }>
        <UserActivityCard item={item} />
      </TouchAwareButton>
    ),
    [navigation.push],
  );

  const refetchAll = React.useCallback(
    () => Promise.all([refetch(), refetchActivity()]),
    [refetch, refetchActivity],
  );

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      setRefreshing(enableRefreshControl);
      try {
        await refetchAll();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetchAll],
  );

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetchAll?.();
        }
      } catch (_) {}
    }, [refetchAll]),
  );

  const handleSelection = item => {
    setSelectedFilter(item);
  };

  React.useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: isMyProfile ? locale.MyProfile : locale.ExpertsProfile,
    });
  }, [navigation, isMyProfile]);

  if (loading || profileLoading) {
    return <ExpertsProfileLoader />;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={data?.getExpertFeedActivity?.data?.edges}
        columnWrapperStyle={styles.contentContainer}
        renderItem={renderItem}
        keyExtractor={item => item?.id?.toString?.()}
        numColumns={2}
        ListHeaderComponent={
          <>
            <Header
              data={profileData}
              refetch={refetch}
              id={profileData?.getExpert?.data?.user?.id}
              isMyProfile={isMyProfile}
            />
            {isMyProfile ? null : (
              <Activity
                userData={profileData?.getExpert?.data?.user}
                selectedFilter={selectedFilter}
                handleSelection={handleSelection}
                isMyProfile={isMyProfile}
              />
            )}
          </>
        }
        ListEmptyComponent={
          <View style={styles.emptyView}>
            <DefaultEmptyComponent
              text={locale.UserHasNoActivityYet}
              lottieStyle={{width: 100, height: 100, alignSelf: 'center'}}
            />
          </View>
        }
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
      />
    </View>
  );
};

function Activity({
  userData = null,
  selectedFilter,
  handleSelection = () => null,
}) {
  const [sheetVisible, setSheetVisible] = React.useState(false);

  const username = userData?.username;

  const handleRowSelection = item => {
    handleSelection(item);
    setSheetVisible(false);
  };

  return (
    <>
      <View style={styles.userActivityTitleWrapper}>
        <Text style={styles.userActivityTitle} numberOfLines={1}>
          {username}'s Activity
        </Text>
        <TouchableOpacity
          style={styles.profileFilterButton}
          onPress={() => setSheetVisible(true)}>
          <Text style={styles.profileFilterButtonText}>
            {selectedFilter?.name}
          </Text>
          <Icon
            name={icons.CHEVRON_DOWN}
            size={fontSize.sm}
            tint={colors.darkGrey}
          />
        </TouchableOpacity>
      </View>
      <BottomSheet
        automaticallyAdjustHeight
        visible={sheetVisible}
        setVisible={setSheetVisible}>
        <View style={{padding: 20}}>
          <Text
            style={{
              color: colors.black,
              fontSize: fontSize.xl,
              fontWeight: 'bold',
            }}>
            Showing
          </Text>
          {filterData?.map(item => (
            <Pressable
              key={item?.id}
              style={{
                paddingVertical: spacings.md,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
              onPress={handleRowSelection.bind(null, item)}>
              <Text style={{color: colors.black, fontSize: fontSize.lg}}>
                {item?.name}
              </Text>
              <Icon
                name={
                  icons[
                    selectedFilter?.id == item?.id
                      ? 'CHECKED_RADIO'
                      : 'UNCHECKED_RADIO'
                  ]
                }
                size={20}
                tint={
                  selectedFilter?.id == item?.id ? colors.primary : colors.grey
                }
              />
            </Pressable>
          ))}
        </View>
      </BottomSheet>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  userActivityTitle: {
    color: colors.black,
    fontSize: fontSize.lg,
    fontFamily: 'Inter-Bold',
    maxWidth: '50%',
  },
  profileFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
    backgroundColor: colors.lightGrey,
    paddingVertical: spacings.md,
    borderRadius: 4,
  },
  profileFilterButtonText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginRight: spacings.md,
  },
  userActivityTitleWrapper: {
    paddingHorizontal: spacings.lg,
    paddingBottom: spacings.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default ExpertProfile;
