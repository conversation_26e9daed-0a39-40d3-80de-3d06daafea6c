import React from 'react';
import {StyleSheet} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';

import MyAuthenticationsExpert from '../MyAuthenticationsExpert';
import MyVotes from '../MyVotes';
import colors from '../../assets/colors';
import {screens, navLabels} from '../../assets/strings';
import {fontSize} from '../../assets/font';

const TopTabs = createMaterialTopTabNavigator();

const Index = () => {
  return (
    <TopTabs.Navigator
      initialRouteName={screens.MY_AUTHENTICATIONS}
      screenOptions={{
        swipeEnabled: false,
        tabBarActiveTintColor: colors.black,
        tabBarIndicatorStyle: styles.indicator,
        tabBarStyle: { ...styles.barStyle, display: 'none' }, // Hide Tab Bar

        tabBarLabelStyle: styles.tabBarLabelStyle,
      }}>
      <TopTabs.Screen
        name={screens.MY_AUTHENTICATIONS}
        component={MyAuthenticationsExpert}
        options={{
          tabBarLabel: navLabels.MY_AUTHENTICATIONS,
        }}
      />    
    </TopTabs.Navigator>
  );
};

const styles = StyleSheet.create({
  indicator: {
    backgroundColor: colors.primary,
    borderRadius: 25,
  },
  barStyle: {
    backgroundColor: colors.white,
    elevation: 1,
    borderTopWidth: 3,
    borderTopColor: colors.lightGrey,
  },
  tabBarLabelStyle: {
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
  },
});

export default Index;
