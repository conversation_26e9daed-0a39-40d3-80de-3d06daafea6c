import React from 'react';
import {View, Text, StyleSheet, TextInput} from 'react-native';
import {useMutation} from '@apollo/client';
import {useDispatch, useSelector} from 'react-redux';

import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Button from '../../components/Button';
import {IS_WALLET_ADDRESS_VALID} from '../../apollo/mutations';
import {showToast} from '../../components/Toast';
import locale from '../../assets/locale.json';
import {setCryptoAddress} from '../../redux/actions/auth';

export default function PasteWalletAddress() {
  const [walletAddress, setWalletAddress] = React.useState('');
  const dispatch = useDispatch();

  const {cryptoAddress, isCryptoAddressManual = false} = useSelector(
    state => state?.auth,
  );

  const [verifyWalletAddress, {loading}] = useMutation(IS_WALLET_ADDRESS_VALID);

  const handleWalletAddressPaste = async function (address) {
    try {
      const ethAddress = address?.trim();
      const {data: response} = await verifyWalletAddress({
        variables: {walletAddress: ethAddress},
      });

      const success = response?.isWalletAddressValid?.success;
      const message = response?.isWalletAddressValid?.message;

      if (success) {
        dispatch(
          setCryptoAddress({
            walletAddress: ethAddress,
            userData: {
              address: ethAddress,
            },
            isCryptoAddressManual: true,
          }),
        );
        setWalletAddress('');
      } else {
        if (message?.length > 0) {
          showToast({message});
        } else {
          throw new Error();
        }
      }
    } catch (_) {
      showToast({message: locale.SomethingWentWrongPleaseTryAgain});
    }
  };

  const handleCryptoLogout = React.useCallback(async () => {
    try {
      dispatch(
        setCryptoAddress({
          walletAddress: null,
          userData: null,
          isCryptoAddressManual: false,
        }),
      );
    } catch (_) {}
  }, [dispatch]);

  return cryptoAddress?.length > 0 && isCryptoAddressManual ? (
    <Button
      text="Remove Wallet"
      containerStyle={{marginTop: 'auto'}}
      style={{height: 50}}
      onPress={handleCryptoLogout}
    />
  ) : cryptoAddress?.length > 0 ? null : (
    <View>
      <Text style={styles.label}>Paste your wallet address:</Text>
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          minHeight: 45,
        }}>
        <TextInput
          value={walletAddress}
          onChangeText={setWalletAddress}
          style={styles.inputField}
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
        <Button
          isLoading={loading}
          disabled={walletAddress?.length === 0 || loading}
          text="Save"
          style={{height: 45, borderRadius: 5}}
          onPress={handleWalletAddressPaste.bind(null, walletAddress)}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  inputField: {
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 4,
    color: colors.black,
    paddingHorizontal: spacings.md,
    fontSize: fontSize.md,
    fontFamily: 'Inter-Regular',
    height: 45,
    flex: 1,
    marginRight: 10,
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.black,
    fontFamily: 'Inter-Regular',
    marginVertical: 10,
  },
});
