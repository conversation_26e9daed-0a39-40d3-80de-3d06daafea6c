import React from 'react';
import {Text} from 'react-native';

import styles from './styles';
import Icon from '../Icon';
import TouchAwareButton from '../Button/TouchAwareButton';
import ActivityIndicator from '../ActivityIndicator';
import colors from '../../assets/colors';

export default function ListActionRow({
  iconName = '',
  iconSize = 20,
  actionText = '',
  iconStyle = {},
  actionTextStyle = {},
  onPress = () => null,
  isLoading = false,
  disabled = false,
  iconContainerStyle = {},
  style = {},
  containerStyle = {},
  iconTint = colors.white,
}) {
  return (
    <TouchAwareButton
      style={{...styles.container, style}}
      containerStyle={containerStyle}
      onPress={onPress}
      disabled={disabled}>
      <Icon
        name={iconName}
        size={iconSize}
        style={iconStyle}
        tint={iconTint}
        containerStyle={[styles.iconContainerStyle, iconContainerStyle]}
      />
      <Text style={[styles.actionText, actionTextStyle]}>{actionText}</Text>
      {isLoading ? <ActivityIndicator /> : null}
    </TouchAwareButton>
  );
}
