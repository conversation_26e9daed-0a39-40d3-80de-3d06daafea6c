/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH - spacings.xxl;

const CommentsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={50}
          borderRadius={4}
          marginHorizontal={20}
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={50}
          borderRadius={4}
          marginHorizontal={20}
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={50}
          borderRadius={4}
          marginHorizontal={20}
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={50}
          borderRadius={4}
          marginHorizontal={20}
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={50}
          borderRadius={4}
          marginHorizontal={20}
        />
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingVertical: spacings.lg,
  },
});

export default CommentsLoader;
