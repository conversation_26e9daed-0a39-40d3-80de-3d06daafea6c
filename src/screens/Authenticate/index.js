import React from 'react';
import {StyleSheet, Text, View} from 'react-native';

import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {navLabels} from '../../assets/strings';

const Index = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.textStyle}>{navLabels.AUTHENTICATE}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  textStyle: {
    color: colors.white,
    fontSize: fontSize.md,
  },
});

export default Index;
