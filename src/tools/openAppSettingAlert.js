import {Alert, Linking} from 'react-native';

import locale from '../assets/locale.json';

export default (
  alertTitle,
  alertMessage,
  willStay,
  navigationReset = true,
  navigation,
) => {
  Alert.alert(alertTitle, alertMessage, [
    {
      text: locale.cancel,
      onPress: () => {
        {
          !willStay ? navigation.goBack() : {};
        }
      },
      style: 'cancel',
    },
    {
      text: locale.takeMeThere,
      onPress: () => {
        Linking.openSettings();
        navigationReset && navigation.goBack();
      },
    },
  ]);
};
