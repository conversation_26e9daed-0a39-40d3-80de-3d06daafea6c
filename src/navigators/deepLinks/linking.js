import {DOMAIN, DOMAIN_WWW} from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NativeURL from 'url';

import {screens, persistent} from '../../assets/strings';

const AUTHENTICATION_REQUIRED_SCREENS = [
  screens.SINGLE_LISTING,
  screens.AUTHENTICATE,
];

const config = {
  screens: {
    [screens.SINGLE_LISTING]: {
      path: 'app/post/:key',
    },
    [screens.AUTHENTICATE]: {
      path: 'app/create-new-authentication',
    },
  },
};

const linking = {
  prefixes: [DOMAIN, DOMAIN_WWW, 'post://auth8'],
  config,

  async filter(deepLinkURL) {
    try {
      const sessionUserId = await AsyncStorage.getItem(persistent.USER_ID);
      if (sessionUserId == null) {
        const url = NativeURL.parse(deepLinkURL, true);
        const extractedParams = {};
        const screen = Object.entries(config.screens).find(
          ([_, screenOptions]) => {
            const pathname = url?.pathname?.slice(1);
            const splitPathnameThroughSlash = pathname.split('/');
            const splitScreenPathThroughSlash = screenOptions.path.split('/');
            return splitPathnameThroughSlash.every((path, index) => {
              const targetEntryToCompare = splitScreenPathThroughSlash[index];
              if (targetEntryToCompare.startsWith(':')) {
                extractedParams[targetEntryToCompare.slice(1)] = path;
                return !(path == null);
              }
              return path === targetEntryToCompare;
            });
          },
        );
        if (
          !(screen == null) &&
          AUTHENTICATION_REQUIRED_SCREENS.includes(screen[0])
        ) {
          await AsyncStorage.setItem(
            persistent.REDIRECT_NAVIGATION,
            JSON.stringify({
              path: screen[0],
              params: {...(url.query ?? {}), ...extractedParams},
            }),
          );
          return false;
        }
      }
    } catch (_) {}
    return true;
  },
};

export default linking;
