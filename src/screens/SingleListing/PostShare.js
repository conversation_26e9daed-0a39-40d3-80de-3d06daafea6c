import React from 'react';
import {Text, View} from 'react-native';
import RNFetchBlob from 'rn-fetch-blob';
import Share from 'react-native-share';

import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import usePostShareURLConstructor from '../../tools/hooks/usePostShareURLConstructor';
import {showToast} from '../../components/Toast';
import locale from '../../assets/locale.json';
import ActivityIndicator from '../../components/ActivityIndicator';
import TouchAwareButton from '../../components/Button/TouchAwareButton';

export default function PostShare({
  postTitle = '',
  authenticationKey = null,
  isAuthenticationPending = false,
  imageUrl = null,
  authenticationType = null,
}) {
  const [loading, setLoading] = React.useState(false);

  const {
    url: postShareURL,
    displayTitle: shareDisplayTitle,
    isValid: isValidShareURL,
  } = usePostShareURLConstructor({
    authenticationKey,
    postTitle,
    authenticationType,
  });

  const handleSystemShare = React.useCallback(async () => {
    if (imageUrl) {
      setLoading(true);
      const {data} = await RNFetchBlob.fetch('GET', imageUrl);
      setLoading(false);

      await Share.open({
        url: `data:image/png;base64,${data}`,
        message: `${shareDisplayTitle}\n\n${postShareURL}`,
        failOnCancel: false,
      });
    } else {
      await Share.open({
        message: `${shareDisplayTitle}\n\n${postShareURL}\n\n${locale.DownloadTheAppToSeeFullResults}`,
        failOnCancel: false,
      });
    }
  }, [shareDisplayTitle, postShareURL, imageUrl]);

  const handlePostShare = React.useCallback(async () => {
    try {
      await handleSystemShare();
    } catch (_) {
      showToast({
        type: 'error',
        message: locale.SomethingWentWrongPleaseTryAgain,
        position: 'bottom',
      });
    } finally {
      setLoading(false);
    }
  }, [imageUrl, handleSystemShare]);

  return !isAuthenticationPending && isValidShareURL ? (
    <TouchAwareButton
      onPress={handlePostShare}
      containerStyle={{
        marginVertical: 5,
      }}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.grey3,
        borderRadius: 10,
        minWidth: 80,
        paddingHorizontal: 10,
      }}
      disabled={loading}>
      {loading ? (
        <ActivityIndicator size={20} style={{marginRight: 5}} />
      ) : (
        <Icon name="share" style={{marginRight: 5}} />
      )}

      <Text
        style={{
          fontFamily: 'Inter-Bold',
          fontSize: 14,
          paddingVertical: 5,
        }}>
        {locale.Share}
      </Text>
    </TouchAwareButton>
  ) : null;
}
