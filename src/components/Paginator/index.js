import React from 'react';
import {View, Animated, useWindowDimensions, StyleSheet} from 'react-native';
import colors from '../../assets/colors';

const Paginator = ({data, scrollx}) => {
  const {width} = useWindowDimensions();
  return (
    <View style={{flexDirection: 'row', height: 10}}>
      {data.map((_, i) => {
        const inputRange = [(i - 1) * width, i * width, (i + 1) * width];

        const opacity = scrollx.interpolate({
          inputRange,
          outputRange: [0.3, 1, 0.3],
          extrapolate: 'clamp',
        });

        return (
          <Animated.View style={[styles.dot, {opacity}]} key={i.toString()} />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    height: 8,
    width: 8,
    backgroundColor: colors.white,
    borderRadius: 8 / 2,
    marginHorizontal: 5,
    alignSelf: 'center',
  },
});

export default Paginator;
