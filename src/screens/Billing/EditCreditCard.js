import React, {useState, useEffect, useLayoutEffect} from 'react';
import {
  TextInput,
  StyleSheet,
  Text,
  View,
  Pressable,
  TouchableOpacity,
  Platform,
} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';
import {useToast} from 'react-native-toast-notifications';

import EditCreditCardsLoader from '../../components/Loaders/EditCreditCardsLoader';
import {UPDATE_CARD, DELETE_CARD} from '../../apollo/mutations';
import {GET_CARD, GET_ALL_CARDS} from '../../apollo/queries';
import {editCreditCardScreen, icons, screens} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import Icon from '../../components/Icon';
import Layout from '../../layout';
import Button from '../../components/Button';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

const isIOS = Platform.OS === 'ios';

const EditCreditCard = ({route, navigation}) => {
  const {params} = route?.params;

  const [modalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const {data, loading, error} = useQuery(GET_CARD, {
    variables: {id: params},
  });

  const [updateCard] = useMutation(UPDATE_CARD, {
    refetchQueries: [GET_CARD, {variables: {cardId: params}}],
  });
  const [deleteCard] = useMutation(DELETE_CARD, {
    refetchQueries: [GET_ALL_CARDS],
  });
  const toast = useToast();

  const [name, setName] = useState('');
  const [isDefaultCard, setIsDefaultCard] = useState(false);

  useEffect(() => {
    setName(data?.getCard?.data?.name ?? '');
    setIsDefaultCard(data?.getCard?.data?.is_default ?? '');
  }, [data]);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity
          style={styles.trash}
          onPress={() => setModalVisible(true)}>
          <Icon
            name={icons.TRASH_ICON}
            size={fontSize.xxl}
            tint={colors.white}
          />
        </TouchableOpacity>
      ),
    });
  }, [navigation]);

  const handleCardDelete = async () => {
    try {
      setIsLoading(true);
      const {data: deleteResponse} = await deleteCard({
        variables: {
          cardId: data?.getCard.data.id,
        },
      });
      setModalVisible(false);
      const isSuccess = deleteResponse?.deleteCard?.success;
      const code = deleteResponse?.deleteCard?.code;
      const message = deleteResponse?.deleteCard?.message;
      if (isSuccess) {
        navigation.navigate(screens.CREDIT_CARDS);
      } else {
        if (code?.length) {
          setPopup(previousState => ({
            ...previousState,
            state: true,
            isError: true,
            data: {
              title: locale.Error,
              description: message,
            },
          }));
        } else {
          throw new Error();
        }
      }
    } catch (e) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
        isError: true,
        data: {
          title: locale.Error,
          description: locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCardChange = async () => {
    setIsLoading(true);
    try {
      const {data: updateCardResponse} = await updateCard({
        variables: {
          cardId: data?.getCard.data.id,
          setAsDefault: isDefaultCard,
          name: name,
        },
      });
      const isSuccess = updateCardResponse?.updateCard?.success;
      const code = updateCardResponse?.updateCard?.code;
      const message = updateCardResponse?.updateCard?.message;
      if (isSuccess) {
        navigation.navigate(screens.CREDIT_CARDS);
      } else {
        if (code?.length) {
          setPopup(previousState => ({
            ...previousState,
            state: true,
            isError: true,
            data: {
              title: locale.Error,
              description: message,
            },
          }));
        } else {
          throw new Error();
        }
      }
    } catch (_) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
        isError: true,
        data: {
          title: locale.Error,
          description: locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const defaultCardCheckBox = () => {
    setIsDefaultCard(!isDefaultCard);
  };

  if (error) {
    toast.show('Something went wrong.', {
      type: 'warning',
      placement: 'bottom',
      duration: 4000,
      offset: 30,
      animationType: 'slide-in',
    });
    return <EditCreditCardsLoader />;
  }
  if (loading) {
    return <EditCreditCardsLoader />;
  }

  return (
    <Layout style={styles.container}>
      <View style={{paddingTop: 20}}>
        <Text style={styles.label}>{editCreditCardScreen.CARD_NAME}</Text>
        <TextInput
          value={name}
          style={styles.fieldInput}
          onChangeText={setName}
          placeholder={editCreditCardScreen.NAME_PLACEHOLDER}
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
        <View style={styles.checkBoxWrapper}>
          <Pressable
            style={[styles.checkBox, {flexDirection: 'row'}]}
            onPress={defaultCardCheckBox}>
            {isDefaultCard ? (
              <Icon
                name={icons.CHECKED_RADIO}
                size={fontSize.xxxl}
                color={colors.primary}
              />
            ) : (
              <Icon
                name={icons.UNCHECKED_RADIO}
                size={fontSize.xxxl}
                color={colors.grey}
              />
            )}
            <Text style={[styles.defaultCardLabel, {marginLeft: 10}]}>
              {editCreditCardScreen.SET_DEFAULT}
            </Text>
          </Pressable>
        </View>
        <View style={styles.notFungibleWrapper}>
          <View style={styles.notFungible}>
            <Text style={styles.label}>{editCreditCardScreen.BRAND}</Text>
            <Text style={styles.value}>{data?.getCard.data.brand}</Text>
          </View>
          <View style={styles.notFungible}>
            <Text style={styles.label}>{editCreditCardScreen.CARD_NUMBER}</Text>
            <Text style={styles.value}>{data?.getCard.data.number}</Text>
          </View>
          <View style={styles.notFungible}>
            <Text style={styles.label}>{editCreditCardScreen.EXPIRY_DATE}</Text>
            <Text style={styles.value}>
              {data?.getCard?.data?.expiry_month}/
              {data?.getCard?.data?.expiry_year}
            </Text>
          </View>
        </View>
        <Button
          text={editCreditCardScreen.SAVE_CHANGES}
          style={{height: 50, borderRadius: 5}}
          isLoading={isLoading}
          disabled={isLoading}
          onPress={handleCardChange}
        />
      </View>
      <InfoModal
        setVisible={setModalVisible}
        showLottieIcon={false}
        popUp={{
          state: modalVisible,
          data: {
            title: locale.DeleteCard,
            description: locale.AreYouSureYouWantToDeleteThisCard,
          },
        }}
        showDefaultButton={false}>
        <View style={{flexDirection: 'row'}}>
          <Button
            text={locale.No}
            style={{marginRight: 10, backgroundColor: colors.darkGrey}}
            onPress={() => setModalVisible(false)}
          />
          <Button
            text={locale.Yes}
            style={{backgroundColor: colors.warning}}
            onPress={handleCardDelete}
            isLoading={isLoading}
            disabled={isLoading}
          />
        </View>
      </InfoModal>
      <InfoModal
        setVisible={() => {
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  checkBox: {
    marginRight: spacings.sm,
    alignItems: 'center',
  },
  checkBoxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  defaultCardLabel: {
    color: colors.black,
    fontSize: fontSize.lg,
  },
  button: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.xxl,
    borderRadius: 4,
  },
  buttonText: {
    fontSize: fontSize.lg,
    color: colors.white,
  },
  fieldInput: {
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 4,
    marginBottom: 20,
    paddingLeft: spacings.md,
    color: colors.black,
    ...(isIOS ? {padding: 15} : {}),
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.black,
    marginBottom: spacings.sm,
    fontFamily: 'Inter-Medium',
  },
  value: {
    fontSize: fontSize.lg,
    color: colors.grey,
    marginBottom: spacings.sm,
  },
  notFungible: {
    marginBottom: 10,
  },
  notFungibleWrapper: {
    backgroundColor: colors.lightGrey,
    padding: spacings.lg,
    borderRadius: 4,
    marginVertical: 20,
  },
  trash: {
    paddingVertical: spacings.sm,
    paddingHorizontal: spacings.lg,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.darkBadge,
  },
  visibleSection: {
    backgroundColor: colors.white,
    paddingTop: spacings.xxl,
    marginHorizontal: spacings.xl,
    borderRadius: 4,
  },
  deleteButtonWrapper: {
    flexDirection: 'row',
    width: '100%',
  },
  cancelButton: {
    flex: 1 / 2,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.black,
    borderBottomRightRadius: 4,
    paddingVertical: spacings.md,
  },
  deleteButton: {
    flex: 1 / 2,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 4,
    backgroundColor: colors.primary,
    paddingVertical: spacings.md,
  },
  modalHeader: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: spacings.lg,
  },
  cardModalValue: {
    color: colors.grey,
    fontSize: fontSize.md,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});

export default EditCreditCard;
