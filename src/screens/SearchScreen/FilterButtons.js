import React from 'react';
import {Text, View, TouchableOpacity, Pressable} from 'react-native';
import {useQuery} from '@apollo/client';
import {ScrollView, FlatList} from 'react-native-gesture-handler';
import Animated, {SlideInDown} from 'react-native-reanimated';

import styles from './styles';
import Icon from '../../components/Icon';
import {icons} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {
  GET_AUTHENTICATION_INFO_FOR_SEARCH,
  GET_CATEGORIES,
} from '../../apollo/queries';
import BottomSheet from '../../components/BottomSheet';
import {FILTER_TYPE} from './TShirts';
import {BrandPicker} from '../../ui';

const AUTHENTICATION_RESULTS = [
  {
    id: 'All',
    name: 'All',
  },
  {
    id: 'REA<PERSON>',
    name: 'Pass',
  },
  {
    id: 'FAKE',
    name: 'Fail',
  },
  {
    id: 'UNDECIDED',
    name: 'Undecided',
  },
  {
    id: 'UNDETERMINED',
    name: 'Undetermined',
  },
];

function FilterButtons({
  selectedType = null,
  selectedMaterial = null,
  selectedDecade = null,
  selectedStitching = null,
  selectedBrand = null,
  selectedSize = null,
  handleRowSelection = () => null,
  selectedVerdict = null,
  selectedCategory = null,
}) {
  const {data} = useQuery(GET_AUTHENTICATION_INFO_FOR_SEARCH);

  return (
    <View style={[styles.filters, {backgroundColor: colors.white}]}>
      <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
        <TypesButton
          selectedType={selectedType}
          list={
            Array.isArray(data?.getAuthenticationInfo?.data?.type)
              ? [
                  {id: 'All', name: 'All'},
                  ...data?.getAuthenticationInfo?.data?.type?.filter?.(
                    item => item?.id !== 5,
                  ),
                ]
              : []
          }
          handleRowSelection={handleRowSelection}
        />
        <VerdictButton
          selectedType={selectedVerdict}
          list={AUTHENTICATION_RESULTS}
          handleRowSelection={handleRowSelection}
        />
        <BrandsButton
          selectedBrand={selectedBrand}
          handleRowSelection={handleRowSelection}
        />
        <CategoryButton
          selectedCategory={selectedCategory}
          handleRowSelection={handleRowSelection}
        />
        <DecadesButton
          selectedDecade={selectedDecade}
          list={
            Array.isArray(data?.getAuthenticationInfo?.data?.from_decades)
              ? [
                  {id: 'All', name: 'All'},
                  ...data?.getAuthenticationInfo?.data?.from_decades,
                ]
              : []
          }
          handleRowSelection={handleRowSelection}
        />
        <StitchingButton
          selectedStitching={selectedStitching}
          list={
            Array.isArray(data?.getAuthenticationInfo?.data?.stitches)
              ? [
                  {id: 'All', name: 'All'},
                  ...data?.getAuthenticationInfo?.data?.stitches,
                ]
              : []
          }
          handleRowSelection={handleRowSelection}
        />
        <MaterialsButton
          selectedMaterial={selectedMaterial}
          list={
            Array.isArray(data?.getAuthenticationInfo?.data?.materials)
              ? [
                  {id: 'All', name: 'All'},
                  ...data?.getAuthenticationInfo?.data?.materials,
                ]
              : []
          }
          handleRowSelection={handleRowSelection}
        />
        <LabelSizeButton
          selectedSize={selectedSize}
          list={
            Array.isArray(data?.getAuthenticationInfo?.data?.sizes)
              ? [
                  {id: 'All', name: 'All'},
                  ...data?.getAuthenticationInfo?.data?.sizes,
                ]
              : []
          }
          handleRowSelection={handleRowSelection}
        />
      </ScrollView>
    </View>
  );
}

export const TypesButton = React.memo(
  ({selectedType = null, list = [], handleRowSelection = () => null}) => {
    const [sheetVisible, setSheetVisible] = React.useState(false);
    const [selected, setSelected] = React.useState(null);

    const handlePress = () => {
      list?.length > 0 && setSheetVisible(true);
    };

    const handleRowPressed = item => {
      setSelected(previousState =>
        selectedType == null
          ? item
          : previousState?.id == item?.id
          ? null
          : item,
      );
      handleRowSelection(item, FILTER_TYPE.AuthenticationType);
      setSheetVisible(false);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(100)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <TouchableOpacity
          style={[
            styles.filterChip,
            {
              backgroundColor:
                selectedType === null ? colors.white : colors.primary,
            },
          ]}
          onPress={handlePress}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[
              styles.filterChipText,
              {
                color: selectedType === null ? colors.darkGrey : colors.white,
              },
            ]}>
            Types: {selectedType === null ? 'All' : selectedType?.name}
          </Text>
          <Icon
            style={{marginLeft: spacings.md, padding: spacings.sm}}
            name={icons.CHEVRON_DOWN}
            size={5}
            tint={selectedType === null ? colors.darkGrey : colors.white}
          />
        </TouchableOpacity>
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight>
          <FlatList
            keyExtractor={item => item?.id?.toString?.()}
            data={list}
            renderItem={({item}) => (
              <ListRow
                item={item}
                isSelected={selected?.id == item?.id}
                onPress={handleRowPressed.bind(null, item)}
              />
            )}
          />
        </BottomSheet>
      </Animated.View>
    );
  },
);
export const VerdictButton = React.memo(
  ({selectedType = null, list = [], handleRowSelection = () => null}) => {
    const [sheetVisible, setSheetVisible] = React.useState(false);
    const [selected, setSelected] = React.useState(null);

    const handlePress = () => {
      list?.length > 0 && setSheetVisible(true);
    };

    const handleRowPressed = item => {
      setSelected(previousState =>
        selectedType == null
          ? item
          : previousState?.id == item?.id
          ? null
          : item,
      );
      handleRowSelection(item, FILTER_TYPE.Verdict);
      setSheetVisible(false);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(150)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <TouchableOpacity
          style={[
            styles.filterChip,
            {
              backgroundColor:
                selectedType === null ? colors.white : colors.primary,
            },
          ]}
          onPress={handlePress}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[
              styles.filterChipText,
              {
                color: selectedType === null ? colors.darkGrey : colors.white,
              },
            ]}>
            Authenticity: {selectedType === null ? 'All' : selectedType?.name}
          </Text>
          <Icon
            style={{marginLeft: spacings.md, padding: spacings.sm}}
            name={icons.CHEVRON_DOWN}
            size={5}
            tint={selectedType === null ? colors.darkGrey : colors.white}
          />
        </TouchableOpacity>
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight>
          <FlatList
            keyExtractor={item => item?.id?.toString?.()}
            data={list}
            renderItem={({item}) => (
              <ListRow
                item={item}
                isSelected={selected?.id == item?.id}
                onPress={handleRowPressed.bind(null, item)}
              />
            )}
          />
        </BottomSheet>
      </Animated.View>
    );
  },
);

export const BrandsButton = React.memo(
  ({selectedBrand = null, handleRowSelection = () => null}) => {
    const [selected, setSelected] = React.useState([]);

    const handleClear = () => {
      setSelected([]);
      handleRowSelection([], FILTER_TYPE.Brand);
    };

    const handleApply = selectedItems => {
      setSelected(selectedItems);
      handleRowSelection(selectedItems, FILTER_TYPE.Brand);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(200)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <BrandPicker
          onApply={handleApply}
          onClear={handleClear}
          selectedItems={selected}>
          {({openPicker}) => (
            <TouchableOpacity
              style={[
                styles.filterChip,
                {
                  backgroundColor:
                    selectedBrand?.length == 0 ? colors.white : colors.primary,
                },
              ]}
              onPress={openPicker}>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[
                  styles.filterChipText,
                  {
                    color:
                      selectedBrand?.length === 0
                        ? colors.darkGrey
                        : colors.white,
                  },
                ]}>
                Brands: {selectedBrand?.length === 0 ? 'All' : '...'}
              </Text>
              <Icon
                style={{marginLeft: spacings.md, padding: spacings.sm}}
                name={icons.CHEVRON_DOWN}
                size={5}
                tint={
                  selectedBrand?.length === 0 ? colors.darkGrey : colors.white
                }
              />
            </TouchableOpacity>
          )}
        </BrandPicker>
      </Animated.View>
    );
  },
);

export const MaterialsButton = React.memo(
  ({selectedMaterial = null, list = [], handleRowSelection = () => null}) => {
    const [sheetVisible, setSheetVisible] = React.useState(false);
    const [selected, setSelected] = React.useState(null);

    const handlePress = () => {
      list?.length > 0 && setSheetVisible(true);
    };

    const handleRowPressed = item => {
      setSelected(previousState =>
        selectedMaterial == null
          ? item
          : previousState?.id == item?.id
          ? null
          : item,
      );
      handleRowSelection(item, FILTER_TYPE.Material);
      setSheetVisible(false);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(250)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <TouchableOpacity
          style={[
            styles.filterChip,
            {
              backgroundColor:
                selectedMaterial === null ? colors.white : colors.primary,
            },
          ]}
          onPress={handlePress}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[
              styles.filterChipText,
              {
                color:
                  selectedMaterial == null ? colors.darkGrey : colors.white,
              },
            ]}>
            Materials:{' '}
            {selectedMaterial === null ? 'All' : selectedMaterial?.name}
          </Text>
          <Icon
            style={{marginLeft: spacings.md, padding: spacings.sm}}
            name={icons.CHEVRON_DOWN}
            size={5}
            tint={selectedMaterial == null ? colors.darkGrey : colors.white}
          />
        </TouchableOpacity>
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight>
          <FlatList
            keyExtractor={item => item?.id?.toString?.()}
            data={list}
            renderItem={({item}) => (
              <ListRow
                item={item}
                isSelected={selected?.id == item?.id}
                onPress={handleRowPressed.bind(null, item)}
              />
            )}
          />
        </BottomSheet>
      </Animated.View>
    );
  },
);

export const DecadesButton = React.memo(
  ({selectedDecade = null, list = [], handleRowSelection = () => null}) => {
    const [sheetVisible, setSheetVisible] = React.useState(false);
    const [selected, setSelected] = React.useState(null);

    const handlePress = () => {
      list?.length > 0 && setSheetVisible(true);
    };

    const handleRowPressed = item => {
      setSelected(previousState =>
        selectedDecade == null
          ? item
          : previousState?.id == item?.id
          ? null
          : item,
      );
      handleRowSelection(item, FILTER_TYPE.Decade);
      setSheetVisible(false);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(250)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <TouchableOpacity
          style={[
            styles.filterChip,
            {
              backgroundColor:
                selectedDecade === null ? colors.white : colors.primary,
            },
          ]}
          onPress={handlePress}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[
              styles.filterChipText,
              {
                color: selectedDecade == null ? colors.darkGrey : colors.white,
              },
            ]}>
            Decades: {selectedDecade === null ? 'All' : selectedDecade?.name}
          </Text>
          <Icon
            style={{marginLeft: spacings.md, padding: spacings.sm}}
            name={icons.CHEVRON_DOWN}
            size={5}
            tint={selectedDecade == null ? colors.darkGrey : colors.white}
          />
        </TouchableOpacity>
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight>
          <FlatList
            keyExtractor={item => item?.id?.toString?.()}
            data={list}
            renderItem={({item}) => (
              <ListRow
                item={item}
                isSelected={selected?.id == item?.id}
                onPress={handleRowPressed.bind(null, item)}
              />
            )}
          />
        </BottomSheet>
      </Animated.View>
    );
  },
);

export const StitchingButton = React.memo(
  ({selectedStitching = null, list = [], handleRowSelection = () => null}) => {
    const [sheetVisible, setSheetVisible] = React.useState(false);
    const [selected, setSelected] = React.useState(null);

    const handlePress = () => {
      list?.length > 0 && setSheetVisible(true);
    };

    const handleRowPressed = item => {
      setSelected(previousState =>
        selectedStitching == null
          ? item
          : previousState?.id == item?.id
          ? null
          : item,
      );
      handleRowSelection(item, FILTER_TYPE.Stitch);
      setSheetVisible(false);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(300)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <TouchableOpacity
          onPress={handlePress}
          style={[
            styles.filterChip,
            {
              backgroundColor:
                selectedStitching === null ? colors.white : colors.primary,
            },
          ]}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[
              styles.filterChipText,
              {
                color:
                  selectedStitching == null ? colors.darkGrey : colors.white,
              },
            ]}>
            Stitching:{' '}
            {selectedStitching === null ? 'All' : selectedStitching?.name}
          </Text>
          <Icon
            style={{marginLeft: spacings.md, padding: spacings.sm}}
            name={icons.CHEVRON_DOWN}
            size={5}
            tint={selectedStitching == null ? colors.darkGrey : colors.white}
          />
        </TouchableOpacity>
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight>
          <FlatList
            keyExtractor={item => item?.id?.toString?.()}
            data={list}
            renderItem={({item}) => (
              <ListRow
                item={item}
                isSelected={selected?.id == item?.id}
                onPress={handleRowPressed.bind(null, item)}
              />
            )}
          />
        </BottomSheet>
      </Animated.View>
    );
  },
);
export const LabelSizeButton = React.memo(
  ({selectedSize = null, list = [], handleRowSelection = () => null}) => {
    const [sheetVisible, setSheetVisible] = React.useState(false);
    const [selected, setSelected] = React.useState(null);

    const handlePress = () => {
      list?.length > 0 && setSheetVisible(true);
    };

    const handleRowPressed = item => {
      setSelected(previousState =>
        selectedSize == null
          ? item
          : previousState?.id == item?.id
          ? null
          : item,
      );
      handleRowSelection(item, FILTER_TYPE.Size);
      setSheetVisible(false);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(350)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <TouchableOpacity
          onPress={handlePress}
          style={[
            styles.filterChip,
            {
              marginRight: spacings.lg,
              backgroundColor:
                selectedSize === null ? colors.white : colors.primary,
            },
          ]}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[
              styles.filterChipText,
              {
                color: selectedSize == null ? colors.darkGrey : colors.white,
              },
            ]}>
            Label Size: {selectedSize === null ? 'All' : selectedSize?.name}
          </Text>
          <Icon
            style={{marginLeft: spacings.md, padding: spacings.sm}}
            name={icons.CHEVRON_DOWN}
            size={5}
            tint={selectedSize == null ? colors.darkGrey : colors.white}
          />
        </TouchableOpacity>
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight>
          <FlatList
            keyExtractor={item => item?.id?.toString?.()}
            data={list}
            renderItem={({item}) => (
              <ListRow
                item={item}
                isSelected={selected?.id == item?.id}
                onPress={handleRowPressed.bind(null, item)}
              />
            )}
          />
        </BottomSheet>
      </Animated.View>
    );
  },
);
export const CategoryButton = React.memo(
  ({selectedCategory = null, handleRowSelection = () => null}) => {
    const [sheetVisible, setSheetVisible] = React.useState(false);
    const [selected, setSelected] = React.useState(null);

    const {data} = useQuery(GET_CATEGORIES);

    const list = React.useMemo(
      () =>
        Array.isArray(data?.getCategories?.data)
          ? [{id: 'All', name: 'All'}, ...data?.getCategories?.data]
          : [],
      [data?.getCategories?.data],
    );

    const handlePress = () => {
      list?.length > 0 && setSheetVisible(true);
    };

    const handleRowPressed = item => {
      setSelected(previousState =>
        selectedCategory == null
          ? item
          : previousState?.id == item?.id
          ? null
          : item,
      );
      handleRowSelection(item, FILTER_TYPE.Category);
      setSheetVisible(false);
    };

    return (
      <Animated.View
        entering={SlideInDown.delay(400)
          .springify()
          .damping(100)
          .stiffness(350)}>
        <TouchableOpacity
          onPress={handlePress}
          style={[
            styles.filterChip,
            {
              backgroundColor:
                selectedCategory === null ? colors.white : colors.primary,
            },
          ]}>
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={[
              styles.filterChipText,
              {
                color:
                  selectedCategory == null ? colors.darkGrey : colors.white,
              },
            ]}>
            Category:{' '}
            {selectedCategory === null ? 'All' : selectedCategory?.name}
          </Text>
          <Icon
            style={{marginLeft: spacings.md, padding: spacings.sm}}
            name={icons.CHEVRON_DOWN}
            size={5}
            tint={selectedCategory == null ? colors.darkGrey : colors.white}
          />
        </TouchableOpacity>
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight={false}
          snapPoints={['40%', '80%']}>
          <FlatList
            keyExtractor={item => item?.id?.toString?.()}
            data={list}
            renderItem={({item}) => (
              <ListRow
                item={item}
                isSelected={selected?.id == item?.id}
                onPress={handleRowPressed.bind(null, item)}
              />
            )}
          />
        </BottomSheet>
      </Animated.View>
    );
  },
);

function ListRow({
  item = null,
  onPress = () => null,
  isCheckBox = false,
  isSelected = false,
}) {
  return (
    <Pressable style={styles.modalSheetContainer} onPress={onPress}>
      <Text style={{color: colors.black}}>{item?.name}</Text>
      {!isCheckBox ? (
        <Icon
          name={isSelected ? icons.CHECKED_RADIO : icons.UNCHECKED_RADIO}
          tint={isSelected ? colors.primary : colors.grey}
          size={20}
        />
      ) : (
        <Icon
          style={styles[isSelected ? 'checkedbox' : 'uncheckedbox']}
          name={icons[isSelected ? 'CHECKED_RADIO' : 'UNCHECKED_RADIO']}
          tint={isSelected ? colors.primary : colors.grey}
          size={20}
        />
      )}
    </Pressable>
  );
}

export default FilterButtons;
