/* eslint-disable prettier/prettier */
import {Platform, Alert} from 'react-native';
import {PERMISSIONS, request, RESULTS,} from 'react-native-permissions';

import locale from '../assets/locale.json';
import permissionAlert from './openAppSettingAlert';

const decodeType = {
  camera: {
    ANDROID: 'CAMERA',
    IOS: 'CAMERA',
  },
  location: {
    ANDROID: 'ACCESS_FINE_LOCATION',
    IOS: 'LOCATION_WHEN_IN_USE',
  },
  storage: {
    ANDROID: 'READ_EXTERNAL_STORAGE',
    IOS: 'PHOTO_LIBRARY',
  },
  storage_new: {
    ANDROID: 'READ_MEDIA_IMAGES',
    IOS: 'PHOTO_LIBRARY',
  },
  calendar: {
    ANDROID: 'READ_CALENDAR',
    IOS: 'CALENDARS',
  },
};

export default async (
  permissionType,
  setGranted,
  willStay,
  requireAlert = true,
  navigationReset = true,
  navigation,
  // if navigation should go back after going to permission setting
) => {
  try {
    request(
      Platform.OS === 'android'
        ? PERMISSIONS.ANDROID?.[decodeType[permissionType]?.ANDROID]
        : PERMISSIONS.IOS?.[decodeType[permissionType]?.IOS],
    )
      .then(res => {
        if (res === RESULTS.DENIED || res === RESULTS.BLOCKED) {
          let title;
          let message;
          switch (permissionType) {
            case 'camera':
              title = locale.allowCameraPermissionToContinue;
              message = locale.cameraPermissionSteps;
              break;

            case 'location':
              title = locale.locationPermissionIsRequired;
              message = locale.locationPermissionSteps;
              break;

            case 'storage':
              title = locale.storagePermissionIsRequired;
              message = locale.storagePermissionSteps;
              break;

            case 'storage_new':
              title = locale.storagePermissionIsRequired;
              message = locale.storagePermissionSteps;
              break;

            case 'calendar':
              title = locale.calendarPermissionIsRequired;
              message = locale.calendarPermissionSteps;
              break;

            default:
              title = '';
              message = '';
          }
          requireAlert &&
            permissionAlert(
              title,
              message,
              willStay,
              navigationReset,
              navigation,
            );
        } else if (res === RESULTS.UNAVAILABLE) {
          Alert.alert(
            locale.FeatureNotAvailable,
            locale.ThisFeatureIsNotAvailableInYourDevice,
          );
        } else {
          setGranted(true);
        }
      })
      .catch(error => {
        // console.log(error);
      });
  } catch (err) {
    // console.warn(err);
  }
};
