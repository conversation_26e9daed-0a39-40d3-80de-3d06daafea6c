import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const BRAND_CARD_WIDTH = (WIDTH - spacings.lg) / 3.5;

const RecentBrandsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            width={BRAND_CARD_WIDTH}
            height={BRAND_CARD_WIDTH / 1.5}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={BRAND_CARD_WIDTH}
            height={BRAND_CARD_WIDTH / 1.5}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={BRAND_CARD_WIDTH}
            height={BRAND_CARD_WIDTH / 1.5}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
});

export default RecentBrandsLoader;
