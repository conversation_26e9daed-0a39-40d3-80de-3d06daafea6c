import React, {useCallback, useState} from 'react';
import {Text, View, TouchableOpacity, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/core';
import Animated, {FadeInUp} from 'react-native-reanimated';

import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {authenticationTypeScreen, icons, screens} from '../../assets/strings';
import {styles} from './styles';
import Icon from '../../components/Icon';
import {spacings} from '../../assets/spacing';
import Button from '../../components/Button';
import BottomSheet from '../../components/BottomSheet';
import locale from '../../assets/locale.json';

const RenderItem = ({
  item = null,
  onItemPressed = () => null,
  index = 0,
  isUpgradeToExpertAuthentication = false,
  upgradeData = null,
  isGetNFT = false,
  getNFTDetails = null,
}) => {
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);

  const handleNavigation = value => {
    navigation.navigate(screens.ADD_PHOTOS, {
      authenticationType: value,
      isUpgradeToExpertAuthentication,
      upgradeData,
    });
  };

  const handleNFTAuthenticationPressed = () => {
    setModalVisible(false);
    if (isGetNFT && !(getNFTDetails == null)) {
      navigation.navigate(screens.PAYMENT_METHOD, {
        authenticationType: getNFTDetails?.authenticationType,
        getNFTDetails,
        authenticationId: getNFTDetails?.authenticationId,
      });
    } else {
      navigation.navigate(screens.ADD_PHOTOS, {
        authenticationType: 3,
        isUpgradeToExpertAuthentication,
        upgradeData,
      });
    }
  };

  const handleNavigationToCryptoWallet = useCallback(() => {
    setModalVisible(false);
    navigation.navigate(screens.CONNECT_WALLET);
  }, []);

  const handleColor = useCallback(id => {
    switch (id) {
      case 1:
        return colors.black;
      case 2:
        return colors.secondary;
      case 3:
        return colors.primary;
      case 4:
        return colors.black;
      case 5:
        return colors.secondary;
    }
  }, []);

  const chooseIcons = id => {
    switch (id) {
      case 1:
        return icons.PEOPLE_OUTLINE;
      case 2:
        return icons.EXPERT_CERTIFIED;
      case 3:
        return icons.EXPERT_CERTIFIED_NFT;
      case 4:
        return icons.PRICE_TAG;
      case 5:
        return icons.CHAT_BUBBLE;
    }
  };

  const pricing = Platform.OS === 'ios' ? item?.iap_price_ios : item?.pricing;

  return (
    <Animated.View
      style={{flex: 1}}
      entering={FadeInUp.delay(150 + 40 * index)
        .springify()
        .damping(20)
        .stiffness(350)}>
      {item?.id === 4 ? (
        <Text style={styles.cardHeader}>
          {authenticationTypeScreen.HEADING_TWO}:
        </Text>
      ) : null}
      <Button
        style={{...styles.card, justifyContent: 'flex-start'}}
        onPress={() =>
          item?.id == 3
            ? handleNFTAuthenticationPressed()
            : handleNavigation(item?.id)
        }>
        <View style={[styles.sectionOne]}>
          <Icon
            name={chooseIcons(item?.id)}
            tint={handleColor(item?.id)}
            size={fontSize.xxl}
            style={{marginRight: 10}}
          />
          <Text
            style={[
              styles.cardText,
              item?.id == 4 ? {textTransform: 'capitalize'} : {},
              {maxWidth: '85%'},
            ]}
            numberOfLines={1}>
            {item?.name}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => onItemPressed(item?.id)}
          style={{paddingHorizontal: 10}}>
          <Icon
            name={icons.QUESTIONMARK_CIRCLE}
            tint={colors.primary}
            size={fontSize.xxl}
          />
        </TouchableOpacity>
        {item?.id === 2 || item?.id === 3 || item?.id === 5 ? (
          <View style={styles.sectionTwo}>
            <Text style={styles.textStyle}>${pricing}</Text>
          </View>
        ) : null}
        {item?.pricing === 0 && item?.id === 1 ? (
          <View style={styles.sectionTwo}>
            <Text style={styles.textStyle}>
              {authenticationTypeScreen.FREE}
            </Text>
          </View>
        ) : null}
      </Button>

      <BottomSheet
        visible={modalVisible}
        setVisible={setModalVisible}
        automaticallyAdjustHeight>
        <View style={{padding: 20}}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: colors.black,
                fontSize: fontSize.xl,
                fontWeight: 'bold',
              }}>
              {locale.AddWallet}
            </Text>
            <Button
              text={locale.Skip}
              style={{backgroundColor: 'transparent', minWidth: 30}}
              textStyle={{color: colors.primary, fontSize: 16}}
              onPress={() => handleNFTAuthenticationPressed(true)}
            />
          </View>

          <Text
            style={{
              color: colors.black,
              marginTop: spacings.md,
              fontSize: fontSize.md,
            }}>
            {locale['PleaseAddYourCryptoWallet...']}
          </Text>
          <TouchableOpacity
            onPress={handleNavigationToCryptoWallet}
            style={{
              marginTop: spacings.md,
              height: 50,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: colors.primary,
              borderRadius: 4,
            }}>
            <Text
              style={{
                color: colors.white,
                fontWeight: 'bold',
                fontSize: fontSize.lg,
              }}>
              {locale.ConnectCryptoWallet}
            </Text>
          </TouchableOpacity>
        </View>
      </BottomSheet>
    </Animated.View>
  );
};

export default RenderItem;
