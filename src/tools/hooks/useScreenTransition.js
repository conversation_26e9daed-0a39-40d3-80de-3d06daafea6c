import React from 'react';
import {Platform} from 'react-native';
import {useNavigation} from '@react-navigation/native';

const TYPE = {
  TRANSITION_OPENED: 'TRANSITION_OPENED',
  TRANSITION_CLOSED: 'TRANSITION_CLOSED',
};

const defaultOptions = {
  ignoreIOS: false,
};

const isIOS = Platform.OS === 'ios';

export default function useScreenTransition(options = defaultOptions) {
  options = {...defaultOptions, ...options};
  const navigation = useNavigation();

  const [transitionState, setTransitionState] = React.useState(
    options?.ignoreIOS && isIOS ? TYPE.TRANSITION_OPENED : undefined,
  );

  React.useEffect(() => {
    let unsubscribe;

    if (options.ignoreIOS && isIOS) {
      return;
    }

    unsubscribe = navigation.addListener('focus', e => {
      setTransitionState(
        e?.data?.closing ? TYPE.TRANSITION_CLOSED : TYPE.TRANSITION_OPENED,
      );
    });

    return () => {
      unsubscribe?.();
    };
  }, [navigation.addListener, options.ignoreIOS]);
  return {
    transitionState,
    isTransitionOpenComplete: transitionState === TYPE.TRANSITION_OPENED,
  };
}
