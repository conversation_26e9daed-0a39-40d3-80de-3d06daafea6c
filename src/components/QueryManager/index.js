import React from 'react';
import {Text, View} from 'react-native';

import Icon from '../Icon';
import colors from '../../assets/colors';
import locale from '../../assets/locale.json';
import Lottie from '../Lottie';

export function DefaultErrorComponent({
  style = {},
  textStyle = {},
  onRetryIconPress = () => null,
  customErrorText = locale['SomethingWentWrong.'],
  errorLottieStyle = {},
}) {
  return (
    <View
      style={[
        {flex: 1, justifyContent: 'center', alignItems: 'center'},
        style,
      ]}>
      <Lottie
        name="error2"
        style={[{width: 80, height: 80}, errorLottieStyle]}
        loop={false}
      />
      <Text style={[{marginBottom: 10, fontFamily: 'Inter-Medium'}, textStyle]}>
        {customErrorText}
      </Text>
      <Icon
        size={25}
        name="reload"
        tint={colors.red}
        clickable
        onPress={() => onRetryIconPress()}
      />
    </View>
  );
}

const CompoundContext = React.createContext({
  valid: false,
  refetch: () => null,
});

export const Loading = ({children = null}) => {
  const compoundContext = React.useContext(CompoundContext);
  return compoundContext?.valid ? children : null;
};

export const _Error = ({
  children = null,
  renderDefaultErrorComponent = false,
  style = {},
  customErrorText = locale['SomethingWentWrong.'],
  errorLottieStyle = {errorLottieStyle},
}) => {
  const {valid, refetch} = React.useContext(CompoundContext);

  return valid ? (
    renderDefaultErrorComponent ? (
      <DefaultErrorComponent
        onRetryIconPress={() => refetch()}
        style={style}
        customErrorText={customErrorText}
        errorLottieStyle={errorLottieStyle}
      />
    ) : (
      children
    )
  ) : null;
};

export const Data = ({children = null}) => {
  const compoundContext = React.useContext(CompoundContext);
  return compoundContext?.valid ? children : null;
};

export default function QueryManager({
  children: _children = null,
  data = null,
  error = null,
  loading = false,
  refetch = () => null,
}) {
  const children = [];
  const loaderChildren = [];
  const errorChildren = [];
  const dataChildren = [];

  React.Children.forEach(_children, child => {
    if (child?.type === Loading) {
      loaderChildren.push(child);
    } else if (child?.type === _Error) {
      errorChildren.push(child);
    } else if (child?.type === Data) {
      dataChildren.push(child);
    } else {
      children.push(child);
    }
  });

  return (
    <CompoundContext.Provider value={{valid: true, refetch}}>
      {data
        ? dataChildren
        : loading
        ? loaderChildren
        : error
        ? errorChildren
        : null}
    </CompoundContext.Provider>
  );
}

QueryManager.Loading = Loading;
QueryManager.Error = _Error;
QueryManager.Data = Data;
