import React from 'react';
import {FlatList, StyleSheet, Text, View, Dimensions} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import FollowersLoader from '../../components/Loaders/FollowersLoader';
import {GET_FOLLOWING} from '../../apollo/queries';
import colors from '../../assets/colors';
import {icons, messageListingScreen, screens} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Icon from '../../components/Icon';
import ProfileImage from '../../components/ProfileImage';
import RefreshControl from '../../components/RefreshControl';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {fixedNumberTo} from '../../tools/utils';

const {height: SCREEN_HEIGHT} = Dimensions.get('window');

const LIMIT = 100;

const FollowersLIsting = ({route, navigation}) => {
  const userId = route?.params.params;
  const scrollRef = React.useRef();

  const [refreshing, setRefreshing] = React.useState(false);

  const {data, loading, refetch} = useQuery(GET_FOLLOWING, {
    variables: {
      filters: {
        ofUserId: userId,
      },
      limit: LIMIT,
    },
  });

  const handleNavigation = item => {
    navigation.push(
      item?.role === 'EXPERT'
        ? screens.EXPERTS_PROFILE
        : screens.PUBLIC_PROFILES,
      {
        expertUserId: item?.id,
        params: item?.id,
      },
    );
  };

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (_) {}
    }, [refetch]),
  );

  const renderItem = ({item}) => {
    return (
      <TouchAwareButton
        style={styles.card}
        onPress={() => handleNavigation(item)}>
        <View style={styles.profileImageWrapper}>
          <ProfileImage size={45} url={item?.image} />
        </View>
        <View style={styles.info}>
          <Text style={styles.name} numberOfLines={1}>
            {item?.username}
          </Text>
          <View style={styles.accuracyWrapper}>
            {item?.role === 'EXPERT' ? null : (
              <>
                <Text style={styles.desc}>
                  {fixedNumberTo(item?.accuracy) ?? 0}
                </Text>
                <Text style={styles.separator}>
                  {messageListingScreen.SEPARATOR}
                </Text>
              </>
            )}
            <Text style={styles.desc}>{item?.badge?.name}</Text>
          </View>
        </View>
        <View style={styles.iconWrapper}>
          <Icon name={icons.CHEVRON} size={fontSize.xxxl} tint={colors.grey} />
        </View>
      </TouchAwareButton>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={data?.getFollowing?.data?.edges}
        keyExtractor={item => item?.id?.toString()}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          !loading ? (
            <View style={styles.emptyView}>
              <Icon
                name={icons.PEOPLE_OUTLINE}
                size={70}
                tint={colors.darkGrey}
              />
              <Text style={styles.emptyViewText}>
                Not following anyone yet.
              </Text>
            </View>
          ) : null
        }
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListFooterComponent={
          loading && data == null ? <FollowersLoader /> : null
        }
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
        initialNumToRender={10}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  accuracyWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    fontSize: 16,
    color: colors.black,
    fontFamily: 'Inter-SemiBold',
  },
  desc: {
    color: colors.darkGrey,
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
  },
  separator: {
    marginRight: spacings.sm,
    color: colors.darkGrey,
  },
  profileImageWrapper: {
    alignItems: 'flex-start',
  },
  profileImage: {
    height: 50,
    width: 50,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: colors.grey,
  },
  info: {
    paddingHorizontal: spacings.md,
  },
  iconWrapper: {
    alignItems: 'flex-end',
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.3,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FollowersLIsting;
