import React from 'react';
import {
  Text,
  Pressable,
  View,
  TextInput,
  Keyboard,
  Platform,
} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import styles from './styles';
import Icon from '../../components/Icon';

import {fontSize} from '../../assets/font';
import {icons, screens, searchScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import Users from './Users';
import TShirts from './TShirts';

const TabNavigator = createMaterialTopTabNavigator();

const SearchNavigator = function ({navigation}) {
  const {top} = useSafeAreaInsets();
  const [term, setTerm] = React.useState('');

  const [screenTarget, setScreenTarget] = React.useState(0);

  const inputRef = React.useRef();
  const debounceIdRef = React.useRef();

  const navigateBack = React.useCallback(() => {
    if (screenTarget === 1) {
      navigation.navigate(screens.TSHIRT_SEARCH);
    } else {
      Keyboard.dismiss();
      navigation.goBack();
    }
  }, [navigation, screenTarget]);

  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      inputRef.current?.focus?.();
    }, 500);

    return () => {
      clearTimeout(timeoutId);
      clearTimeout(debounceIdRef.current);
    };
  }, []);

  const handleFocus = React.useCallback(({target}) => {
    setScreenTarget(target?.startsWith(screens.USER_SEARCH) ? 1 : 0);
  });

  return (
    <View style={{flex: 1, paddingTop: top}}>
      <View style={styles.modalHeader}>
        <View style={styles.searchBarWrapper}>
          <Icon
            style={{padding: spacings.sm}}
            name={icons.SEARCH_ICON}
            size={fontSize.xxl}
            tint={colors.darkGrey}
          />
          <TextInput
            ref={inputRef}
            value={term}
            onChangeText={setTerm}
            selectionColor={colors.white}
            placeholder={searchScreen.PLACEHOLDER}
            autoCapitalize="sentences"
            autoCorrect={false}
            placeholderTextColor="#828282"
            returnKeyType="search"
            style={styles.searchBar}
          />
        </View>
        <Pressable onPress={navigateBack}>
          <Text style={styles.closeButtonText}>
            {screenTarget === 0 ? 'Cancel' : 'Back'}
          </Text>
        </Pressable>
      </View>
      <TabNavigator.Navigator
        transitionStyle="curl"
        backBehavior="firstRoute"
        tabBar={() => null}
        keyboardDismissMode="none"
        initialRouteName={screens.TSHIRT_SEARCH}
        screenOptions={{
          swipeEnabled: Platform.OS === 'ios',
          tabBarActiveTintColor: colors.black,
          tabBarIndicatorStyle: styles.indicator,
          tabBarStyle: styles.barStyle,
          tabBarLabelStyle: styles.tabBarLabelStyle,
        }}
        screenListeners={{
          focus: handleFocus,
        }}>
        <TabNavigator.Screen
          name={screens.TSHIRT_SEARCH}
          options={{tabBarLabel: 'T-Shirt'}}>
          {props => <TShirts {...props} term={term} setTerm={setTerm} />}
        </TabNavigator.Screen>
        <TabNavigator.Screen
          name={screens.USER_SEARCH}
          options={{tabBarLabel: 'User'}}>
          {props => <Users {...props} term={term} setTerm={setTerm} />}
        </TabNavigator.Screen>
      </TabNavigator.Navigator>
    </View>
  );
};

export default SearchNavigator;
