/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const CreditCardsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH / 3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="flex-end"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={WIDTH}
          height={3}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH / 3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="flex-end"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={WIDTH}
          height={3}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH / 3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="flex-end"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={WIDTH}
          height={3}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH / 3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="flex-end"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={WIDTH}
          height={3}
          borderRadius={4}
          marginVertical={20}
        />
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default CreditCardsLoader;
