package com.auth8;

import com.facebook.react.ReactActivity;
import com.facebook.react.ReactActivityDelegate; 
import com.zoontek.rnbootsplash.RNBootSplash; 

public class MainActivity extends ReactActivity {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
      
  @Override
  protected String getMainComponentName() {
    return "authenteec8";
  }

  @Override
  protected ReactActivityDelegate createReactActivityDelegate() {
    return new ReactActivityDelegate(this, getMainComponentName()) {

      @Override
      protected void loadApp(String appKey) {
        RNBootSplash.init(MainActivity.this); // <- initialize the splash screen
        super.loadApp(appKey);
      }
    };
  }
}
