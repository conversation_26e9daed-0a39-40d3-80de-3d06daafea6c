import React, {useEffect, useState} from 'react';
import {Text, View, FlatList, Platform} from 'react-native';
import {useQuery} from '@apollo/client';
import {useDispatch} from 'react-redux';
import {STRIPE_MERCHANT_IDENTIFIER} from '@env';
import {useIsFocused} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useSelector} from 'react-redux';

import Icon from '../../components/Icon';
import {clearAllPhotosFromState} from '../../redux/actions/uploadMedia';
import {GET_STRIPE_PUBLISHABLE_KEY} from '../../apollo/queries';
import {
  authenticationTypeScreen,
  persistent,
  screens,
} from '../../assets/strings';
import RenderItem from './RenderItem';
import {styles} from './styles';
import BottomSheet from '../../components/BottomSheet';
import locale from '../../assets/locale.json';
import {useAuthenticationInfo} from '../../tools/hooks';
import colors from '../../assets/colors';

const Header = ({isUpgradeToExpertAuthentication = false}) => {
  return (
    <>
      <View style={styles.headerWrapper}>
        <Text style={styles.headerStyle}>
          {isUpgradeToExpertAuthentication
            ? locale.UpgradeToExpertCertification
            : authenticationTypeScreen.HEADER}
        </Text>
        <Text style={styles.headerText}>
          {isUpgradeToExpertAuthentication
            ? locale.ChooseTheCertificationYouWant
            : authenticationTypeScreen.SUBTITLE}
        </Text>
      </View>
      {!isUpgradeToExpertAuthentication ? (
        <Text style={styles.cardHeader}>
          {authenticationTypeScreen.HEADING_ONE}
        </Text>
      ) : null}
    </>
  );
};

function Authenticate({navigation, route}) {
  const {
    isUpgradeToExpertAuthentication,
    upgradeData,
    isGetNFT = false,
    getNFTDetails = {},
    coupon_code: couponCode,
  } = route?.params ?? {};

  const dispatch = useDispatch();
  const role = useSelector(state => state?.auth?.credential?.role);
  const isFocused = useIsFocused();

  const {authenticationTypes, data} = useAuthenticationInfo({isFocused});

  const [sheetState, setSheetState] = useState({
    visible: false,
    id: null,
  });

  const {data: publishableKeyData} = useQuery(GET_STRIPE_PUBLISHABLE_KEY);

  React.useLayoutEffect(() => {
    if (!navigation.canGoBack()) {
      navigation.setOptions({
        headerLeft: () => (
          <Icon
            name="cancel_x"
            tint={colors.white}
            style={{marginLeft: 10}}
            containerStyle={{padding: 10}}
            size={15}
            clickable
            onPress={() =>
              navigation.reset({
                index: 1,
                routes: [{name: screens.DRAW_NAVIGATOR}],
              })
            }
          />
        ),
      });
    }
  }, [navigation]);

  React.useLayoutEffect(() => {
    if (role?.toLowerCase?.() !== 'member') {
      navigation.canGoBack()
        ? navigation.goBack()
        : navigation.reset({
            index: 1,
            routes: [{name: screens.DRAW_NAVIGATOR}],
          });
    }
  }, [role, navigation]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      if (publishableKeyData?.getStripePublishableKey.data) {
        import('@stripe/stripe-react-native').then(({initStripe}) => {
          initStripe({
            publishableKey: publishableKeyData?.getStripePublishableKey.data,
            merchantIdentifier: STRIPE_MERCHANT_IDENTIFIER,
          });
        });
      }
    }
  }, [publishableKeyData?.getStripePublishableKey.data]);

  useEffect(() => {
    dispatch(clearAllPhotosFromState());
  }, [dispatch, navigation]);

  React.useEffect(() => {
    if (!(couponCode == null) && couponCode?.length > 0) {
      AsyncStorage.setItem(persistent.COUPON_CODE, couponCode)
        .then(() => null)
        .catch(() => null);
    }
  }, [couponCode]);

  const handleItemPress = id => {
    setSheetState({
      visible: true,
      id,
    });
  };

  const filterAuthenticationTypes = React.useMemo(
    () =>
      isGetNFT
        ? authenticationTypes
            ?.filter(item => item?.id == 3)
            ?.map(item => ({
              ...item,
              name: locale.UpgradeWithNFT,
              pricing:
                data?.upgradeToExpertCertifiedNFTFromExpertCertifiedPricing,
              iap_price_ios:
                data?.upgradeToExpertCertifiedNFTFromExpertCertified_IAP_Pricing,
            }))
        : isUpgradeToExpertAuthentication
        ? authenticationTypes?.filter(item => [2, 3, 5]?.includes(+item?.id))
        : authenticationTypes,
    [
      authenticationTypes,
      isUpgradeToExpertAuthentication,
      isGetNFT,
      data?.upgradeToExpertCertifiedNFTFromExpertCertifiedPricing,
    ],
  );

  const renderList = React.useMemo(
    () => (
      <FlatList
        showsVerticalScrollIndicator={false}
        data={filterAuthenticationTypes}
        renderItem={({item, index}) => (
          <RenderItem
            key={item?.id}
            onItemPressed={handleItemPress}
            item={item}
            index={index}
            {...{
              isUpgradeToExpertAuthentication,
              upgradeData,
              isGetNFT,
              getNFTDetails,
            }}
          />
        )}
        keyExtractor={item => item?.id?.toString?.()}
        ListHeaderComponent={
          <Header
            isUpgradeToExpertAuthentication={isUpgradeToExpertAuthentication}
          />
        }
      />
    ),
    [
      filterAuthenticationTypes,
      handleItemPress,
      upgradeData,
      isUpgradeToExpertAuthentication,
      isGetNFT,
      getNFTDetails,
    ],
  );

  return (
    <View style={styles.authenticateContainer}>
      {renderList}
      <Sheet
        visible={sheetState.visible}
        setVisible={() => {
          setSheetState({visible: false, id: null});
        }}
        authenticationId={sheetState.id}
        authenticationTypes={authenticationTypes}
      />
    </View>
  );
}

function Sheet({
  visible = false,
  setVisible = () => null,
  authenticationId,
  authenticationTypes = [],
}) {
  const textContent = React.useMemo(
    function () {
      const authentication = authenticationTypes.find(
        auth => auth?.id == authenticationId,
      );
      switch (authenticationId) {
        case 1:
          return {
            title: authentication?.name ?? locale.CommunityVoted,
            description:
              'The Legiteem8 community will vote on the authenticity of your t-shirt and we’ll calculate an average score. Items that generate a crowdsourced score of at least 60% positive votes are considered a “pass.” Anything below that is considered a "fail." A certificate of authenticity is not issued for community-voted authentications due to the limited nature of the details required in the submission.',
          };
        case 2:
          return {
            title: authentication?.name ?? locale.ExpertCertified,
            description:
              'An expert will thoroughly review your submission using our desktop-based authentication portal. In this instance the user is required to have the t-shirt in their possession. Because there are higher requirements during the  submission process an app based certificate will be issued should the item prove to be authentic.',
          };
        case 3:
          return {
            title: authentication?.name ?? locale.ExpertCertifiedNFT,
            description:
              'An expert will thoroughly review your submission using our desktop-based authentication portal. In this instance, the user is required to have the t-shirt in their possession. Because there are higher requirements for the submission a certificate will be issued should the item prove to be authentic. Legiteem8 will then take care of minting your certificate and securely store it for you. Should you wish to transfer it to an external crypto wallet, our platform can accommodate that process.',
          };
        case 4:
          return {
            title: authentication?.name ?? locale.TagOnly,
            description:
              'You can help build our database of tags by uploading tags from t-shirts in your collection. Join our mission to create the world’s largest index of t-shirt tags which all users of Legiteem8 are able to access via our search module.',
          };
        case 5:
          return {
            title: authentication?.name ?? locale.ExpertFeedback,
            description:
              'In the event you want a second opinion on votes made by the community or on a private submission,  an expert will weigh in with their observations. No certificate is issued in this instance, as community submissions don’t have the stringent requirements like our certified tiers.',
          };
        default:
          return {
            title: '',
            description: '',
          };
      }
    },
    [authenticationId, authenticationTypes],
  );

  return (
    <BottomSheet
      visible={visible}
      setVisible={setVisible}
      automaticallyAdjustHeight
      key={authenticationId?.toString?.()}>
      <View style={{padding: 20}}>
        <Text style={styles.modalTitle}>{textContent.title}</Text>
        <Text style={styles.modal}>{textContent?.description}</Text>
      </View>
    </BottomSheet>
  );
}

export default Authenticate;
