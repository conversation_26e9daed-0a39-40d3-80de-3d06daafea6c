import {View} from 'react-native';
import React from 'react';
import {useRoute, useNavigation} from '@react-navigation/native';
import {useLazyQuery} from '@apollo/client';

import {GET_SINGLE_POST_FOR_UPGRADE} from '../../apollo/queries/index';
import colors from '../../assets/colors';
import {screens} from '../../assets/strings';
import ActivityIndicator from '../../components/ActivityIndicator';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: locale.Error,
    description: '',
  },
};

export default function withAuthenticationResubmission(
  WrappedComponent = null,
) {
  return props => {
    const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
    const [canProceed, setCanProceed] = React.useState(false);

    const navigation = useNavigation();
    const route = useRoute();
    const {isUpgradeToExpertAuthentication, upgradeData} = route.params ?? {};
    const {postId} = upgradeData ?? {};

    const [getPost, {data, error}] = useLazyQuery(GET_SINGLE_POST_FOR_UPGRADE, {
      variables: {
        id: upgradeData?.postId,
      },
    });

    const postData = data?.getPost?.data;

    React.useEffect(() => {
      if (isUpgradeToExpertAuthentication && postId) {
        getPost();
      } else {
        setCanProceed(true);
      }
    }, [postId, isUpgradeToExpertAuthentication, getPost]);

    React.useEffect(() => {
      if (data) {
        const isSuccess = data?.getPost?.success;
        const code = data?.getPost?.code;
        const message = data?.getPost?.message;
        if (isSuccess) {
          setCanProceed(true);
        } else {
          setPopup(previousState => ({
            ...previousState,
            state: true,
            isError: true,
            data: {
              title: locale.Error,
              description: code?.length
                ? message
                : locale.SomethingWentWrongPleaseTryAgain,
            },
          }));
        }
      }
      if (error) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: true,
          data: {
            title: locale.Error,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }));
      }
    }, [data, error]);

    return (
      <>
        {!canProceed ? (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: colors.white,
            }}>
            <ActivityIndicator size={30} />
          </View>
        ) : (
          <WrappedComponent
            {...props}
            {...{
              isUpgradeToExpertAuthentication,
              upgradeData: {authenticationData: upgradeData, postData},
            }}
          />
        )}
        <InfoModal
          delayInMs={300}
          setVisible={() => {
            navigation.navigate(screens.DRAW_NAVIGATOR);
            setPopup(previousState => ({
              ...previousState,
              state: false,
            }));
          }}
          popUp={popup}
        />
      </>
    );
  };
}
