import React, {useCallback, useEffect} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import '@walletconnect/react-native-compat';
import {useWeb3Modal, Web3Button, Web3Modal} from '@web3modal/react-native';
import {WALLET_CONNECT_PROJECT_ID} from '@env';
import {useDispatch, useSelector} from 'react-redux';

import Layout from '../../layout';
import Icon from '../../components/Icon';
import {icons} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import colors from '../../assets/colors';
import {setCryptoAddress} from '../../redux/actions/auth';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import PasteWalletAddress from './PasteWalletAddress';
import Button from '../../components/Button';

const providerMetadata = {
  name: 'Legiteem8',
  description:
    'Legiteem8 is a pioneering app for vintage t-shirt wearers and collectors looking to ensure their t-shirts are authentic.',
  url: 'https://www.legiteem8.app/',
  icons: ['https://www.legiteem8.app/wp-content/uploads/2022/07/356.jpg'],
};

const sessionParams = {
  namespaces: {
    eip155: {
      methods: [
        'eth_sendTransaction',
        'eth_signTransaction',
        'eth_sign',
        'personal_sign',
        'eth_signTypedData',
      ],
      chains: ['eip155:1', 'eip155:137'],
      events: ['chainChanged', 'accountsChanged'],
      rpcMap: {
        1: 'https://eth.public-rpc.com/',
        137: 'https://rpc.ankr.com/polygon',
      },
    },
  },
};

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

function App() {
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const {isConnected, address, open} = useWeb3Modal();

  const {cryptoAddress, isCryptoAddressManual = false} = useSelector(
    state => state?.auth,
  );
  const dispatch = useDispatch();

  const handleCryptoLogout = useCallback(async () => {
    if (isCryptoAddressManual) {
      return;
    }
    try {
      dispatch(
        setCryptoAddress({
          walletAddress: null,
          userData: null,
          isCryptoAddressManual: false,
        }),
      );
    } catch (error) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
        isError: true,
        data: {
          title: locale.Error,
          description: locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    }
  }, [dispatch, isConnected]);

  useEffect(() => {
    if (address?.length) {
      dispatch(
        setCryptoAddress({
          walletAddress: address,
          userData: {
            address,
          },
          isCryptoAddressManual: false,
        }),
      );
    } else {
      handleCryptoLogout();
    }
  }, [address, dispatch, handleCryptoLogout]);

  return (
    <Layout style={{padding: 20}} contentContainerStyle={{flex: 1}}>
      {cryptoAddress ? (
        <>
          <Text style={styles.header}>Your Connected Wallet</Text>
          <View style={styles.addressWrapper}>
            <Text
              numberOfLines={1}
              ellipsizeMode="middle"
              style={styles.address}>
              Address: {cryptoAddress}
            </Text>
          </View>
        </>
      ) : (
        <>
          <Text style={styles.header}>No wallet connected.</Text>
          <Text style={styles.subheader}>
            You're not required to connect an external wallet to use Legiteem8.
            This is just an added convenience if your tee is NFT certified and
            you wish to transfer the token externally.
          </Text>
          <View style={styles.emptyWalletMessage}>
            <Icon name={icons.WALLET} size={50} tint="#222222" />
            <Text style={styles.cryptoWalletMessage}>
              Once a wallet is connected, it will show here.
            </Text>
          </View>
        </>
      )}

      <View
        style={{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          backgroundColor: colors.white,
        }}>
        <PasteWalletAddress />
        {cryptoAddress?.length === 0 || cryptoAddress == null ? (
          <Text
            style={[
              styles.header,
              {color: colors.primary, textAlign: 'center'},
            ]}>
            OR
          </Text>
        ) : null}
        {!isCryptoAddressManual ? (
          <Web3Button style={styles.buttonStyle} />
        ) : null}
      </View>

      <Web3Modal
        projectId={WALLET_CONNECT_PROJECT_ID}
        providerMetadata={providerMetadata}
        sessionParams={sessionParams}
      />
      <InfoModal
        setVisible={() => {
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
    </Layout>
  );
}

export default App;

const styles = StyleSheet.create({
  mainBody: {
    justifyContent: 'center',
    padding: 25,
    backgroundColor: 'white',
    alignContent: 'center',
    flex: 1,
  },
  buttonStyle: {
    backgroundColor: '#327BE3',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
    marginTop: 20,
    width: '100%',
    minHeight: 50,
  },
  buttonTextStyle: {
    color: '#FFFFFF',
    paddingVertical: 10,
    fontSize: 16,
    fontWeight: 'bold',
  },
  addressWrapper: {
    marginVertical: spacings.lg,
    justifyContent: 'flex-start',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: colors.grey,
    paddingVertical: spacings.lg,
    paddingHorizontal: spacings.md,
  },
  header: {
    marginTop: 20,
    fontFamily: 'Gugi-Regular',
    fontSize: 20,
    color: colors.black,
  },
  subheader: {
    marginTop: 10,
    fontFamily: 'Inter-Medium',
    fontSize: 16,
    color: colors.darkGrey,
  },
  address: {
    color: 'black',
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
  },
  cryptoWalletMessage: {
    marginTop: spacings.lg,
    color: colors.darkGrey,
    fontSize: fontSize.md,
  },
  emptyWalletMessage: {
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 50,
    flex: 1,
  },
  leftIconWrapper: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
  },
});
