import React, {useState, useCallback, useLayoutEffect} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  TextInput,
  View,
  Pressable,
  Platform,
  FlatList,
  KeyboardAvoidingView,
} from 'react-native';
import dayjs from 'dayjs';
import {useQuery, useMutation, useLazyQuery} from '@apollo/client';
import {useIsFocused} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import ActivityIndicator from '../../components/ActivityIndicator';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import {
  SEND_CHAT_MESSAGE,
  SET_ALL_CHAT_MESSAGES_AS_READ,
} from '../../apollo/mutations';
import {
  GET_CHAT_MESSAGES,
  GET_PENDING_CHAT_MESSAGES,
} from '../../apollo/queries';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import colors from '../../assets/colors';
import {icons, userProfileScreen, persistent} from '../../assets/strings';
import Icon from '../../components/Icon';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import TextAutoLink from '../../components/TextAutoLink';

var relativeTime = require('dayjs/plugin/relativeTime');
dayjs.extend(relativeTime);

const LIMIT = 20;

const isIOS = Platform.OS == 'ios';

const ChatScreen = ({route, navigation}) => {
  const isFocused = useIsFocused();

  const cursorRef = React.useRef([]);
  const conversationUserId = route?.params?.params;
  const conversationUserUsername = route?.params?.username;

  const [isFetchingMore, setIsFetchingMore] = React.useState(false);
  const [chatData, setChatData] = React.useState(null);
  const [sendMessageLoading, setSendMessageLoading] = React.useState(null);

  const [sendViewHeight, setSendViewHeight] = React.useState(0);
  const {bottom: bottomOffset} = useSafeAreaInsets();

  const {data, fetchMore, loading, error, refetch} = useQuery(
    GET_CHAT_MESSAGES,
    {
      variables: {
        userId: parseInt(route?.params?.params, 10),
        limit: LIMIT,
      },
    },
  );

  const lastMessage = React.useMemo(
    () => (Array.isArray(chatData) ? chatData?.[0] : null),
    [chatData],
  );

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: data?.getChatMessages?.data?.edges[0]?.toUser?.username
        ? `${data?.getChatMessages?.data?.edges[0]?.toUser?.username}`
        : conversationUserUsername ?? ' ',
    });
  }, [
    data?.getChatMessages?.data?.edges,
    navigation,
    conversationUserUsername,
  ]);

  React.useEffect(() => {
    try {
      if (isFocused && !(conversationUserId == null)) {
        refetch?.();
        AsyncStorage.setItem(
          persistent.CURRENT_CONVERSATION_USER_ID,
          JSON.stringify(conversationUserId),
        );
      } else {
        AsyncStorage.removeItem(persistent.CURRENT_CONVERSATION_USER_ID);
      }
    } catch (_) {}
  }, [isFocused, conversationUserId, refetch]);

  const cursor = React.useMemo(
    () => data?.getChatMessages?.data?.pageInfo?.endCursor,
    [data?.getChatMessages?.data?.pageInfo?.endCursor],
  );

  const hasMore = React.useMemo(
    () => data?.getChatMessages?.data?.pageInfo?.hasMore,
    [data?.getChatMessages?.data?.pageInfo?.hasMore],
  );

  const handleFetchMore = useCallback(async () => {
    try {
      if (cursor !== null && hasMore && !cursorRef.current?.includes(cursor)) {
        cursorRef.current?.push(cursor);
        setIsFetchingMore(true);

        await fetchMore?.({
          query: GET_CHAT_MESSAGES,
          variables: {
            after: cursor,
            limit: LIMIT,
            userId: parseInt(conversationUserId, 10),
          },
          updateQuery(previousResult, {fetchMoreResult}) {
            setIsFetchingMore(false);
            const newCursor =
              fetchMoreResult?.getChatMessages?.data?.pageInfo?.endCursor;

            return {
              getChatMessages: {
                data: {
                  edges: [
                    ...previousResult?.getChatMessages?.data?.edges,
                    ...fetchMoreResult?.getChatMessages?.data?.edges,
                  ],
                  pageInfo: {
                    endCursor: newCursor,
                    hasMore:
                      fetchMoreResult?.getChatMessages?.data?.pageInfo?.hasMore,
                  },
                  __typename: previousResult?.getChatMessages?.__typename,
                },
                code: fetchMoreResult?.getChatMessages?.code,
                errors: fetchMoreResult?.getChatMessages?.errors,
                message: fetchMoreResult?.getChatMessages?.message,
                success: fetchMoreResult?.getChatMessages?.success,
              },
            };
          },
        });
        setIsFetchingMore(false);
      }
    } catch (error) {
      setIsFetchingMore(false);
    }
  }, [cursor, fetchMore, hasMore, conversationUserId]);

  React.useEffect(() => {
    if (Array.isArray(data?.getChatMessages?.data?.edges)) {
      setChatData(data?.getChatMessages?.data?.edges);
    }
  }, [data?.getChatMessages?.data?.edges]);

  return (
    <>
      {chatData ? (
        <KeyboardAvoidingView
          {...(isIOS
            ? {
                behavior: isIOS ? 'position' : 'height',
                keyboardVerticalOffset: sendViewHeight,
              }
            : {})}
          style={[styles.container, {marginBottom: bottomOffset}]}>
          <View>
            <ChatList
              chatData={chatData}
              handleFetchMore={handleFetchMore}
              isFetchingMore={isFetchingMore}
            />
          </View>
          <SendView
            setSendViewHeight={setSendViewHeight}
            toUserId={conversationUserId}
            setSendMessageLoading={setSendMessageLoading}
            sendMessageLoading={sendMessageLoading}
            lastMessage={lastMessage}
            setChatData={setChatData}
          />
        </KeyboardAvoidingView>
      ) : loading ? (
        <View style={styles.emptyContainer}>
          <ActivityIndicator color={colors.primary} />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorContainerText}>
            {userProfileScreen.SOMETHING_WENT_WRONG}
          </Text>
        </View>
      ) : null}
      <ReadStatus toUserId={conversationUserId} />
    </>
  );
};

const ChatList = React.memo(
  ({chatData = [], handleFetchMore = () => null, isFetchingMore = false}) => {
    return (
      <FlatList
        bounces={false}
        initialNumToRender={LIMIT}
        inverted={Platform.OS === 'ios'}
        data={chatData ?? []}
        contentContainerStyle={[
          styles.chatWrapper,
          chatData?.length == 0 ? {flex: 1} : {},
        ]}
        keyExtractor={item => item?.id?.toString?.()}
        style={{
          height: '100%',
          ...(Platform.OS === 'android' ? {scaleY: -1, paddingTop: 30} : {}),
        }}
        renderItem={({item}) => <ChatBubble item={item} />}
        ListEmptyComponent={
          <View
            style={{
              height: '70%',
              alignItems: 'center',
              ...(Platform.OS === 'android' ? {transform: [{scaleY: -1}]} : {}),
            }}>
            <DefaultEmptyComponent
              text={locale.YouHaveNotMessagesYet}
              lottieStyle={{width: 80, height: 80, alignSelf: 'center'}}
              textStyle={{fontSize: 13}}
            />
          </View>
        }
        ListFooterComponent={
          <>
            {isFetchingMore ? (
              <ActivityIndicator color={colors.primary} />
            ) : null}
            <View style={{margin: spacings.lg}} />
          </>
        }
        onEndReachedThreshold={0.8}
        onEndReached={handleFetchMore}
        scrollToOverflowEnabled={true}
        scrollEventThrottle={1900}
        showsVerticalScrollIndicator={false}
      />
    );
  },
);

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: locale.Error,
    description: locale.SomethingWentWrongPleaseTryAgain,
  },
};

const SendView = React.memo(
  ({
    setSendViewHeight = () => null,
    toUserId,
    setSendMessageLoading = () => null,
    sendMessageLoading = false,
    setChatData = () => null,
    lastMessage = null,
  }) => {
    const [text, setText] = useState('');

    const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

    const intervalRef = React.useRef();

    const [sendChatMessage] = useMutation(SEND_CHAT_MESSAGE);
    const [getPendingChatMessages, {refetch: refetchPending}] = useLazyQuery(
      GET_PENDING_CHAT_MESSAGES,
      {
        fetchPolicy: 'no-cache',
        variables: {
          userId: toUserId,
          cursor: `${lastMessage?.created_at}|${lastMessage?.id}`,
        },
      },
    );

    const handlePendingChats = React.useCallback(
      pendingChatsData => {
        const isSuccess = pendingChatsData?.getPendingChatMessages?.success;
        if (isSuccess) {
          const pendingChats = pendingChatsData?.getPendingChatMessages?.data;
          if (Array.isArray(pendingChats) && pendingChats?.length > 0) {
            setChatData(previousChats =>
              Array.isArray(previousChats)
                ? [...pendingChats, ...previousChats]
                : pendingChats,
            );
          }
        }
      },
      [setChatData],
    );

    const fetchPending = React.useCallback(async () => {
      try {
        const {data: pendingChatsData} = await (refetchPending
          ? refetchPending()
          : getPendingChatMessages());
        handlePendingChats(pendingChatsData);
      } catch (error) {}
    }, [getPendingChatMessages, handlePendingChats, refetchPending]);

    const sendMessage = useCallback(async () => {
      try {
        setSendMessageLoading(true);
        await sendChatMessage({
          variables: {
            toUserId,
            text: text,
            messageType: 'TEXT',
          },
        });
        const {data: pendingChats} = await getPendingChatMessages();
        handlePendingChats(pendingChats);
        setText('');
      } catch (e) {
        setPopup(previousState => ({...previousState, state: true}));
      } finally {
        setSendMessageLoading(false);
      }
    }, [
      sendChatMessage,
      text,
      getPendingChatMessages,
      handlePendingChats,
      toUserId,
    ]);

    React.useEffect(() => {
      if (sendMessageLoading && intervalRef.current) {
        clearInterval(intervalRef.current);
      } else {
        if (lastMessage?.id && lastMessage?.created_at) {
          intervalRef.current = setInterval(fetchPending, 5000);
        }
      }

      return () => {
        clearInterval(intervalRef.current);
      };
    }, [fetchPending, lastMessage, sendMessageLoading]);

    const isSendDisabled = text?.length == 0;

    return (
      <>
        <View
          style={[styles.inputWrapper]}
          onLayout={evt => {
            setSendViewHeight(evt?.nativeEvent?.layout?.height);
          }}>
          <TextInput
            style={[styles.input]}
            multiline={true}
            placeholder={userProfileScreen.PLACEHOLDER}
            value={text}
            onChangeText={setText}
            placeholderTextColor={colors.grey}
            selectionColor={colors.primary}
          />
          <View style={[styles.buttonWrapper, {paddingLeft: 10}]}>
            {sendMessageLoading ? (
              <ActivityIndicator color={colors.white} size={30} />
            ) : (
              <TouchableOpacity onPress={sendMessage} disabled={isSendDisabled}>
                <Icon
                  name={icons.SEND}
                  tint={colors.white}
                  size={30}
                  style={{opacity: isSendDisabled ? 0.5 : 1}}
                />
              </TouchableOpacity>
            )}
          </View>
          <InfoModal
            setVisible={() => setPopup(POPUP_INITIAL_STATE)}
            popUp={popup}
          />
        </View>
      </>
    );
  },
);

const ChatBubble = React.memo(({item = null}) => {
  const renderText = React.useMemo(
    () => (
      <TextAutoLink
        text={item?.text ?? ''}
        linkStyle={{color: colors.secondary}}
        mentionStyle={{color: colors.secondary}}
      />
    ),
    [item?.text],
  );

  return !item?.isFromMe ? (
    <Pressable
      style={[
        styles.revceivedBubble,
        Platform.OS === 'android' ? {scaleY: -1} : {},
      ]}>
      <>
        <View
          style={
            isIOS
              ? {
                  backgroundColor: '#EFEFEF',
                  paddingHorizontal: spacings.sm,
                  borderTopRightRadius: 10,
                  borderTopLeftRadius: 10,
                  borderBottomRightRadius: 10,
                }
              : {}
          }>
          <Text
            style={[
              styles.revceivedBubbleText,
              isIOS ? {backgroundColor: 'transparent'} : {},
            ]}>
            {renderText}
          </Text>
        </View>
        <Text style={styles.dateText}>
          {dayjs(item?.created_at)?.fromNow?.()}
        </Text>
      </>
    </Pressable>
  ) : (
    <Pressable
      style={[
        styles.sentBubble,
        Platform.OS === 'android' ? {scaleY: -1} : {},
      ]}>
      <>
        <View
          style={[
            styles.textWrapper,
            isIOS
              ? {
                  backgroundColor: colors.primary,
                  borderTopRightRadius: 15,
                  borderTopLeftRadius: 15,
                  borderBottomLeftRadius: 15,
                  paddingHorizontal: spacings.lg,
                  paddingVertical: spacings.md,
                }
              : {},
          ]}>
          <Text style={[isIOS ? {color: colors.white} : styles.sentBubbleText]}>
            {renderText}
          </Text>
        </View>
        <Text style={styles.dateText}>
          {dayjs(item?.created_at)?.fromNow?.()}
        </Text>
      </>
    </Pressable>
  );
});

function ReadStatus({toUserId}) {
  const [setAllChatMessagesAsRead] = useMutation(SET_ALL_CHAT_MESSAGES_AS_READ);

  React.useEffect(() => {
    (async function () {
      try {
        await setAllChatMessagesAsRead({
          variables: {
            userId: +toUserId,
          },
        });
      } catch (e) {}
    })();
  }, [toUserId]);

  return null;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  inputWrapper: {
    width: '100%',
    backgroundColor: colors.black,
    position: 'absolute',
    bottom: 0,
    paddingVertical: spacings.lg,
    paddingHorizontal: spacings.md,
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatWrapper: {
    paddingBottom: 60,
    paddingTop: 80,
  },
  input: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: colors.secondaryGrey,
    marginHorizontal: spacings.sm,
    paddingLeft: 10,
    color: colors.white,
    textAlignVertical: 'top',
    fontFamily: 'Inter-Medium',
    paddingVertical: 8,
    ...(isIOS
      ? {
          minHeight: 40,
          paddingLeft: spacings.md,
          flex: 1,
          justifyContent: 'center',
          paddingTop: 12,
        }
      : {}),
  },
  trash: {
    paddingVertical: spacings.sm,
    paddingHorizontal: spacings.lg,
  },
  buttonWrapper: {
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  button: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  revceivedBubble: {
    width: '75%',
    alignItems: 'flex-start',
    paddingHorizontal: spacings.lg,
    marginBottom: spacings.lg,
  },
  revceivedBubbleText: {
    color: colors.black,
    backgroundColor: '#EFEFEF',
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  sentBubble: {
    alignSelf: 'flex-end',
    width: '75%',
    alignItems: 'flex-end',
    marginBottom: spacings.lg,
    paddingHorizontal: spacings.lg,
  },
  sentBubbleText: {
    overflow: 'hidden',
    color: colors.white,
    backgroundColor: colors.primary,
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15,
    borderBottomLeftRadius: 15,
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
  },
  noMessages: {
    color: colors.black,
    fontSize: fontSize.md,
  },
  dateText: {
    fontSize: 10,
    color: colors.darkGrey,
  },
  sentImageStyle: {
    borderTopRightRadius: 25,
    borderTopLeftRadius: 25,
    borderBottomLeftRadius: 25,
    marginBottom: spacings.sm,
  },
  receivedImageStyle: {
    borderTopRightRadius: 25,
    borderTopLeftRadius: 25,
    borderBottomRightRadius: 25,
    marginBottom: spacings.sm,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.darkBadge,
  },
  visibleSection: {
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingTop: spacings.xxl,
    marginHorizontal: spacings.xl,
    borderRadius: 4,
  },
  modalButtonWrapper: {
    flexDirection: 'row',
  },
  modalButton: {
    flex: 1 / 2,
    marginTop: spacings.xl,
    paddingVertical: spacings.md,
    paddingHorizontal: spacings.xl,
    justifyContent: 'center',
    alignItems: 'center',
  },
  prompt: {
    color: colors.black,
    fontSize: fontSize.lg,
  },
  label: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
  selectedBubbleText: {
    backgroundColor: colors.black,
  },
  textWrapper: {
    flexDirection: 'row',
  },
});

export default ChatScreen;
