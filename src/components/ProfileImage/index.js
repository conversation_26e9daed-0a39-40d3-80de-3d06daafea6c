import React from 'react';
import {View} from 'react-native';

import styles from './styles';
import FastImage from '../FastImage';
import PlaceholderImage from '../../assets/Images/avatar.png';
import ActivityIndicator from '../ActivityIndicator';
import ImageViewer from '../ImageViewer';
import TouchAwareButton from '../Button/TouchAwareButton';

export default function ProfileImage({
  size = 40,
  url = '',
  style = {},
  containerStyle = {},
  isLoading = false,
  canViewImage = false,
  fallbackImage,
}) {
  return (
    <>
      {isLoading ? (
        <View
          style={[
            styles.wrapper,
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              justifyContent: 'center',
              alignItems: 'center',
            },
          ]}>
          <ActivityIndicator size={size / 2} />
        </View>
      ) : (
        <ImageViewerComponent
          canViewImage={canViewImage}
          size={size}
          style={style}
          containerStyle={containerStyle}
          url={url}
          fallbackImage={fallbackImage}
        />
      )}
    </>
  );
}

function ImageViewerComponent({
  size = 40,
  style = {},
  containerStyle = {},
  url = '',
  canViewImage = false,
  fallbackImage = PlaceholderImage,
}) {
  const [viewerVisible, setViewerVisible] = React.useState(false);

  return (
    <>
      <TouchAwareButton
        containerStyle={{...style, ...containerStyle}}
        style={[{opacity: 1}]}
        disabled={!canViewImage || url == null || url?.length == 0}
        onPress={() => setViewerVisible(true)}>
        <FastImage
          style={[
            styles.wrapper,
            {width: size, height: size, borderRadius: size / 2},
            style,
          ]}
          source={url?.length ? {uri: url} : fallbackImage}
          containerStyle={{borderRadius: size / 2}}
        />
      </TouchAwareButton>
      <ImageViewer
        visible={viewerVisible}
        setVisible={setViewerVisible}
        images={[{url: url}]}
      />
    </>
  );
}
