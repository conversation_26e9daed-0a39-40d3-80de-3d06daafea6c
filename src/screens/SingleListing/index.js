import React, {useEffect} from 'react';
import {View, ScrollView} from 'react-native';
import {useQuery} from '@apollo/react-hooks';
import {useFocusEffect} from '@react-navigation/native';

import SingleListingsLoader from '../../components/Loaders/SingleListingsLoader';
import {GET_SINGLE_POST} from '../../apollo/queries';
import {styles} from './styles';
import ImageSection from './ImageSection';
import UserCard from './UserCard';
import StatsCard from './StatsCard';
import DescriptionCard from './DescriptionCard';
import VoteButton from './VoteButton';
import CommunityVotedDescriptionCard from './CommunityVotedDescriptionCard';
import CommunityVotedImageSection from './CommunityVotedImageSection';
import CommunityVotedUserCard from './CommunityVotedUserCard';
import CommunityVotedStatsCard from './CommunityVotedStatsCard';
import {useUser} from '../../tools/hooks';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import locale from '../../assets/locale.json';
import RefreshControl from '../../components/RefreshControl';
import scale from '../../tools/scale';
import InfoModal from '../../components/Modal/InfoModal';
import {screens} from '../../assets/strings';
import ProvenanceTagging from './ProvenanceTagging';
import ShareTask from './PostShare';

const SingleListing = ({route, navigation}) => {
  const postId = route.params?.params;
  const authenticationKey = route?.params?.key;

  const [refreshing, setRefreshing] = React.useState(false);



const variables = {
  ...(!(postId == null) ? { id: +postId } : { key: authenticationKey }),
};

// Log the query and variables
console.log("Variables:", variables);


  const [
    postNotFoundModalVisible,
    setPostNotFoundModalVisible,
  ] = React.useState(false);

  const {data, refetch, loading, error} = useQuery(GET_SINGLE_POST, {
    variables: {
      ...(!(postId == null) ? {id: +postId} : {key: authenticationKey}),
    },
  });

  useFocusEffect(
    React.useCallback(() => {
      try {
        refetch?.();
      } catch (error) {}
    }, [refetch]),
  );

  const {user: me} = useUser();

  const post = data?.getPost?.data;
  const authenticationType = post?.authentication_type?.id;
  const postAuthorId = post?.user?.id;
  const postTitle = post?.title;
  const authenticationKeyUpdated = post?.authentication_key;
  const isAuthenticationPending = post?.authentication_status === 'PENDING';

  const isPostNotFound = React.useMemo(
    () =>
      (!(data?.getPost?.success == null) && !data?.getPost?.success) ||
      (postId == null && authenticationKey == null),
    [data?.getPost?.success, postId, authenticationKey],
  );

  React.useEffect(() => {
    if (isPostNotFound) {
      setPostNotFoundModalVisible(true);
    }
  }, [isPostNotFound]);

  useEffect(() => {
    navigation.setOptions({
      title: postTitle ?? ' ',
      headerTitleContainerStyle: {maxWidth: `${scale.moderateScale(60)}%`},
    });
  }, [navigation, postTitle]);

  const isPostByMe = React.useMemo(() => me?.id == postAuthorId, [
    postAuthorId,
    me?.id,
  ]);

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  const handlePostNotFoundNavigation = React.useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.reset({
        index: 1,
        routes: [{name: screens.DRAW_NAVIGATOR}],
      });
    }
    setPostNotFoundModalVisible(false);
  }, [navigation]);

  const canShowVoteButton = React.useMemo(
    () => !isPostByMe && me?.role !== 'EXPERT' && authenticationType == 1,
    [isPostByMe, me?.role, authenticationType],
  );

  const canShowProvenanceTagging = React.useMemo(
    () => !isAuthenticationPending && +authenticationType !== 5,
    [isAuthenticationPending, authenticationType],
  );

  React.useLayoutEffect(() => {
    if (!isAuthenticationPending) {
      navigation.setOptions({
        ...(navigation.canGoBack()
          ? {}
          : {
              headerLeft: () => (
                <Icon
                  name="cancel_x"
                  tint={colors.white}
                  style={{marginLeft: 10}}
                  containerStyle={{padding: 10}}
                  size={15}
                  clickable
                  onPress={() =>
                    navigation.reset({
                      index: 1,
                      routes: [{name: screens.DRAW_NAVIGATOR}],
                    })
                  }
                />
              ),
            }),
      });
    }
  }, [navigation, isAuthenticationPending]);

  const isLoading = loading && data == null;

  if (isLoading || error) {
    return <SingleListingsLoader />;
  }

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl onRefresh={onRefresh} refreshing={refreshing} />
        }>
        {authenticationType == 1 ? (
          <>
            <CommunityVotedImageSection data={data} />
            <CommunityVotedUserCard
              data={data}
              refetchPost={refetch}
              renderShare={
                <ShareTask
                  authenticationKey={authenticationKeyUpdated}
                  postTitle={postTitle}
                  isAuthenticationPending={isAuthenticationPending}
                  imageUrl={
                    post?.has_certificate &&
                    post?.certificate_image_url?.length > 0
                      ? post?.certificate_image_url
                      : null
                  }
                  authenticationType={post?.authentication_type?.id}
                />
              }
            />
            <CommunityVotedStatsCard data={data} />
            <CommunityVotedDescriptionCard
              data={data}
              provenanceTaggingComponent={
                canShowProvenanceTagging ? (
                  <ProvenanceTagging
                    postAuthorId={postAuthorId}
                    postId={post?.id}
                    refetchPost={refetch}
                    shouldRefresh={refreshing}
                  />
                ) : null
              }
            />
          </>
        ) : (
          <>
            <ImageSection data={data} />
            <UserCard
              data={data}
              refetchPost={refetch}
              renderShare={
                <ShareTask
                  authenticationKey={authenticationKeyUpdated}
                  postTitle={postTitle}
                  isAuthenticationPending={isAuthenticationPending}
                  imageUrl={
                    post?.has_certificate &&
                    post?.certificate_image_url?.length > 0
                      ? post?.certificate_image_url
                      : null
                  }
                  authenticationType={post?.authentication_type?.id}
                />
              }
            />
            <StatsCard data={data} />
            <DescriptionCard
              data={data}
              provenanceTaggingComponent={
                canShowProvenanceTagging ? (
                  <ProvenanceTagging
                    postAuthorId={postAuthorId}
                    postId={post?.id}
                    refetchPost={refetch}
                    shouldRefresh={refreshing}
                  />
                ) : null
              }
            />
          </>
        )}
      </ScrollView>

      {canShowVoteButton ? <VoteButton data={data} refetch={refetch} /> : null}

      <InfoModal
        delayInMs={100}
        setVisible={handlePostNotFoundNavigation}
        popUp={{
          state: postNotFoundModalVisible,
          isError: true,
          data: {
            title: locale.PostNotFound,
            description:
              locale.ThePostYouAreTryingToFindMayNotBeActiveOrIsDeleted,
          },
        }}
      />
    </View>
  );
};

export default SingleListing;
