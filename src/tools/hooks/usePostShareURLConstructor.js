import React from 'react';
import {POST_SHARE_ENDPOINT, DOMAIN} from '@env';

import locale from '../../assets/locale.json';

export default function usePostShareURLConstructor({
  authenticationKey = null,
  postTitle = '',
  authenticationType = null,
}) {
  return {
    isValid: authenticationKey?.length > 0,
    url: `${DOMAIN}${POST_SHARE_ENDPOINT?.replace(
      ':key',
      authenticationKey ?? '',
    )}`,
    deepLink: `post://auth8/app/post/${authenticationKey}`,
    displayTitle: `${postTitle ?? 'Post'} | Legiteem8 ${
      authenticationType == 1
        ? `\n\n${locale['TheseAreCommunityResultsAndHaveNotBeen...']}`
        : ''
    }`,
  };
}
