import React, {useState, useCallback} from 'react';
import {View, FlatList, Text, TouchableOpacity} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import HomeListingsLoader from '../../components/Loaders/HomeListingsLoader';
import {GET_ALL_LISTING_HOME_TAB} from '../../apollo/queries';
import {spacings} from '../../assets/spacing';
import ActivityCard from '../../components/ActivityCard';
import RecentItems from './RecentItems';
import {screens} from '../../assets/strings';
import {styles} from './styles';
import AccountCreatedModal from './AccountCreatedModal';
import {DefaultErrorComponent} from '../../components/QueryManager';
import locale from '../../assets/locale.json';
import {recentItemsStyles} from './styles';
import RefreshControl from '../../components/RefreshControl/index';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import Button from '../../components/Button';
import colors from '../../assets/colors';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import {useInteractionManager} from '../../tools/hooks';

const LIMIT = 20;

const Index = ({navigation}) => {
  const [refreshing, setRefreshing] = useState(false);
  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const {data, refetch, loading, error} = useQuery(GET_ALL_LISTING_HOME_TAB, {
    variables: {
      filters: {
        show_pinned_items: true,
      },
      limit: LIMIT,
      cacheId: APOLLO_CACHE_IDS.getPosts_HOME_TAB,
    },
  });

  useFocusEffect(
    React.useCallback(() => {
      try {
        refetch?.();
      } catch (error) {}
    }, [refetch]),
  );

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  const renderItem = React.useCallback(
    ({item}) => (
      <TouchAwareButton
        onPress={() => {
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item.id,
          });
        }}>
        <TouchAwareButton.View>
          <ActivityCard item={item} />
        </TouchAwareButton.View>
      </TouchAwareButton>
    ),
    [navigation],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  return (
    <View style={styles.container}>
      <FlatList
        data={isInteractionsComplete ? data?.getPosts?.data?.edges : null}
        columnWrapperStyle={styles.contentContainer}
        style={{paddingTop: spacings.lg}}
        renderItem={renderItem}
        keyExtractor={extractKey}
        numColumns={2}
        initialNumToRender={10}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListHeaderComponent={
          <>
            <RecentItems refreshing={refreshing} />
            <View
              style={[
                recentItemsStyles.labelBar,
                {marginTop: spacings.lg, marginBottom: 10},
              ]}>
              <Text style={recentItemsStyles.label}>
                {locale.LatestLegitChecks}
              </Text>
              <TouchableOpacity
                onPress={() => navigation.navigate(screens.ALL_LISTINGS)}>
                <Text
                  style={[
                    recentItemsStyles.label,
                    {
                      textDecorationLine: 'underline',
                      fontFamily: 'Inter-Medium',
                      paddingLeft: 30,
                    },
                  ]}>
                  {locale.All}
                </Text>
              </TouchableOpacity>
            </View>
          </>
        }
        ListFooterComponent={
          <View style={{minHeight: 100}}>
            {!isInteractionsComplete || (data == null && loading) ? (
              <HomeListingsLoader />
            ) : error ? (
              <DefaultErrorComponent
                onRetryIconPress={onRefresh.bind(null, false)}
                style={{marginTop: 30}}
                customErrorText={locale.CouldNotFetchLatestListing}
                errorLottieStyle={{width: 60, height: 60}}
              />
            ) : (
              <Button
                text={locale.AllLegitChecks}
                textStyle={{
                  color: colors.primary,
                  fontFamily: 'Gugi-Regular',
                }}
                style={{backgroundColor: 'transparent', marginTop: 20}}
                onPress={() => navigation.navigate(screens.ALL_LISTINGS)}
              />
            )}
            <View style={{margin: spacings.lg}} />
          </View>
        }
        showsVerticalScrollIndicator={false}
      />

      <AccountCreatedModal />
    </View>
  );
};

export default Index;
