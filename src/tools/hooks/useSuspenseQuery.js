import React from 'react';
import {useApolloClient} from '../../apollo/index';

function queryWrapper(query, options = {}) {
  let status = 'pending';
  let result = null;

  const {client} = useApolloClient();

  const suspender = client
    .query({
      query,
      fetchPolicy: 'network-only',
      ...options,
    })
    .then(response => {
      status = 'success';
      result = response.data;
    })
    .catch(err => {
      status = 'error';
      result = err;
    });

  return {
    read() {
      if (status === 'pending') {
        throw suspender;
      } else if (status === 'error') {
        throw {result, suspender};
      } else {
        return {result, suspender};
      }
    },
  };
}

const useSuspenseQuery = (query, options = {}) => {
  const data = React.useMemo(() => {
    return queryWrapper(query, options);
  }, [query]);

  return {data};
};

export default useSuspenseQuery;
