import React from 'react';
import {
  Text,
  View,
  TextInput,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import PasswordErrorHandler from './PasswordErrorHandler';

const {width: WIDTH} = Dimensions.get('window');
const isIOS = Platform.OS == 'ios';

const CustomInput = props => {
  const {
    children = null,
    field: {name, onBlur, onChange, value},
    form: {errors, touched, setFieldTouched},
    ...inputProps
  } = props;

  const hasError = errors[name] && touched[name];

  return (
    <>
      <View style={[styles.container, hasError ? styles.errorInput : {}]}>
        <TextInput
          style={styles.textInput}
          autoCorrect={false}
          autoCapitalize="none"
          value={value}
          placeholderTextColor={colors.grey}
          selectionColor={colors.primary}
          onChangeText={text => onChange(name)(text)}
          onBlur={() => {
            setFieldTouched(name);
            onBlur(name);
          }}
          textContentType="none"
          {...inputProps}
        />
        {children}
      </View>
      <View>
        {hasError === true && name === 'name_of_shirt' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'brand' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'pit_to_pit_measurements' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'rear_collar_to_bottom' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'stitching' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'category' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'decade' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'condition' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}

        {hasError === true && name === 'label_size' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'material' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'condition_description' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'provenance' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'email' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
        {hasError === true && name === 'loginpassword' ? (
          <Text style={styles.errorText}>{errors[name]}</Text>
        ) : null}
      </View>
      {name === 'password' ? (
        <PasswordErrorHandler errors={errors} value={value} />
      ) : null}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: spacings.sm,
    width: WIDTH * 0.9,
    ...(isIOS ? {marginTop: 5} : {}),
  },
  textInput: {
    height: spacings.xxxl,
    flex: 1,
  },
  errorText: {
    fontSize: 9,
    color: colors.warning,
    width: '100%',
    position: 'absolute',
    marginTop: spacings.sm,
  },
  errorInput: {
    borderColor: colors.warning,
  },
});

export default CustomInput;
