import React, {useState} from 'react';
import {TouchableOpacity, Text, View, Linking} from 'react-native';
import {useMutation} from '@apollo/client';
import {useNavigation} from '@react-navigation/native';

import {FOLLOW_USER, UNFOLLOW_USER} from '../../apollo/mutations';
import {styles} from './styles';
import {icons, screens, userProfileScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import Icon from '../../components/Icon';
import {stripHTTPSFromURL, formatURL} from '../../tools/utils';
import ProfileImage from '../../components/ProfileImage';
import locale from '../../assets/locale.json';
import FastImage from '../../components/FastImage';
import ActivityIndicator from '../../components/ActivityIndicator';
import InfoModal from '../../components/Modal/InfoModal';
import {fixedNumberTo} from '../../tools/utils';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: locale.Error,
    description: locale.SomethingWentWrongPleaseTryAgain,
  },
};

const Header = ({id, data = null, refetch = () => null}) => {
  const navigation = useNavigation();

  const [isLoading, setIsLoading] = useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [isFollowing, setIsFollowing] = React.useState(data?.doIFollow);

  const [followUser] = useMutation(FOLLOW_USER);
  const [unfollowUser] = useMutation(UNFOLLOW_USER);

  const handleFollowUser = async () => {
    setIsLoading(true);
    try {
      const {data} = await followUser({
        variables: {
          userId: id,
        },
      });
      const isSuccess = data?.followUser?.success;
      const code = data?.followUser?.code;
      const doIAlreadyFollow = !isSuccess && code == 'USER_ALREADY_FOLLOWED';
      if (isSuccess) {
        await refetch?.();
        setIsFollowing(true);
      } else {
        if (doIAlreadyFollow) {
          setIsFollowing(true);
          setIsLoading(false);
          return;
        }
        throw new Error();
      }
    } catch (_) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnfollowUser = async () => {
    setIsLoading(true);
    try {
      const {data} = await unfollowUser({
        variables: {
          userId: id,
        },
      });
      const isSuccess = data?.unfollowUser?.success;
      const code = data?.unfollowUser?.code;
      const doINotFollow = !isSuccess && code == 'USER_NOT_FOLLOWED';
      if (isSuccess) {
        await refetch?.();
        setIsFollowing(false);
      } else {
        if (doINotFollow) {
          setIsFollowing(false);
          setIsLoading(false);
          return;
        }
        throw new Error();
      }
    } catch (_) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
      }));
    } finally {
      setIsLoading(false);
    }
  };
  const socialMediaUrl = data?.social_media?.[0]?.url;

  const handleLinking = async () => {
    try {
      await Linking.openURL(formatURL(socialMediaUrl));
    } catch (_) {}
  };

  return (
    <View style={styles.headerContainer}>
      <View style={styles.imageWrapper}>
        <ProfileImage url={data?.image} size={70} canViewImage />
        <View style={styles.textSection}>
          <View style={{marginRight: 10}}>
            <Text style={styles.username}>{data?.username}</Text>
            <View style={styles.levelsWrapper}>
              <View style={styles.levelsButton}>
                <Text style={styles.levelsText}>{data?.badge?.name}</Text>
              </View>
            </View>
          </View>
          <FastImage
            source={{uri: data?.badge?.image_url ?? ''}}
            style={styles.badge}
          />
        </View>
      </View>
      <View style={styles.descWrapper}>
        {data?.bio ? (
          <Text style={styles.desc} numberOfLines={5}>
            {data?.bio}{' '}
          </Text>
        ) : null}
        {socialMediaUrl ? (
          <TouchableOpacity onPress={handleLinking}>
            <Text style={styles.link} numberOfLines={1}>
              {stripHTTPSFromURL(socialMediaUrl)}
            </Text>
          </TouchableOpacity>
        ) : null}
      </View>
      <View style={styles.popularity}>
        <TouchableOpacity
          style={styles.popularityWrapper}
          onPress={() =>
            navigation.navigate(screens.FOLLOWING_LISTING, {params: id})
          }>
          <Text style={styles.popularityStat}>
            {data?.followingsCount ?? 0}
          </Text>
          <Text style={styles.desc}>{userProfileScreen.FOLLOWING}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.popularityWrapper}
          onPress={() =>
            navigation.navigate(screens.FOLLOWERS_LISTING, {params: id})
          }>
          <Text style={styles.popularityStat}>{data?.followersCount ?? 0}</Text>
          <Text style={styles.desc}>{userProfileScreen.FOLLOWERS}</Text>
        </TouchableOpacity>
        <View style={styles.popularityWrapper}>
          <Text style={styles.popularityStat}>
            {data?.number_of_votes ?? 0}
          </Text>
          <Text style={styles.desc}>{locale.Votes}</Text>
        </View>
        <View style={styles.popularityWrapper}>
          <Text style={styles.popularityStat}>
            {fixedNumberTo(data?.accuracy ?? 0, {
              onlyFixIfItHasDecimal: true,
            })}
            {data?.accuracy === 0 || data?.accuracy == null ? null : '%'}
          </Text>
          <Text style={styles.desc}>{userProfileScreen.ACCURACY}</Text>
        </View>
      </View>
      <View style={styles.socialButtonsWrapper}>
        {isFollowing ? (
          <TouchableOpacity
            onPress={handleUnfollowUser}
            style={[styles.socialButtons, {backgroundColor: colors.primary}]}>
            {isLoading ? (
              <ActivityIndicator color={colors.white} size={19} />
            ) : (
              <>
                <Icon
                  name={icons.PASS_ICON}
                  tint={colors.white}
                  size={fontSize.md}
                  style={{marginRight: 5}}
                />

                <Text style={styles.socialButtonsText}>
                  {userProfileScreen.FOLLOWING}
                </Text>
              </>
            )}
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.socialButtons}
            onPress={handleFollowUser}>
            {isLoading ? (
              <ActivityIndicator color={colors.white} size={19} />
            ) : (
              <>
                <Icon
                  name={icons.ADD}
                  tint={colors.white}
                  size={fontSize.md}
                  style={{marginRight: 5}}
                />
                <Text style={styles.socialButtonsText}>
                  {userProfileScreen.FOLLOW}
                </Text>
              </>
            )}
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={styles.socialButtons}
          onPress={() =>
            navigation.navigate(screens.CHAT_SCREEN, {
              params: id,
              username: data?.username,
            })
          }>
          <Icon
            name={icons.MESSENGER_READ}
            tint={colors.white}
            size={fontSize.md}
            style={{marginRight: 5}}
          />
          <Text style={styles.socialButtonsText}>
            {userProfileScreen.MESSAGE}
          </Text>
        </TouchableOpacity>
      </View>
      <InfoModal
        popUp={{state: popup.state, isError: true, data: popup.data}}
        setVisible={() => setPopup(POPUP_INITIAL_STATE)}
      />
    </View>
  );
};

export default Header;
