import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  Pressable,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {showAuthenticationScore} from '../../redux/actions/uploadMedia';
import Icon from '../../components/Icon';
import CommonModal from '../../components/Modal';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import {screens} from '../../assets/strings';

const ExpertFeedbackOptions = ({route, navigation}) => {
  const [visible, setVisible] = useState(false);
  const [modalTwoVisible, setModalTwoVisible] = useState(false);
  const [isItemOwner, setIsItemOwner] = useState(null);
  const [toBePublic, setToBePublic] = useState(null);
  const [error, setError] = useState(null);
  const dispatch = useDispatch();

  const handleChoice = value => {
    switch (value) {
      case 'a1':
        dispatch(showAuthenticationScore(true));
        setVisible(!visible);
        setToBePublic(true);
        return;
      case 'a2':
        dispatch(showAuthenticationScore(false));
        setVisible(!visible);
        setToBePublic(false);
        return;
      case 'b1':
        setIsItemOwner(true);
        setModalTwoVisible(!modalTwoVisible);
        return;
      case 'b2':
        setIsItemOwner(false);
        setModalTwoVisible(!modalTwoVisible);
        return;
      default:
        return;
    }
  };

  const handleNavigation = () => {
    if (isItemOwner !== null && toBePublic !== null) {
      setError(null);
      if (isItemOwner === false) {
        let value = 5;
        navigation.navigate(screens.ADD_PHOTOS, {
          authenticationType: value,
          setPublic: toBePublic,
        });
      } else {
        let value = 2;
        navigation.navigate(screens.ADD_PHOTOS, {
          authenticationType: value,
          setPublic: toBePublic,
        });
      }
    } else {
      setError('Please fill in all the required fields');
    }
  };

  return (
    <View style={styles.container}>
      <CommonModal visible={visible} setVisible={setVisible} height={100}>
        <View style={styles.optionsWrapper}>
          <Pressable
            style={styles.optionIconWrapper}
            onPress={() => handleChoice('a1')}>
            <Text style={[styles.options, {color: colors.primary}]}>Yes</Text>
            {toBePublic ? (
              <Icon name="checked_radio" size={20} tint={colors.primary} />
            ) : (
              <Icon name="unchecked_radio" size={20} tint={colors.grey} />
            )}
          </Pressable>
          <Pressable
            style={styles.optionIconWrapper}
            onPress={() => handleChoice('a2')}>
            <Text style={[styles.options, {color: colors.warning}]}>No</Text>
            {toBePublic !== false || toBePublic === null ? (
              <Icon name="unchecked_radio" size={20} tint={colors.grey} />
            ) : (
              <Icon name="checked_radio" size={20} tint={colors.primary} />
            )}
          </Pressable>
        </View>
      </CommonModal>
      <CommonModal
        visible={modalTwoVisible}
        setVisible={setModalTwoVisible}
        height={100}>
        <View style={styles.optionsWrapper}>
          <Pressable
            style={styles.optionIconWrapper}
            onPress={() => handleChoice('b1')}>
            <Text style={[styles.options, {color: colors.primary}]}>Yes</Text>
            {isItemOwner ? (
              <Icon name="checked_radio" size={20} tint={colors.primary} />
            ) : (
              <Icon name="unchecked_radio" size={20} tint={colors.grey} />
            )}
          </Pressable>
          <Pressable
            style={styles.optionIconWrapper}
            onPress={() => handleChoice('b2')}>
            <Text style={[styles.options, {color: colors.warning}]}>No</Text>
            {isItemOwner !== false || isItemOwner === null ? (
              <Icon name="unchecked_radio" size={20} tint={colors.grey} />
            ) : (
              <Icon name="checked_radio" size={20} tint={colors.primary} />
            )}
          </Pressable>
        </View>
      </CommonModal>
      <Text style={styles.title}>
        Choose The Kind of Expert Feedback You Would Like.
      </Text>
      <View style={styles.filterWrapper}>
        <Text style={styles.label}>
          <Text style={{color: colors.warning}}>*</Text> Would You Like Your
          Results To Be Public?
        </Text>
        <TouchableOpacity
          style={styles.filter}
          onPress={() => setVisible(!visible)}>
          <Text
            style={[
              styles.filterText,
              {color: toBePublic !== null ? colors.black : colors.darkGrey},
            ]}>
            {toBePublic === null ? '- select -' : toBePublic ? 'Yes' : 'No'}
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.filterWrapper}>
        <Text style={styles.label}>
          <Text style={{color: colors.warning}}>*</Text> Do You Already Own This
          Item?
        </Text>
        <TouchableOpacity
          style={styles.filter}
          onPress={() => setModalTwoVisible(!modalTwoVisible)}>
          <Text
            style={[
              styles.filterText,
              {
                color: isItemOwner !== null ? colors.black : colors.darkGrey,
              },
            ]}>
            {isItemOwner === null ? '- select -' : isItemOwner ? 'Yes' : 'No'}
          </Text>
        </TouchableOpacity>
      </View>
      <View>
        {error ? <Text style={styles.error}>{error}</Text> : null}
        <TouchableOpacity style={styles.button} onPress={handleNavigation}>
          <Text style={styles.buttonText}>Next: Add Photos</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  button: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    backgroundColor: colors.primary,
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
  filter: {
    borderWidth: 1,
    borderColor: colors.darkGrey,
    paddingVertical: spacings.md,
    paddingHorizontal: spacings.md,
    borderRadius: 4,
  },
  title: {
    marginVertical: spacings.lg,
    fontSize: fontSize.lg,
    color: colors.black,
    fontWeight: 'bold',
  },
  label: {
    color: colors.black,
    marginBottom: spacings.sm,
  },
  error: {
    color: colors.warning,
    fontSize: fontSize.xs,
    marginBottom: spacings.sm,
  },
  optionIconWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterText: {
    color: colors.darkGrey,
    fontSize: fontSize.lg,
  },
  filterWrapper: {
    marginBottom: spacings.lg,
  },
  optionsWrapper: {
    width: '100%',
  },
  options: {
    fontSize: fontSize.lg,
    marginVertical: spacings.md,
  },
});

export default ExpertFeedbackOptions;
