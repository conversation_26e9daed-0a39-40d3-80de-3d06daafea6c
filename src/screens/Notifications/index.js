import React, {useState, useEffect, useCallback} from 'react';
import {View, Text, Switch} from 'react-native';
import {useMutation} from '@apollo/client';
import {useIsFocused} from '@react-navigation/native';

import NotificationsLoader from '../../components/Loaders/NotificationsLoader';
import {UPDATE_PUSH_NOTIFICATION_SETTING} from '../../apollo/mutations';
import {notificationsScreen} from '../../assets/strings';
import {styles} from './styles';
import colors from '../../assets/colors';
import {useUserPushNotificationSettings} from '../../tools/hooks';

const Index = () => {
  const [directPush, setDirectPush] = useState(false);
  const [postReactionPush, setPostReactionPush] = useState(false);
  const [submissionPush, setSubmissionPush] = useState(false);
  const [accountFollowPush, setAccountFollowPush] = useState(false);

  const [directInApp, setDirectInApp] = useState(false);
  const [postReactionInApp, setPostReactionInApp] = useState(false);
  const [submissionInApp, setSubmissionInApp] = useState(false);
  const [accountFollowInApp, setAccountFollowInAp] = useState(false);

  const isFocused = useIsFocused();

  const timeoutIdRef = React.useRef();

  const {
    userPushNotificationSetting,
    isLoading: loading,
    refresh,
    syncNotificationToStorage,
  } = useUserPushNotificationSettings({focused: isFocused});

  const [updatePushNotificationSetting] = useMutation(
    UPDATE_PUSH_NOTIFICATION_SETTING,
  );

  useEffect(() => {
    setDirectPush(userPushNotificationSetting?.allow_direct_message);
    setPostReactionPush(userPushNotificationSetting?.allow_post_reaction);
    setSubmissionPush(
      userPushNotificationSetting?.allow_authentication_submission_status,
    );
    setAccountFollowPush(userPushNotificationSetting?.allow_follow_status);
    setDirectInApp(userPushNotificationSetting?.allow_in_app_direct_message);
    setPostReactionInApp(
      userPushNotificationSetting?.allow_in_app_post_reaction,
    );
    setSubmissionInApp(
      userPushNotificationSetting?.allow_in_app_authentication_submission_status,
    );
    setAccountFollowInAp(
      userPushNotificationSetting?.allow_in_app_follow_status,
    );
  }, [userPushNotificationSetting]);

  const toggleSwitch = value => {
    switch (value) {
      case 'directPush':
        setDirectPush(previousState => {
          pushToggleSwitch('directPush', !previousState);
          return !previousState;
        });
        return;
      case 'postReactionPush':
        setPostReactionPush(previousState => {
          pushToggleSwitch('postReactionPush', !previousState);
          return !previousState;
        });
        return;
      case 'submissionPush':
        setSubmissionPush(previousState => {
          pushToggleSwitch('submissionPush', !previousState);
          return !previousState;
        });
        return;
      case 'accountFollowPush':
        setAccountFollowPush(previousState => {
          pushToggleSwitch('accountFollowPush', !previousState);
          return !previousState;
        });
        return;

      case 'submissionInApp':
        setSubmissionInApp(previousState => {
          inAppToggleSwitch('submissionInApp', !previousState);
          return !previousState;
        });
        return;
      case 'postReactionInApp':
        setPostReactionInApp(previousState => {
          inAppToggleSwitch('postReactionInApp', !previousState);
          return !previousState;
        });
        return;
      case 'directInApp':
        setDirectInApp(previousState => {
          inAppToggleSwitch('directInApp', !previousState);
          return !previousState;
        });
        return;
      case 'accountFollowInApp':
        setAccountFollowInAp(previousState => {
          inAppToggleSwitch('accountFollowInApp', !previousState);
          return !previousState;
        });

        return;
    }
  };

  const inAppToggleSwitch = useCallback(
    async (type, value) => {
      try {
        await updatePushNotificationSetting({
          variables: {
            ...(type === 'directInApp'
              ? {allow_in_app_direct_message: value}
              : {}),
            ...(type === 'postReactionInApp'
              ? {allow_in_app_post_reaction: value}
              : {}),
            ...(type === 'submissionInApp'
              ? {allow_in_app_authentication_submission_status: value}
              : {}),
            ...(type === 'accountFollowInApp'
              ? {allow_in_app_follow_status: value}
              : {}),
          },
        });
        await syncNotificationToStorage({
          ...(type === 'directInApp'
            ? {allow_in_app_direct_message: value}
            : {}),
          ...(type === 'postReactionInApp'
            ? {allow_in_app_post_reaction: value}
            : {}),
          ...(type === 'submissionInApp'
            ? {allow_in_app_authentication_submission_status: value}
            : {}),
          ...(type === 'accountFollowInApp'
            ? {allow_in_app_follow_status: value}
            : {}),
        });
      } catch (_) {}
    },
    [updatePushNotificationSetting, refresh],
  );

  React.useEffect(() => {
    if (!isFocused) {
      try {
        refresh();
      } catch (_2) {}
    }
    return () => {
      clearTimeout(timeoutIdRef.current);
    };
  }, [isFocused]);

  const pushToggleSwitch = useCallback(
    async (type, value) => {
      try {
        await updatePushNotificationSetting({
          variables: {
            ...(type === 'directPush' ? {allow_direct_message: value} : {}),
            ...(type === 'postReactionPush'
              ? {allow_post_reaction: value}
              : {}),
            ...(type === 'submissionPush'
              ? {allow_authentication_submission_status: value}
              : {}),
            ...(type === 'accountFollowPush'
              ? {allow_follow_status: value}
              : {}),
          },
        });
      } catch (_) {}
    },
    [updatePushNotificationSetting, refresh, syncNotificationToStorage],
  );

  if (loading) {
    return <NotificationsLoader />;
  }

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.section,
          {borderBottomWidth: 3, borderBottomColor: colors.lightGrey},
        ]}>
        <Text style={styles.title}>
          {notificationsScreen.IN_APP_NOTIFICATIONS}
        </Text>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Direct Message</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={directInApp ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('directInApp')}
            value={directInApp}
          />
        </View>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Reaction to your post</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={postReactionInApp ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('postReactionInApp')}
            value={postReactionInApp}
          />
        </View>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Submission Status</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={submissionInApp ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('submissionInApp')}
            value={submissionInApp}
          />
        </View>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Account Follow</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={submissionInApp ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('accountFollowInApp')}
            value={accountFollowInApp}
          />
        </View>
      </View>
      <View style={styles.section}>
        <Text style={styles.title}>
          {notificationsScreen.PUSH_NOTIFICATIONS}
        </Text>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Direct Message</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={directPush ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('directPush')}
            value={directPush}
          />
        </View>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Reaction to your post</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={postReactionPush ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('postReactionPush')}
            value={postReactionPush}
          />
        </View>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Submission Status</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={submissionPush ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('submissionPush')}
            value={submissionPush}
          />
        </View>
        <View style={styles.card}>
          <Text style={styles.labelStyle}>Account Follow</Text>
          <Switch
            trackColor={{false: colors.grey, true: colors.green}}
            thumbColor={accountFollowPush ? colors.lightGrey : colors.lightGrey}
            ios_backgroundColor={colors.grey}
            onValueChange={() => toggleSwitch('accountFollowPush')}
            value={accountFollowPush}
          />
        </View>
      </View>
    </View>
  );
};

export default Index;
