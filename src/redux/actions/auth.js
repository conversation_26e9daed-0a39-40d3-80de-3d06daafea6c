import actionTypes from '../actionTypes';

//Login actions
export const loginSuccess = payload => ({
  type: actionTypes.LOGIN_SUCCESS,
  payload,
});

//Register account actions
export const registerSuccess = payload => ({
  type: actionTypes.REGISTER_SUCCESS,
  payload,
});

//Verify account actions
export const verificationSuccess = payload => ({
  type: actionTypes.ACCOUNT_VERIFICATION_SUCCESS,
  payload,
});

//Forgot password actions
export const forgotPasswordSuccess = payload => ({
  type: FORGOT_PASSWORD_SUCCESS,
  payload,
});

//Code validation actions
export const validateCodeSuccess = payload => ({
  type: actionTypes.CODE_VALIDATION_SUCCESS,
  payload,
});

//Reset Password actions
export const resetPasswordSuccess = payload => ({
  type: actionTypes.PASSWORD_RESET_SUCCESS,
  payload,
});

//Logout actions
export const Logout = () => ({
  type: actionTypes.SIGN_OUT,
});

//clean up state actions
export const clean = () => ({
  type: actionTypes.CLEAN_UP,
});

//Handle onboarding
export const handleOnBoarding = () => ({
  type: actionTypes.ONBOARDING,
});

export const isFirstTimeUsingApp = () => ({
  type: actionTypes.IS_FIRST_TIME_USING_APP,
});

export const expertLogin = () => ({
  type: actionTypes.IS_EXPERT,
});

export const setCryptoAddress = payload => ({
  type: actionTypes.CRYPTO_ADDRESS,
  payload,
});

export const refreshUser = payload => ({
  type: actionTypes.REFRESH_USER,
  payload,
});

export const refreshUserPushNotificationSettings = payload => ({
  type: actionTypes.REFRESH_USER_PUSH_NOTIFICATION_SETTINGS,
  payload,
});
