import React, {useState} from 'react';
import {StyleSheet, Text, View, TouchableOpacity, Keyboard} from 'react-native';
import {useMutation} from '@apollo/client';
import {BottomSheetTextInput} from '@gorhom/bottom-sheet';

import {CREATE_CUSTOM_BRAND} from '../../apollo/mutations';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';
import BottomSheet from '../../components/BottomSheet';
import TouchAwareButton from '../../components/Button/TouchAwareButton';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
    customBrand: '',
  },
};

const AddCustomBrand = ({
  handleSelection = () => null,
  defaultValueForBrand = '',
}) => {
  const [sheetVisible, setSheetVisible] = React.useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const closeSheet = () => {
    setSheetVisible(false);
  };

  return (
    <>
      <TouchAwareButton
        onPress={() => {
          Keyboard.dismiss();
          setSheetVisible(true);
        }}>
        <Text
          style={{
            color: colors.primary,
            fontFamily: 'Inter-Medium',
            fontSize: 15,
            textTransform: 'capitalize',
          }}>
          + {locale['AddCustomBrand']}
        </Text>
      </TouchAwareButton>
      <InfoModal
        delayInMs={500}
        setVisible={() => {
          if (!popup.isError) {
            closeSheet();
            handleSelection(popup?.data?.customBrand);
          }
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
      <BottomSheet
        key="custom_brand_sheet"
        visible={sheetVisible}
        setVisible={setSheetVisible}
        automaticallyAdjustHeight>
        <SheetChildren
          closeSheet={closeSheet}
          handleSelection={handleSelection}
          defaultValue={defaultValueForBrand}
          setPopup={setPopup}
        />
      </BottomSheet>
    </>
  );
};

function SheetChildren({
  closeSheet = () => null,
  defaultValue = '',
  setPopup = () => null,
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [text, setText] = useState(defaultValue);

  const [createCustomBrand] = useMutation(CREATE_CUSTOM_BRAND);

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const addNewBrand = React.useCallback(async () => {
    dismissKeyboard();
    setIsLoading(true);

    try {
      const {data} = await createCustomBrand({
        variables: {
          name: text,
        },
      });
      setIsLoading(false);
      const success = data?.createCustomBrand?.success;
      const message = data?.createCustomBrand?.message;
      const code = data?.createCustomBrand?.code;
      const customBrandData = data?.createCustomBrand?.data;
      setPopup(previousState => ({
        ...previousState,
        state: true,
        isError: !success,
        data: {
          title: success ? locale.Success : locale.Error,
          description: success
            ? locale.CustomBrandSubmittedSuccessfully
            : code?.length
            ? message
            : locale.SomethingWentWrongPleaseTryAgain,
          customBrand: customBrandData,
        },
      }));
      closeSheet();
    } catch (_) {
      setIsLoading(false);
      setPopup(previousState => ({
        ...previousState,
        state: true,
        isError: true,
        data: {
          title: locale.Error,
          description: locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    }
  }, [dismissKeyboard, createCustomBrand, text, setPopup, closeSheet]);

  return (
    <TouchableOpacity
      style={{padding: 20}}
      activeOpacity={1}
      onPressIn={dismissKeyboard}>
      <Text style={styles.title}>{locale.AddABrand}</Text>
      <Text style={styles.desc}>{locale['IfTheBrandOfYourTShirt...']}</Text>
      <Text style={styles.label}>{locale.CustomBrand}</Text>
      <BottomSheetTextInput
        value={text}
        onChangeText={val => setText(val)}
        placeholder="Type here..."
        style={styles.textInputStyles}
      />
      <View style={{height: 50}} />

      <Button
        text={locale.AddCustomBrand}
        isLoading={isLoading}
        disabled={isLoading || text.length == 0}
        onPress={addNewBrand}
        style={{paddingVertical: 12, borderRadius: 5}}
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
  textInputStyles: {
    height: 50,
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 4,
    color: colors.black,
    paddingLeft: spacings.md,
  },
  desc: {
    marginTop: spacings.lg,
    color: colors.black,
    fontFamily: 'Inter-Regular',
    fontSize: 15,
  },
  title: {
    color: colors.black,
    fontSize: fontSize.xl,
    fontFamily: 'Inter-Bold',
    textTransform: 'capitalize',
  },
  buttonStyles: {
    backgroundColor: colors.primary,
    height: 50,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacings.xl,
  },
  ButtonText: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontFamily: 'Inter-Bold',
  },
  label: {
    marginTop: spacings.lg,
    color: colors.black,
    marginBottom: spacings.md,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
  errorMessageStyles: {
    color: colors.warning,
    fontSize: fontSize.sm,
    marginTop: spacings.md,
  },
  modalStyles: {
    flex: 1,
    backgroundColor: colors.darkBadge,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacings.lg,
  },
  visibleSection: {
    backgroundColor: colors.white,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacings.xl,
  },
  messageStyles: {
    marginTop: spacings.lg,
    color: colors.black,
    textAlign: 'center',
  },
  continueButton: {
    height: 50,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    marginTop: spacings.lg,
    paddingHorizontal: spacings.lg,
    width: '100%',
  },
  continueButtonText: {
    fontWeight: 'bold',
    color: colors.white,
    fontSize: fontSize.lg,
  },
  closeModalButtonStyles: {
    position: 'absolute',
    top: 0,
    right: 0,
    margin: spacings.md,
    padding: spacings.md,
  },
});

export default AddCustomBrand;
