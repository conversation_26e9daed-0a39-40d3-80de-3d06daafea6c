import React, {useCallback} from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  Linking,
  ActivityIndicator,
} from 'react-native';
import {useMutation} from '@apollo/client';
import {useNavigation} from '@react-navigation/native';

import {FOLLOW_USER, UNFOLLOW_USER} from '../../apollo/mutations';
import {styles} from './styles';
import {icons, screens, userProfileScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import Icon from '../../components/Icon';
import {spacings} from '../../assets/spacing';
import ProfileImage from '../../components/ProfileImage';
import FastImage from '../../components/FastImage';
import {stripHTTPSFromURL, formatURL} from '../../tools/utils';
import locale from '../../assets/locale.json';

const Header = ({data, refetch, id, isMyProfile = false}) => {
  const navigation = useNavigation();

  const expertUser = data?.getExpert?.data?.user;

  const socialMediaUrl = expertUser?.social_media?.[0]?.url;

  const [followUser, {loading: followLoading}] = useMutation(FOLLOW_USER);
  const [unfollowUser, {loading: unfollowLoading}] = useMutation(UNFOLLOW_USER);

  const handleFollowUser = useCallback(async () => {
    try {
      const response = await followUser({
        variables: {
          userId: id,
        },
      });
      refetch();
    } catch (e) {}
  }, [followUser, id, refetch]);

  const handleUnfollowUser = useCallback(async () => {
    try {
      const response = await unfollowUser({
        variables: {
          userId: id,
        },
      });
      refetch();
    } catch (e) {}
  }, [id, refetch, unfollowUser]);

  const handleLinking = async () => {
    try {
      await Linking.openURL(formatURL(socialMediaUrl));
    } catch (_) {}
  };

  return (
    <View style={styles.headerContainer}>
      <View style={styles.imageWrapper}>
        <ProfileImage url={expertUser?.image} size={70} canViewImage />
        <View style={styles.textSection}>
          <Text style={styles.username}>
            {data?.getExpert?.data?.user?.username}
          </Text>
          <View style={styles.levelsWrapper}>
            <View style={styles.levelsButton}>
              <Text style={styles.levelsText}>{locale.Expert}</Text>
            </View>
            <FastImage
              source={{uri: data?.getExpert?.data?.user?.badge?.image_url}}
              style={styles.noviceBadge}
            />
          </View>
        </View>
      </View>
      <View>
        {data?.getExpert?.data?.expertise ? (
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              alignItems: 'center',
            }}>
            <Text style={[styles.desc, {color: colors.black, opacity: 0.9}]}>
              Expertise:{' '}
            </Text>
            {data?.getExpert?.data?.expertise?.map(exp => (
              <View
                key={exp?.id}
                style={[
                  styles.levelsButton,
                  {
                    alignSelf: 'baseline',
                    marginVertical: 3,
                  },
                ]}>
                <Text style={styles.levelsText}>{exp?.name}</Text>
              </View>
            ))}
          </View>
        ) : null}
        <View style={styles.descWrapper}>
          {expertUser?.bio ? (
            <Text style={[styles.desc, {fontSize: 13}]} numberOfLines={5}>
              {expertUser?.bio}{' '}
            </Text>
          ) : null}
          {socialMediaUrl ? (
            <TouchableOpacity onPress={handleLinking}>
              <Text style={styles.link}>
                {stripHTTPSFromURL(socialMediaUrl)}
              </Text>
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      <View style={styles.popularity}>
        <TouchableOpacity
          style={styles.popularityWrapper}
          onPress={() =>
            navigation.navigate(screens.FOLLOWERS_LISTING, {params: id})
          }>
          <Text style={styles.popularityStat}>
            {data?.getExpert?.data?.user?.followersCount ?? 0}
          </Text>
          <Text style={styles.desc}>{userProfileScreen.FOLLOWERS}</Text>
        </TouchableOpacity>
        {isMyProfile ? (
          <TouchableOpacity
            style={styles.popularityWrapper}
            onPress={() =>
              navigation.navigate(screens.FOLLOWING_LISTING, {params: id})
            }>
            <Text style={styles.popularityStat}>
              {data?.getExpert?.data?.user?.followingsCount ?? 0}
            </Text>
            <Text style={styles.desc}>{locale.Following}</Text>
          </TouchableOpacity>
        ) : null}
        <View style={styles.popularityWrapper}>
          <Text style={styles.popularityStat}>
            {data?.getExpert?.data?.number_of_certifications ?? 0}
          </Text>
          <Text style={styles.desc}>
            {userProfileScreen.AUTHENTICATION_COUNT}
          </Text>
        </View>
      </View>
      {isMyProfile ? null : (
        <View style={styles.socialButtonsWrapper}>
          {data?.getExpert?.data?.user?.doIFollow ? (
            <TouchableOpacity
              onPress={handleUnfollowUser}
              style={[styles.socialButtons, {backgroundColor: colors.primary}]}>
              {unfollowLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <>
                  <Icon
                    name={icons.PASS_ICON}
                    tint={colors.white}
                    size={fontSize.md}
                    style={{marginRight: spacings.sm}}
                  />

                  <Text style={styles.socialButtonsText}>
                    {userProfileScreen.FOLLOWING}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.socialButtons}
              onPress={handleFollowUser}>
              {followLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <>
                  <Icon
                    name={icons.ADD}
                    tint={colors.white}
                    size={fontSize.md}
                    style={{marginRight: spacings.sm}}
                  />

                  <Text style={styles.socialButtonsText}>
                    {userProfileScreen.FOLLOW}
                  </Text>
                </>
              )}
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.socialButtons}
            onPress={() =>
              navigation.navigate(screens.CHAT_SCREEN, {params: id})
            }>
            <Icon
              name={icons.MESSENGER_READ}
              tint={colors.white}
              size={fontSize.lg}
              style={{marginRight: spacings.sm}}
            />
            <Text style={styles.socialButtonsText}>
              {userProfileScreen.MESSAGE}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default Header;
