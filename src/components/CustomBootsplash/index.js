import React from 'react';
import {StyleSheet, StatusBar} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import RNBootSplash from 'react-native-bootsplash';

import colors from '../../assets/colors';
import Lottie from '../Lottie';

const INITIAL_OPACITY_VALUE = 1;

export default function CustomBootSplash({children = () => null}) {
  const [isBootSplashVisible, setIsBootSplashVisible] = React.useState(true);
  const [bootSplashLogoIsLoaded, setBootSplashLogoIsLoaded] = React.useState(
    false,
  );
  const [isReady, setIsReady] = React.useState(false);
  const introRef = React.useRef();

  const opacity = useSharedValue(INITIAL_OPACITY_VALUE);

  const opacityStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const onReady = () => [setIsReady(true)];

  const handleNavigationReady = () => {
    opacity.value = withTiming(0, {duration: 300}, () => {
      runOnJS(onReady)();
    });
  };

  const handleOnAnimationFinish = () => {
    setBootSplashLogoIsLoaded(true);
  };

  const onAnimationLayout = async () => {
    await RNBootSplash.hide();
    introRef.current?.play?.();
  };

  React.useEffect(() => {
    bootSplashLogoIsLoaded && setIsBootSplashVisible(false);
  }, [bootSplashLogoIsLoaded]);

  return (
    <>
      {!isReady ? (
        <Animated.View
          style={[StyleSheet.absoluteFill, styles.bootsplash, opacityStyle]}>
          <StatusBar barStyle="light-content" backgroundColor={colors.black} />
          <Lottie
            ref={introRef}
            name="splashIntro"
            loop={false}
            onAnimationFinish={handleOnAnimationFinish}
            style={{backgroundColor: colors.black, width: 200, height: 200}}
            onLayout={onAnimationLayout}
            autoPlay={false}
          />
        </Animated.View>
      ) : null}

      {!isBootSplashVisible
        ? children({
            isBootSplashVisible,
            handleWhenNavigationIsReady: handleNavigationReady,
          })
        : null}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 24,
    fontWeight: '700',
    margin: 20,
    lineHeight: 30,
    color: '#333',
    textAlign: 'center',
  },
  bootsplash: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.black,
  },
  logo: {
    width: 200, // This needs to match with the width from package.json generate-splash script
  },
});
