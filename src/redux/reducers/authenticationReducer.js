import actionTypes from '../actionTypes';
import locale from '../../assets/locale.json';

const INITIAL_STATE = {
  types: [
    {
      id: 1,
      name: locale.CommunityVoted,
      pricing: 0,
    },
    {
      id: 5,
      name: locale.ExpertFeedBack,
      pricing: null,
    },
    {
      id: 2,
      name: locale.ExpertCertified,
      pricing: null,
    },

    {
      id: 3,
      name: locale.ExpertCertNFT,
      pricing: null,
    },
    {
      id: 4,
      name: locale.TagOnly,
      pricing: 0,
    },
  ],
};

const authReducer = (state = INITIAL_STATE, {type, payload}) => {
  switch (type) {
    case actionTypes.SET_AUTHENTICATION_TYPES_INFO:
      return {
        ...state,
        types: updateAuthenticationInfo(payload, state.types),
      };

    default:
      return state;
  }
};

function updateAuthenticationInfo(infoFromServer = [], infoFromReducer = []) {
  if (!Array.isArray(infoFromServer)) {
    throw new Error(
      'Authentication info must be an array of authentication types.',
    );
  }

  return infoFromReducer.map(info => {
    const serverInfo = infoFromServer.find(item => item?.id == info?.id);
    if (!serverInfo) {
      return info;
    }
    return {
      id: serverInfo?.id ?? info?.id,
      name: serverInfo?.name ?? info?.name,
      pricing: serverInfo?.pricing ?? info?.pricing,
      iap_key_ios: serverInfo?.iap_key_ios ?? info?.iap_key_ios,
      iap_price_ios: serverInfo?.iap_price_ios ?? info?.iap_price_ios,
    };
  });
}

export default authReducer;
