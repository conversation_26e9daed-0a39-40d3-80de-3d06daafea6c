import React from 'react';
import {View, Text, StyleSheet, Platform} from 'react-native';
import BottomSheet, {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useLazyQuery} from '@apollo/client';
import {useHeaderHeight} from '@react-navigation/elements';

import colors from '../../assets/colors';
import {GET_USERS} from '../../apollo/queries';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import UsersSkeleton from './UsersSkeleton';
import locale from '../../assets/locale.json';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ProfileImage from '../../components/ProfileImage';
import {replaceAll} from '../../tools/utils';

const LIMIT = 20;
const IS_IOS = Platform.OS === 'ios';

export default function UsernameAutocompleteSheet({
  term = '',
  sheetBackgroundStyle = {},
  sheetChildrenStyle = {},
  onItemPress = () => null,
}) {
  const {top: safeAreaTop} = useSafeAreaInsets();
  const headerHeight = useHeaderHeight();

  const [isLoading, setIsLoading] = React.useState(false);
  const [isVisible, setIsVisible] = React.useState(false);

  const sheetRef = React.useRef();
  const justMountedRef = React.useRef(true);
  const debounceRef = React.useRef();

  const formattedTerm = React.useMemo(() => {
    return replaceAll(term, '@', '') ?? '';
  }, [term]);

  const [getUsers, {data}] = useLazyQuery(GET_USERS, {
    variables: {
      limit: LIMIT,
      filters: {...(formattedTerm?.length > 0 ? {term: formattedTerm} : {})},
      cacheId: APOLLO_CACHE_IDS.getUsers_AUTOCOMPLETE,
    },
    fetchPolicy: 'network-only',
  });

  React.useEffect(() => {
    if (term?.length > 0) {
      (function () {
        setIsLoading(true);
        if (debounceRef.current) {
          clearTimeout(debounceRef.current);
        }
        debounceRef.current = setTimeout(async () => {
          try {
            await getUsers();
          } catch (_) {
          } finally {
            setIsLoading(false);
          }
        }, 300);
      })();
    }
  }, [getUsers, term]);

  React.useEffect(() => {
    let timeOutId;
    if (!justMountedRef.current) {
      if (timeOutId) {
        clearTimeout(timeOutId);
      }
      if (term?.length > 0) {
        setIsVisible(true);
      }
      timeOutId = setTimeout(() => {
        if (term?.length == 0) {
          setIsVisible(term?.length > 0);
        }
      }, 50);
    } else {
      justMountedRef.current = false;
    }

    return () => {
      clearTimeout(timeOutId);
      clearTimeout(debounceRef.current);
    };
  }, [term]);

  const users = React.useMemo(() => data?.getUsers?.data?.edges, [
    data?.getUsers?.data?.edges,
  ]);

  return (
    <BottomSheet
      snapPoints={['1%', '95%']}
      index={isVisible ? 1 : 0}
      style={{zIndex: -1}}
      handleComponent={null}
      keyboardBehavior="interactive"
      keyboardBlurBehavior="restore"
      android_keyboardInputMode="adjustResize"
      enableHandlePanningGesture={false}
      enablePanDownToClose={false}
      enableContentPanningGesture={false}
      topInset={IS_IOS ? headerHeight + safeAreaTop : 0}
      animateOnMount={false}
      backgroundStyle={[
        {
          backgroundColor: 'transparent',
        },
        sheetBackgroundStyle,
      ]}>
      <View
        style={[
          {
            flex: 1,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            marginTop: headerHeight + safeAreaTop,
          },
          sheetChildrenStyle,
        ]}>
        <BottomSheetFlatList
          keyboardShouldPersistTaps={users?.length > 0}
          ref={sheetRef}
          data={term?.length > 0 ? users : []}
          keyExtractor={item => item?.id?.toString?.()}
          renderItem={({item}) => (
            <TouchAwareButton
              style={styles.userProfileContainer}
              onPress={onItemPress.bind(null, item)}>
              <ProfileImage
                size={50}
                canViewImage={false}
                url={item?.image}
                style={{borderWidth: 1}}
              />
              <View style={styles.userInfoContainer}>
                <Text style={styles.titleStyle}>{item?.username}</Text>
              </View>
            </TouchAwareButton>
          )}
          ListEmptyComponent={
            users?.length > 0 ? null : (
              <View style={{flex: 1}}>
                {!isLoading ? (
                  <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: colors.secondaryBlack,
                      flex: 1,
                    }}>
                    {Array.isArray(users) && users?.length == 0 ? (
                      <Text
                        style={{
                          color: colors.white,
                          fontFamily: 'Inter-Medium',
                        }}>
                        {locale.NoUsersFound}
                      </Text>
                    ) : null}
                  </View>
                ) : (
                  <UsersSkeleton />
                )}
              </View>
            )
          }
          style={{
            backgroundColor: colors.secondaryBlack,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
          }}
          contentContainerStyle={
            term?.length > 0 && Array.isArray(users) && users?.length > 0
              ? {
                  paddingBottom: 100,
                }
              : {flex: 1}
          }
        />
      </View>
    </BottomSheet>
  );
}

const styles = StyleSheet.create({
  userProfileContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: colors.white,
    alignItems: 'center',
  },
  userInfoContainer: {
    marginLeft: 16,
  },
  titleStyle: {
    fontFamily: 'Inter-Medium',
    fontSize: 18,
    color: colors.white,
  },
  subtitleStyle: {
    fontFamily: 'Inter-Regular',
    opacity: 0.5,
    color: colors.white,
  },
});
