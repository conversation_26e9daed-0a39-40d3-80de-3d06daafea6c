/* eslint-disable prettier/prettier */
import React, {useState, useCallback, useEffect, useRef} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
} from 'react-native';

import styles from './styles';
import colors from '../../assets/colors';
import BaseLayout from '../../layout';
import {screens, validateCodeScreen} from '../../assets/strings';
import {VALIDATE_CODE} from '../../tools/api';
import {spacings} from '../../assets/spacing';

export default function ValidateCode({route, navigation}) {
  const [isLoading, setIsLoading] = useState(false);
  const [code, setCode] = useState(null);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);

  const item = route.params.params;

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setError(null);
      isMounted.current = false;
    };
  }, []);

  const handleCodeVerification = useCallback(async () => {
    setIsLoading(true);
    let credentials = {
      email: item.email,
      code: code,
    };

    try {
      const response = await VALIDATE_CODE(credentials);
      const res = response.data;
      if (res?.success === true) {
        setIsLoading(false);
        navigation.navigate(screens.RESET_PASSWORD, {params: credentials});
      } else {
        setError(res?.error?.code);
        setIsLoading(false);
      }
    } catch (e) {
      setIsLoading(false);
      setError(e?.response?.data?.message ?? 'Something went wrong...');
    }
  }, [code, item.email, navigation]);

  return (
    <BaseLayout contentContainerStyle={styles.container}>
      <View style={styles.titleWrapper}>
        <Text style={styles.titleStyle}>{validateCodeScreen.TITLE}</Text>
      </View>
      <View style={styles.titleWrapper}>
        <Text style={styles.inputLabel}>{validateCodeScreen.SUBTITLE}</Text>
      </View>
      <View style={styles.inputWrapper}>
        <Text style={styles.label}>{validateCodeScreen.CODE}</Text>
        <TextInput
          value={code}
          onChangeText={setCode}
          placeholder={validateCodeScreen.PLACEHOLDER}
          selectionColor={colors.grey}
          placeholderTextColor={colors.grey}
          style={styles.inputStyles}
          keyboardType="numeric"
          onSubmitEditing={handleCodeVerification}
        />
      </View>
      <TouchableOpacity style={styles.button} onPress={handleCodeVerification}>
        {isLoading ? (
          <ActivityIndicator color={colors.white} />
        ) : (
          <Text style={styles.buttonText}>
            {validateCodeScreen.NEXT_BUTTON}
          </Text>
        )}
      </TouchableOpacity>
      <View style={{marginTop: spacings.sm}} />
      {error ? (
        <View style={styles.errorWrapper}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : null}
    </BaseLayout>
  );
}
