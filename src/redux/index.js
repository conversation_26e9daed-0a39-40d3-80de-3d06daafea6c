/* eslint-disable prettier/prettier */
import {createStore, applyMiddleware} from 'redux';
import {persistStore, persistReducer} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

import rootReducer from './reducers';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
};

const configureStore = (INITIAL_STORE = {}) => {
  const middleware = [];
  if (process.env.NODE_ENV === 'development') {
    // middleware.push(logger);
  }

  const persistedReducer = persistReducer(persistConfig, rootReducer);
  const store = createStore(
    persistedReducer,
    INITIAL_STORE,
    applyMiddleware(...middleware),
  );
  const persistedStore = persistStore(store);
  return {store, persistor: persistedStore};
};

export default configureStore;
