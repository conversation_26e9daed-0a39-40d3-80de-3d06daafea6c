import React from 'react';
import {Modal, View, StyleSheet, Text, Linking} from 'react-native';

import colors from '../../assets/colors';
import Button from '../Button';
import locale from '../../assets/locale.json';
import Lottie from '../Lottie';
import {useAppStateSubscription, useAppDetails} from '../../tools/hooks';

export default WrappedComponent => props => {
  const {appData, refetch} = useAppDetails({manuallyRefetch: true});
  const {storeURL, appVersion, appVersionFromServer} = appData;

  const {state} = useAppStateSubscription();

  const shouldUpdateApp = React.useMemo(
    () => appVersion < appVersionFromServer,
    [appVersion, appVersionFromServer],
  );

  const handleUpdateButtonPress = React.useCallback(() => {
    try {
      Linking.openURL(storeURL);
    } catch (error) {}
  }, [storeURL]);

  React.useEffect(() => {
    if (state === 'active') {
      refetch();
    }
  }, [state]);

  return (
    <>
      <WrappedComponent {...props} />
      {shouldUpdateApp ? (
        <Modal visible>
          <View
            style={{
              backgroundColor: colors.black,
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Lottie name="systemUpdate" style={{width: 200, height: 200}} />
            <Text style={styles.title}>
              {locale.PleaseUpdateTheAppToContinue}
            </Text>
            <Button text={locale.Update} onPress={handleUpdateButtonPress} />
          </View>
        </Modal>
      ) : null}
    </>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 18,
    marginVertical: 30,
    fontFamily: 'Gugi-Regular',
    textAlign: 'center',
    color: colors.white,
  },
});
