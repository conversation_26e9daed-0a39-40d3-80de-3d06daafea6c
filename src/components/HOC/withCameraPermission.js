import React from 'react'

import checkPermission from '../../tools/permissions'
import { useNavigation } from '@react-navigation/native'

export default (
  WrappedComponent,
  willStay = false,
  requireAlert = true,
  canBeIgnored = false
) => (props) => {
  const [granted, setGranted] = React.useState(false)
  const navigation = useNavigation()

  React.useEffect(() => {
    checkPermission(
      'camera',
      setGranted,
      willStay,
      requireAlert,
      true,
      navigation
    )
  }, [navigation, setGranted])

  return canBeIgnored ? (
    <WrappedComponent
      {...props}
      setCameraPermission={setGranted}
      cameraPermission={granted}
    />
  ) : granted ? (
    <WrappedComponent
      {...props}
      setCameraPermission={setGranted}
      cameraPermission={granted}
    />
  ) : null
}

// When user denies permission

//willStay = if permission is required strictly  during mounting of component to do the task then willStay = false eg: Camera permission is strictly required for scanning QRCode. if permission is not given will go back to home screen. Completely related to navigation

// canBeIgnored (only required for camera)= If some component requires camera permission but can do other task when not given. Eg: If theres a form containing optional upload profile image

// requireAlert: Is asking permission alert required at component mount?
