import {StyleSheet, Dimensions, Platform} from 'react-native';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('screen');

const isIOS = Platform.OS == 'ios';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  mainImage: {
    height: WIDTH / 1.5,
    width: WIDTH,
  },
  mainImageContainer: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.lightGrey,
  },
  topImageSection: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  buttonText: {
    color: colors.white,
    textTransform: 'capitalize',
  },
  bottomImageSection: {
    width: '100%',
    position: 'absolute',
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  button: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    flexDirection: 'row',
    alignItems: 'center',
  },
  userContainer: {
    width: WIDTH,
    backgroundColor: colors.lightGrey,
    padding: spacings.lg,
  },
  userCardTitleText: {
    color: colors.black,
    fontSize: fontSize.xxl,
    textTransform: 'capitalize',
    fontFamily: 'Gugi-Regular',
    lineHeight: 20,
    maxWidth: '65%',
  },
  userId: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  postId: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
    textTransform: 'uppercase',
    marginVertical: 8,
    marginRight: 5,
  },
  userContainerTopSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  userContainerBottomSection: {
    marginTop: spacings.md,
    width: '100%',
  },
  userAvatar: {
    height: 30,
    width: 30,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: colors.grey,
  },
  sm: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
  },
  statsContainer: {
    padding: 20,
  },
  appraisalWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: spacings.lg,
    flexWrap: 'wrap',
  },
  appraisalLabelWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  appraisalLabel: {
    fontSize: fontSize.lg,
    color: colors.black,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Regular',
  },
  descriptionContainer: {
    width: WIDTH,
    paddingHorizontal: spacings.lg,
    paddingBottom: 100,
    marginBottom: 20,
  },
  descCardWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: spacings.lg,
    flexWrap: 'wrap',
  },
  altDescCardWrapper: {
    paddingTop: spacings.lg,
  },
  altDescLabel: {
    color: colors.black,
    fontSize: fontSize.lg,
    borderTopWidth: 3,
    borderTopColor: colors.lightGrey,
    paddingTop: spacings.lg,
    fontFamily: 'Gugi-Regular',
  },
  descValue: {
    fontSize: fontSize.lg,
    color: colors.dark,
  },
  descLabel: {
    fontSize: fontSize.lg,
    color: colors.dark,
  },
  descTitleWrapper: {
    paddingTop: spacings.xl,
  },
  descTitle: {
    color: colors.black,
    fontSize: fontSize.xl,
    textTransform: 'capitalize',
    fontFamily: 'Gugi-Regular',
  },
  voteButtonContainer: {
    position: 'absolute',
    bottom: spacings.xl,
    paddingHorizontal: spacings.lg,
    width: WIDTH,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  showResults: {
    backgroundColor: colors.black,
    minWidth: '50%',
    height: 50,
    paddingHorizontal: spacings.lg,
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  showResultsLabel: {
    color: colors.white,
    fontSize: 16,
    textTransform: 'capitalize',
    marginLeft: spacings.sm,
    fontFamily: 'Inter-Medium',
  },
  voteButton: {
    backgroundColor: colors.primary,
    width: '100%',
    height: 50,
    borderRadius: spacings.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voteButtonLabel: {
    color: colors.white,
    fontSize: fontSize.xl,
    marginLeft: 10,
    fontFamily: 'Inter-Medium',
  },
  bottomSheetContainer: {
    width: WIDTH,
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainerStyle: {
    padding: spacings.lg,
  },
  cardContainer: {
    backgroundColor: '#fff',
    height: WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    paddingTop: spacings.lg,
  },
  smallVoteButtons: {
    flexDirection: 'row',
    borderRadius: 5,
    height: 45,
    width: '48%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.grey,
  },
  smallVoteButtonsText: {
    color: colors.black,
    fontSize: fontSize.xl,
    fontWeight: 'bold',
  },
  smallVoteButtonWrapper: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formWrapper: {
    width: '100%',
    marginTop: spacings.lg,
  },
  formLabelHeading: {
    fontSize: fontSize.xxl,
    color: colors.black,
    marginBottom: spacings.md,
    fontFamily: 'Gugi-Regular',
  },

  textField: {
    width: '100%',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: colors.grey,
    color: colors.black,
    paddingLeft: spacings.md,
    // ADDED
    ...(isIOS ? {padding: 15} : {}),
  },
  formLabel: {
    color: colors.black,
    marginBottom: spacings.sm,
    fontFamily: 'Inter-Medium',
  },
  submitButton: {
    marginVertical: spacings.lg,
    backgroundColor: colors.primary,
    width: '100%',
    height: 50,
    borderRadius: spacings.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  modal: {
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
  modalTitle: {
    color: colors.black,
    fontSize: 22,
    marginBottom: spacings.lg,
    textTransform: 'capitalize',
    fontFamily: 'Gugi-Regular',
  },
  modalStyles: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.darkBadge,
  },
  visibleSection: {
    width: '100%',
    padding: 20,
  },
  modalCloseButton: {
    padding: spacings.lg,
  },
  modalHeader: {
    width: '100%',
    alignItems: 'flex-end',
  },
  namePlaceholder: {
    marginLeft: spacings.sm,
    width: 50,
    height: 15,
    borderRadius: 4,
    backgroundColor: colors.grey,
  },
  voteCount: {
    color: colors.black,
  },
  viewAllVotes: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  voteCountWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: spacings.sm,
    paddingLeft: 25,
  },
  resultsHeading: {
    color: colors.black,
    fontSize: fontSize.xxl,
    fontFamily: 'Gugi-Regular',
  },
  authKey: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  provenanceTaggingTitle: {
    color: colors.primary,
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    alignItems: 'center',
  },
  usernameInputField: {
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: colors.grey2,
    marginVertical: 5,
  },
  provenanceTaggingInputLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: 17,
    color: colors.black,
  },
  usernameInputFieldPlaceholder: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    color: colors.grey,
  },
  searchBar: {
    width: '90%',
    height: 40,
    paddingHorizontal: 5,
    fontSize: 14,
    color: colors.black,
  },
  searchBarWrapper: {
    width: '90%',
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    paddingLeft: 10,
    backgroundColor: colors.lightGrey,
  },
  closeButtonText: {
    fontSize: 14,
    color: colors.primary,
    padding: 10,
    fontWeight: 'bold',
  },
});
