/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH - spacings.xl;

const Loader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={6}
          height={CARD_WIDTH / 2.5}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>

        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={6}
          height={CARD_WIDTH / 2.5}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>

        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={6}
          height={CARD_WIDTH / 2.5}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>

        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={6}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={6}
          height={CARD_WIDTH / 2.5}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 2}
            height={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH / 4}
            height={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default Loader;
