import React, {useCallback} from 'react';
import {StyleSheet, Dimensions, FlatList, View} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import NotificationsMessagesLoader from '../../components/Loaders/NotificationsMessagesLoader';
import {GET_NOTIFICATIONS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import RefreshControl from '../../components/RefreshControl';
import {SET_ALL_NOTIFICATIONS_AS_READ} from '../../apollo/mutations';
import QueryManager from '../../components/QueryManager';
import locale from '../../assets/locale.json';
import NotificationRow from './NotificationRow';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import ActivityIndicator from '../../components/ActivityIndicator';
import {useInteractionManager} from '../../tools/hooks';

const LIMIT = 20;

const {height: SCREEN_HEIGHT} = Dimensions.get('screen');

const NotificationMessages = () => {
  const scrollRef = React.useRef();

  const [refreshing, setRefreshing] = React.useState(false);
  const [isFetchingMore, setIsFetchingMore] = React.useState(false);

  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const {data, fetchMore, loading, error, refetch} = useQuery(
    GET_NOTIFICATIONS,
    {
      variables: {
        limit: LIMIT,
      },
    },
  );

  const cursorRef = React.useRef([]);

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          cursorRef.current = [];
          refetch?.();
        }
      } catch (_) {}
    }, [refetch]),
  );

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        cursorRef.current = [];
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  const cursor = React.useMemo(
    () => data?.getNotifications?.data?.pageInfo?.endCursor,
    [data?.getNotifications?.data?.pageInfo?.endCursor],
  );

  const hasMore = React.useMemo(
    () => data?.getNotifications?.data?.pageInfo?.hasMore,
    [data?.getNotifications?.data?.pageInfo?.hasMore],
  );

  const handleFetchMore = useCallback(async () => {
    try {
      if (cursor !== null && hasMore && !cursorRef.current?.includes(cursor)) {
        cursorRef.current?.push(cursor);
        setIsFetchingMore(true);

        await fetchMore?.({
          variables: {
            after: cursor,
            limit: LIMIT,
          },
          updateQuery(previousResult, {fetchMoreResult}) {
            setIsFetchingMore(false);

            const newCursor =
              fetchMoreResult?.getNotifications?.data?.pageInfo?.endCursor;
            return {
              getNotifications: {
                data: {
                  edges: [
                    ...previousResult?.getNotifications?.data?.edges,
                    ...fetchMoreResult?.getNotifications?.data?.edges,
                  ],
                  pageInfo: {
                    endCursor: newCursor,
                    hasMore:
                      fetchMoreResult?.getNotifications?.data?.pageInfo
                        ?.hasMore,
                  },
                  __typename: previousResult?.getNotifications?.__typename,
                },
                code: fetchMoreResult?.getNotifications?.code,
                errors: fetchMoreResult?.getNotifications?.errors,
                message: fetchMoreResult?.getNotifications?.message,
                success: fetchMoreResult?.getNotifications?.success,
              },
            };
          },
        });
      }
      setIsFetchingMore(false);
    } catch (error) {
      setIsFetchingMore(false);
    }
  }, [cursor, fetchMore, hasMore]);

  return (
    <View style={styles.container}>
      <QueryManager
        data={data}
        loading={loading}
        error={error}
        refetch={refetch}>
        <QueryManager.Data>
          <FlatList
            data={data?.getNotifications?.data?.edges}
            renderItem={({item}) =>
              isInteractionsComplete ? (
                <NotificationRow item={item} key={item?.id} />
              ) : null
            }
            keyExtractor={item => item?.id?.toString()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{paddingBottom: isFetchingMore ? 100 : 50}}
            bounces
            ListEmptyComponent={
              !loading && isInteractionsComplete ? (
                <View style={styles.emptyView}>
                  <DefaultEmptyComponent
                    text={locale.YouHaveNoNotificationsForNow}
                    containerStyle={{alignItems: 'center'}}
                    lottieStyle={{width: 80, height: 80}}
                  />
                </View>
              ) : null
            }
            ListFooterComponent={
              <>
                {isFetchingMore ? (
                  <ActivityIndicator style={{marginTop: 20}} size={30} />
                ) : !isInteractionsComplete ? (
                  <NotificationsMessagesLoader />
                ) : null}
              </>
            }
            onEndReachedThreshold={0.2}
            onEndReached={handleFetchMore}
            scrollToOverflowEnabled={true}
            scrollEventThrottle={1900}
            initialNumToRender={10}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            onScroll={e => {
              scrollRef.current = e?.nativeEvent?.contentOffset?.y;
            }}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <NotificationsMessagesLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale.CouldNotFetchNotifications}
          style={{marginVertical: 10}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
      <SetNotificationAsRead />
    </View>
  );
};

function SetNotificationAsRead() {
  const [setAllNotificationAsRead] = useMutation(SET_ALL_NOTIFICATIONS_AS_READ);

  useFocusEffect(
    React.useCallback(() => {
      try {
        setAllNotificationAsRead();
      } catch (_) {}
    }, [setAllNotificationAsRead]),
  );

  return null;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  imagesStyles: {
    height: 45,
    width: 45,
    borderRadius: 45,
    borderWidth: 2,
    borderColor: colors.grey,
    marginRight: spacings.md,
  },
  notificationsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: colors.lightGrey,
    paddingVertical: spacings.lg,
  },
  iconWrapper: {
    flex: 2 / 10,
    alignItems: 'flex-end',
    padding: spacings.sm,
  },
  descWrapper: {
    flex: 8 / 10,
    flexDirection: 'row',
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyViewText: {
    marginTop: 150,
    color: colors.black,
    fontSize: fontSize.md,
  },
  userName: {
    color: colors.black,
    fontWeight: 'bold',
  },
  authenticationTitle: {
    color: colors.black,
    fontWeight: 'bold',
  },
});

export default NotificationMessages;
