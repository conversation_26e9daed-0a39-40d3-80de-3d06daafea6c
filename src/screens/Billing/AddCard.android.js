import React, {useState, useEffect} from 'react';
import {StyleSheet, Text, View, TextInput} from 'react-native';
import {CardField, useStripe} from '@stripe/stripe-react-native';
import {useMutation, useQuery} from '@apollo/client';
import {initStripe} from '@stripe/stripe-react-native';
import {STRIPE_MERCHANT_IDENTIFIER} from '@env';

import {GET_STRIPE_PUBLISHABLE_KEY} from '../../apollo/queries';
import {CREATE_CARD} from '../../apollo/mutations';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {creditCardScreen, screens} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import Layout from '../../layout';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

const CARD_ERRORS = {
  CARD_DECLINED: locale.CardDeclined,
};

const AddCard = ({navigation}) => {
  const {createToken} = useStripe();
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState('');
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
  const [canSubmit, setCanSubmit] = React.useState(false);

  const {data: publishableKeyData} = useQuery(GET_STRIPE_PUBLISHABLE_KEY);
  const [createCard] = useMutation(CREATE_CARD);

  useEffect(() => {
    if (publishableKeyData?.getStripePublishableKey?.data) {
      initStripe({
        publishableKey: publishableKeyData?.getStripePublishableKey?.data,
        merchantIdentifier: STRIPE_MERCHANT_IDENTIFIER,
      });
    }
  }, [publishableKeyData?.getStripePublishableKey.data]);

  const handleStripeToken = async () => {
    setIsLoading(true);
    try {
      const {token, error} = await createToken({
        type: 'Card',
        name,
      });

      if (error) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: true,
          data: {
            title: locale.Error,
            description:
              error.message ?? locale.SomethingWentWrongPleaseTryAgain,
          },
        }));
        setIsLoading(false);
        return;
      }
      const {data} = await createCard({
        variables: {
          token: token.id,
        },
      });
      const isSuccess = data?.createCard?.success;
      const code = data?.createCard?.code;
      const message = data?.createCard?.message;

      if (isSuccess) {
        navigation.navigate(screens.CREDIT_CARDS, {refetchCards: true});
      } else {
        if (code?.length) {
          setPopup(previousState => ({
            ...previousState,
            state: true,
            isError: true,
            data: {
              title: locale.Error,
              description: CARD_ERRORS[code] || message,
            },
          }));
        } else {
          throw new Error();
        }
      }
    } catch (e) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
        isError: true,
        data: {
          title: locale.Error,
          description: locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout style={styles.container}>
      <View style={styles.modalWrapper}>
        <Text style={styles.label}>{creditCardScreen.NAME}</Text>
        <TextInput
          style={styles.fieldInput}
          value={name}
          onChangeText={setName}
          placeholder="Cardholder's Name"
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
        <Text style={styles.label}>{creditCardScreen.CARD_DETAILS}</Text>
        <CardField
          postalCodeEnabled={false}
          placeholder={{
            number: creditCardScreen.CARD_NUMBER_PLACEHOLDER,
          }}
          cardStyle={{
            ...styles.cardStyle,
            textColor: colors.black,
            borderRadius: 5,
            fontSize: 14,
          }}
          onCardChange={({complete}) => {
            setCanSubmit(complete);
          }}
          style={styles.cardFieldStyle}
        />

        <Button
          text={creditCardScreen.SAVE}
          isLoading={isLoading}
          disabled={isLoading || !canSubmit}
          onPress={handleStripeToken}
          style={{height: 50, borderRadius: 5}}
        />
      </View>
      <InfoModal
        setVisible={() => {
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: 20,
    marginTop: 20,
  },
  cardFieldStyle: {
    width: '100%',
    height: 50,
    marginBottom: 30,
    borderRadius: 4,
    color: colors.black,
  },
  cardStyle: {
    borderWidth: 1,
    color: colors.black,
    borderColor: colors.grey,
  },
  fieldInput: {
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 4,
    marginBottom: 30,
    paddingLeft: spacings.md,
    color: colors.black,
    minHeight: 45,
  },
  addCardButton: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.grey,
    marginBottom: spacings.xxl,
    borderRadius: 4,
  },
  addCardButtonText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.black,
    marginBottom: spacings.sm,
    fontFamily: 'Inter-Medium',
  },
});

export default AddCard;
