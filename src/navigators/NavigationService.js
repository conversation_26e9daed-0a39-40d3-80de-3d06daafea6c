/* eslint-disable prettier/prettier */
import React from 'react';
import {StackActions} from '@react-navigation/native';

export const navigationRef = React.createRef();
export const isReadyRef = React.createRef();

export function goBack() {
  // if (isReadyRef.current && navigationRef.current) {
  //   navigationRef.current?.navigate(name, params);
  // }

  navigationRef.current?.reset();
}

export function navigate(name, params) {
  // if (isReadyRef.current && navigationRef.current) {
  //   navigationRef.current?.navigate(name, params);
  // }
  navigationRef.current?.navigate(name, params);
}

export function push(...args) {
  // if (isReadyRef.current && navigationRef.current) {
  //   navigationRef.current?.dispatch(StackActions.push(...args));
  // }
  navigationRef.current?.dispatch(StackActions.push(...args));
}
export function pop(count = 0) {
  // if (isReadyRef.current && navigationRef.current) {
  //   navigationRef.current?.dispatch(StackActions.push(...args));
  // }
  navigationRef.current?.dispatch(StackActions.pop(count));
}

export default {
  navigate,
  push,
  goBack,
  pop,
};
