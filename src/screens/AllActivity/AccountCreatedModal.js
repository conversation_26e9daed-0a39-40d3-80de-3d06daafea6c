import React, {useCallback} from 'react';
import {View, TouchableOpacity, Text} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';

import {isFirstTimeUsingApp} from '../../redux/actions/auth';
import colors from '../../assets/colors';
import Modal from '../../components/Modal';

import {screens, allActivityScreenHeaders} from '../../assets/strings';
import {styles} from './styles';

function AccountCreatedModal() {
  const {showModal} = useSelector(state => state.auth);

  const navigation = useNavigation();

  const [modalVisible, setModalVisible] = React.useState(false);

  const dispatch = useDispatch();

  const handleNavigation = useCallback(
    value => {
      setModalVisible(false);
      dispatch(isFirstTimeUsingApp());
      switch (value) {
        case 1:
          navigation.navigate(screens.PERSONAL_INFORMATION);
          return;
        case 2:
          navigation.navigate(screens.HOWITWORKS);
          return;
        default:
          return;
      }
    },
    [dispatch, navigation],
  );

  React.useEffect(() => {
    if (showModal) {
      setModalVisible(true);
    }
  }, [showModal]);

  return (
    <Modal
      delayInMs={1000}
      visible={modalVisible}
      setVisible={handleNavigation}
      width={350}
      style={{paddingHorizontal: 0, borderRadius: 10}}>
      <View style={[styles.visibleSection]}>
        <Text style={styles.heading}>
          {allActivityScreenHeaders.ACCOUNT_CREATED}
        </Text>
        <Text style={styles.subHeading}>
          {allActivityScreenHeaders.WELCOME_TO_AUTH8}
        </Text>
        <TouchableOpacity
          onPress={() => handleNavigation(1)}
          style={[styles.button, {backgroundColor: colors.primary}]}>
          <Text style={[styles.buttonText, {color: colors.white}]}>
            {allActivityScreenHeaders.COMPLETE_PROFILE}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleNavigation(2)}
          style={[styles.button, styles.howItWorksButton, {marginTop: 20}]}>
          <Text style={[styles.buttonText, {color: colors.black}]}>
            {allActivityScreenHeaders.VISIT_HOW_IT_WORKS}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.button} onPress={handleNavigation}>
          <Text style={[styles.buttonText, {color: colors.primary}]}>
            {allActivityScreenHeaders.SKIP_FOR_NOW}
          </Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
}

export default AccountCreatedModal;
