import React, {useLayoutEffect} from 'react';
import {StyleSheet, Text, Pressable, View, FlatList} from 'react-native';
import {DrawerActions, useFocusEffect} from '@react-navigation/native';
import {useQuery} from '@apollo/client';

import Icon from '../../components/Icon';
import {icons} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import locale from '../../assets/locale.json';
import Layout from '../../layout';
import {GET_FAQS} from '../../apollo/queries';
import Skeleton from './Skeleton';
import QueryManager, {
  DefaultErrorComponent,
} from '../../components/QueryManager';
import RefreshControl from '../../components/RefreshControl';

const Index = ({navigation}) => {
  const {data, loading, refetch, error} = useQuery(GET_FAQS);

  const [refreshing, setRefreshing] = React.useState(false);

  useFocusEffect(
    React.useCallback(() => {
      try {
        refetch?.();
      } catch (_) {}
    }, []),
  );

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: locale.FrequentlyAskedQuestions,
      headerTintColor: colors.white,
      headerRight: null,
      headerLeft: () => (
        <Pressable
          style={styles.leftIconWrapper}
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}>
          <Icon
            name={icons.ARROW_LEFT}
            tint={colors.white}
            size={fontSize.xxxl}
          />
        </Pressable>
      ),
    });
  }, [navigation.setOptions]);

  const onRefresh = React.useCallback(async () => {
    try {
      setRefreshing(true);
      await refetch?.();
    } catch (_) {
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const faqs = data?.getFAQs?.data;

  const keyExtractor = React.useCallback(item => item?.id?.toString(), []);

  const renderItem = React.useCallback(
    ({item}) => (
      <View style={styles.itemContainer}>
        <Text style={styles.title}>{item?.title}</Text>
        <Text style={styles.body}>{item?.body}</Text>
      </View>
    ),
    [],
  );

  return (
    <Layout
      scrollChildren={false}
      style={{
        ...styles.container,
        ...(data == null && !(error == null) ? {flex: 1} : {}),
      }}>
      <QueryManager
        data={data}
        error={error}
        loading={loading}
        refetch={refetch}>
        <QueryManager.Data>
          <FlatList
            showsVerticalScrollIndicator={false}
            data={faqs ?? []}
            keyExtractor={keyExtractor}
            renderItem={renderItem}
            refreshControl={<RefreshControl {...{onRefresh, refreshing}} />}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <Skeleton />
        </QueryManager.Loading>
        <QueryManager.Error>
          <View
            style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            <DefaultErrorComponent errorLottieStyle={{width: 50, height: 50}} />
          </View>
        </QueryManager.Error>
      </QueryManager>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    paddingHorizontal: 20,
  },
  itemContainer: {
    marginVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: colors.grey,
    paddingBottom: 10,
  },
  title: {
    color: colors.black,
    fontSize: 20,
    fontFamily: 'Inter-Medium',
    fontWeight: '700',
  },
  body: {
    color: colors.secondaryBlack,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    opacity: 0.7,
  },
});

export default Index;
