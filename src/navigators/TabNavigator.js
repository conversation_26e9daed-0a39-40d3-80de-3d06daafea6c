import React from 'react';
import { StyleSheet } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import AuthenticateTab from '../screens/Authenticate';
import MyStuffs from '../screens/MyStuffs';
import MyStuffsExpert from '../screens/MyStuffsExpert';
import { screens, icons, navLabels } from '../assets/strings';
import colors from '../assets/colors';
import { fontSize } from '../assets/font';
import MaterialTopNavigator from './MaterialTopNavigator';
import Icon from '../components/Icon';
import { useCredential } from '../tools/hooks/useUser';
import CameraScanTab from '../screens/AIScan';

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  const credential = useCredential();
  const isExpert = React.useMemo(() => credential?.role_id == 3, [
    credential?.role_id,
  ]);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarActiveTintColor: colors.secondary,
        tabBarInactiveTintColor: colors.white,
        headerShown: false,
        tabBarStyle: styles.tabBarStyles,
        tabBarLabelStyle: styles.tabBarLabelStyles,
        tabBarIconStyle: styles.tabBarIconStyles,
        tabBarIcon: ({ color }) => {
          let iconName;
          if (route.name === screens.HOME) {
            iconName = icons.HOME_ICON;
            return <Icon name={iconName} size={fontSize.xxl} tint={color} />;
          } else if (route.name === screens.AUTHENTICATE_TAB) {
            iconName = icons.AUTHENTICATE_ICON;
            return <Icon name={iconName} size={fontSize.xxl} tint={color} />;
          } else if (route.name === screens.MY_STUFF) {
            iconName = icons.WALLET;
            return <Icon name={iconName} size={fontSize.xxl} tint={color} />;
          } else if (route.name === screens.AI_SCAN) {
            iconName = icons.WALLET;
            return <Icon name={iconName} size={fontSize.xxl} tint={color} />;
          } else if (route.name === screens.MY_STUFF_EXPERT) {
            iconName = icons.WALLET;
            return <Icon name={iconName} size={fontSize.xxl} tint={color} />;
          }
        },
      })}
    // {...(isExpert ? {tabBar: () => null} : {})}
    >


      <Tab.Screen
        name={screens.HOME}
        component={MaterialTopNavigator}
        options={{
          tabBarLabel: navLabels.HOME_TABS,
        }}
      />

      {isExpert && ( // Need to update this...
        <Tab.Screen
          name={screens.MY_STUFF_EXPERT}
          component={MyStuffsExpert}
          options={{ tabBarLabel: navLabels.MY_STUFF }}
        />
      )}


      {!isExpert && (
        <Tab.Screen
          name={screens.MY_STUFF}
          component={MyStuffs}
          options={{ tabBarLabel: navLabels.MY_STUFF }}
        />
      )}

      {!isExpert && (
        <Tab.Screen
          name={screens.AI_SCAN}
          component={CameraScanTab}
          options={{ tabBarLabel: navLabels.SCAN_AI }}
          listeners={({ navigation }) => ({
            tabPress: event => {
              event.preventDefault();
              navigation.navigate(screens.AI_SCAN_SCREEN);
            },
          })}
        />
      )}

      {!isExpert && (
        <Tab.Screen
          name={screens.AUTHENTICATE_TAB}
          component={AuthenticateTab}
          options={{
            tabBarLabel: navLabels.AUTHENTICATE_TAB,
          }}
          listeners={({ navigation }) => ({
            tabPress: event => {
              event.preventDefault();
              navigation.navigate(screens.AUTHENTICATE);
            },
          })}
        />
      )}
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBarLabelStyles: {
    fontSize: 12,
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  tabBarIconStyles: {
    marginTop: 5,
    fontSize: fontSize.xxxxl,
  },
  tabBarStyles: {
    backgroundColor: colors.black,
  },
});

export default TabNavigator;
