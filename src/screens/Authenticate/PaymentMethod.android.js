import React, {useState, useEffect, useCallback} from 'react';
import {
  StyleSheet,
  FlatList,
  Text,
  View,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {useMutation, useQuery} from '@apollo/client';

import {CREATE_PAYMENT_INTENT} from '../../apollo/mutations';
import {GET_ALL_CARDS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {
  authenticationTypeScreen,
  creditCardScreen,
  screens,
  editCreditCardScreen,
} from '../../assets/strings';
import locale from '../../assets/locale.json';
import InfoModal from '../../components/Modal/InfoModal';
import ActivityIndicator from '../../components/ActivityIndicator';

const ERROR_POPUP_INITIAL_STATE = {
  state: false,
  description: locale.SomethingWentWrongPleaseTryAgain,
};

const PaymentMethod = ({route, navigation}) => {
  const [intent, setIntent] = useState('');
  const [authId, setAuthId] = useState(route?.params?.authenticationId);
  const [refreshing, setRefreshing] = useState(false);
  const [createPaymentIntent, {loading: paymentIntentLoading}] = useMutation(
    CREATE_PAYMENT_INTENT,
  );
  const {data, refetch, loading, error} = useQuery(GET_ALL_CARDS, {
    fetchPolicy: 'network-only',
  });
  const [getNFT] = React.useState(route?.params?.getNFTDetails ?? null);

  const [errorPopup, setErrorPopup] = React.useState(ERROR_POPUP_INITIAL_STATE);

  const authenticationType = React.useMemo(
    () => route?.params?.authenticationType,
    [route?.params?.authenticationType],
  );

  useEffect(() => {
    if (route?.params?.authenticationId) {
      setAuthId(route?.params?.authenticationId);
    }
  }, [navigation, route?.params?.authenticationId]);

  useEffect(() => {
    if (route?.params?.cardAdded) {
      refetch();
    }
  }, [route?.params?.cardAdded]);

  const onRefresh = useCallback(async () => {
    try {
      setRefreshing(true);
      await refetch();
    } catch (_) {
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const initializePaymentIntent = useCallback(async () => {
    try {
      const response = await createPaymentIntent({
        variables: {
          authenticationId: +authId,
          ...(!(getNFT == null)
            ? {
                isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true,
              }
            : {}),
        },
      });
      const isSuccess = response?.data?.createPaymentIntent?.success;
      const intentData = response?.data?.createPaymentIntent?.data;
      const code = response?.data?.createPaymentIntent?.code;
      const message = response?.data?.createPaymentIntent?.message;

      const ALLOWED_CODES = [
        'AUTHENTICATION_NOT_CERTIFIED_PASS',
        'NFT_IS_DISABLED_FOR_THIS_BRAND',
      ];

      if (isSuccess) {
        setIntent(intentData);
      } else {
        if (ALLOWED_CODES.includes(code)) {
          setErrorPopup(previousState => ({
            ...previousState,
            state: true,
            description: message,
          }));
        } else {
          throw new Error();
        }
      }
    } catch (e) {
      setErrorPopup(previousState => ({...previousState, state: true}));
    }
  }, [authId, createPaymentIntent, getNFT]);

  useEffect(() => {
    initializePaymentIntent();
  }, [initializePaymentIntent]);

  const navigateToCheckOut = item => {
    navigation.navigate(screens.CHECKOUT, {
      otherParams: item,
      params: intent,
      authId: authId,
      expressCheckout: false,
      getNFTDetails: getNFT,
      authenticationType,
    });
  };

  const handleIntentCreationError = () => {
    setErrorPopup(ERROR_POPUP_INITIAL_STATE);
    navigation.goBack();
  };

  const handleDirectPaymentNavigation = () =>
    navigation.navigate(screens.AUTHENTICATION_ADD_CARD, {
      params: authId,
      intent: intent,
      getNFTDetails: getNFT,
      authenticationType,
    });

  const renderItem = ({item}) => {
    return (
      <TouchableOpacity
        style={styles.card}
        onPress={() => navigateToCheckOut(item)}>
        <View>
          <View style={styles.cardTitleWrapper}>
            <Text style={styles.cardTitle}>{item?.brand}</Text>
            {item.is_default ? (
              <Text style={styles.defaultLabel}>
                {creditCardScreen.DEFAULT}
              </Text>
            ) : null}
          </View>
          <Text style={styles.date}>{item?.name}</Text>
        </View>
        <View style={styles.rightSection}>
          <Text style={styles.cardTitle}>{item?.number}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorContainerText}>
          {editCreditCardScreen.SOMETHING_WENT_WRONG}
        </Text>
      </View>
    );
  }

  if (loading || paymentIntentLoading) {
    return (
      <View style={styles.emptyContainer}>
        <ActivityIndicator color={colors.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.labelTitle}>{locale.SelectPaymentMethod}</Text>
      <FlatList
        data={data?.getAllCards?.data}
        showsVerticalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={item => item?.id?.toString?.()}
        ListFooterComponent={
          <TouchableOpacity
            style={styles.addCardButton}
            onPress={handleDirectPaymentNavigation}>
            <Text style={styles.addCardButtonText}>
              {authenticationTypeScreen.ADD_CARD}
            </Text>
          </TouchableOpacity>
        }
        ListEmptyComponent={
          <View style={[styles.emptyContainer, {height: 100}]}>
            <Text style={styles.emptyContainerText}>
              {authenticationTypeScreen.NO_CARDS_SAVED}
            </Text>
          </View>
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            progressBackgroundColor={colors.lightGrey}
            colors={[colors.primary, colors.black]}
            tintColor={colors.primary}
            onRefresh={onRefresh}
          />
        }
      />
      <InfoModal
        delayInMs={100}
        setVisible={handleIntentCreationError}
        popUp={{
          state: errorPopup.state,
          isError: true,
          data: {
            title: locale.Error,
            description: errorPopup.description,
          },
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacings.lg,
    backgroundColor: colors.white,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyContainerText: {
    color: colors.black,
    fontSize: fontSize.md,
  },
  card: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: spacings.lg,
    borderWidth: 2,
    borderColor: colors.lightGrey,
    marginTop: spacings.lg,
    flexWrap: 'wrap',
  },
  cardTitleWrapper: {
    flexDirection: 'row',
  },
  cardTitle: {
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    color: colors.black,
  },
  defaultCardLabel: {
    color: colors.black,
    fontSize: fontSize.lg,
    marginBottom: 5,
  },
  defaultLabel: {
    color: colors.white,
    backgroundColor: colors.black,
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    fontSize: fontSize.sm,
    borderRadius: spacings.sm,
    marginLeft: spacings.md,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  leftSection: {
    alignItems: 'flex-start',
  },
  button: {
    marginTop: spacings.md,
    paddingVertical: spacings.sm,
  },
  buttonText: {
    fontSize: fontSize.md,
    color: colors.primary,
  },
  date: {
    fontSize: fontSize.md,
  },
  addCardButton: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.xxl,
    borderRadius: 4,
  },
  addCardButtonText: {
    fontSize: fontSize.lg,
    color: colors.white,
    fontWeight: 'bold',
  },
  label: {
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    color: colors.black,
    marginBottom: spacings.sm,
  },
  titleWrapper: {
    paddingHorizontal: spacings.lg,
    marginTop: spacings.lg,
  },
  title: {
    fontSize: fontSize.xl,
    fontWeight: 'bold',
    color: colors.black,
    marginBottom: spacings.sm,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorStyles: {
    color: colors.warning,
    fontSize: fontSize.sm,
  },
  labelTitle: {
    fontSize: 20,
    color: colors.black,
    marginTop: spacings.xl,
    fontFamily: 'Gugi-Regular',
  },
});
export default PaymentMethod;
