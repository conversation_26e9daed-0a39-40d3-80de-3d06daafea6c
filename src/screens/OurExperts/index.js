import React, {useCallback, useState} from 'react';
import {
  StyleSheet,
  FlatList,
  Text,
  View,
  Pressable,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {useQuery} from '@apollo/client';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import {useFocusEffect} from '@react-navigation/native';

import Icon from '../../components/Icon';
import HomeListingsLoader from '../../components/Loaders/HomeListingsLoader';
import ExpertsLoader from '../../components/Loaders/ExpertsLoader';
import {GET_EXPERTS, GET_ALL_EXPERTISE} from '../../apollo/queries';
import {fontSize} from '../../assets/font';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {images, icons, screens} from '../../assets/strings';
import {useUser} from '../../tools/hooks';
import QueryManager from '../../components/QueryManager';
import BottomSheet from '../../components/BottomSheet';
import ActivityIndicator from '../../components/ActivityIndicator';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import FastImage from '../../components/FastImage';
import ProfileImage from '../../components/ProfileImage';

const {width, height: SCREEN_HEIGHT} = Dimensions.get('window');
const WIDTH = width / 2;
const CARD_WIDTH = WIDTH - spacings.xl;
const LIMIT = 100;
const EXPERTISE_LIMIT = 50;

const Index = ({navigation}) => {
  const [expertiseIds, setExpertiseIds] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  const {data, refetch, loading, error} = useQuery(GET_EXPERTS, {
    variables: {
      limit: LIMIT,
      filters:
        expertiseIds?.length > 0 ? {expertise_ids: expertiseIds} : expertiseIds,
    },
  });

  const {user: userData} = useUser();
  const scrollRef = React.useRef();

  const handleNavigation = useCallback(
    item => {
      if (
        parseInt(userData?.me?.data?.id, 10) === parseInt(item?.user?.id, 10)
      ) {
        navigation.navigate(screens.USER_PROFILE);
      } else {
        navigation.navigate(screens.EXPERTS_PROFILE, {
          params: item?.id,
          expertUserId: item?.user?.id,
        });
      }
    },
    [navigation, userData?.me?.data?.id],
  );

  useFocusEffect(
    React.useCallback(() => {
      setExpertiseIds([]);
      try {
        if (!scrollRef.current) {
          cursorRef.current = [];
          refetch?.();
        }
      } catch (_) {}
    }, []),
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch().then(() => setRefreshing(false));
  }, [refetch]);

  const renderItem = React.useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          style={styles.cardsGroup}
          onPress={() => handleNavigation(item)}>
          <FastImage
            style={styles.imageStyles}
            source={
              item?.user?.image
                ? {uri: item?.user?.image}
                : images.PROFILE_PLACEHOLDER
            }
          />
          <View style={styles.desc}>
            <ProfileImage
              url={item?.user?.image}
              size={25}
              style={{marginRight: 10}}
            />

            <Text style={styles.nameLabel} numberOfLines={1}>
              {item?.user?.username}
            </Text>
          </View>
          <View>
            {item.expertise ? (
              <Text style={styles.label}>
                Expertise:{' '}
                {item?.expertise?.map(expertise => expertise?.name).join(', ')}
              </Text>
            ) : null}
          </View>
        </TouchAwareButton>
      );
    },
    [handleNavigation],
  );

  const handleSelection = React.useCallback(ids => setExpertiseIds(ids), []);

  const allExperts = data?.getExperts?.data?.edges;
  return (
    <View style={styles.container}>
      <QueryManager
        data={data}
        error={error}
        loading={loading}
        refetch={refetch}>
        <QueryManager.Data>
          <FlatList
            data={allExperts}
            columnWrapperStyle={styles.contentContainer}
            style={{paddingTop: spacings.lg}}
            renderItem={renderItem}
            numColumns={2}
            keyExtractor={item => item?.id?.toString?.()}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={
              Array.isArray(allExperts) && allExperts?.length == 0
                ? {flex: 1}
                : {}
            }
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                progressBackgroundColor={colors.lightGrey}
                colors={[colors.primary, colors.black]}
                tintColor={colors.primary}
                onRefresh={onRefresh}
              />
            }
            ListHeaderComponent={
              <Header
                handleSelection={handleSelection}
                selectedIds={expertiseIds}
              />
            }
            ListFooterComponent={() =>
              loading ? (
                <View style={{marginBottom: spacings.lg}}>
                  <HomeListingsLoader />
                </View>
              ) : (
                <View style={{marginBottom: spacings.xl}} />
              )
            }
            ListEmptyComponent={
              loading ? null : (
                <DefaultEmptyComponent
                  containerStyle={{
                    flex: 0.8,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  text={locale.NoExpertsFound}
                />
              )
            }
            onScroll={e => {
              scrollRef.current = e?.nativeEvent?.contentOffset?.y;
            }}
          />
        </QueryManager.Data>

        <QueryManager.Loading>
          <ExpertsLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          style={{marginVertical: 10}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
    </View>
  );
};

function Header({handleSelection = () => null, selectedIds = []}) {
  const [visible, setVisible] = React.useState(false);

  const closeSheet = () => {
    setVisible(false);
  };

  return (
    <>
      <View style={styles.filterWrapper}>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setVisible(true)}>
          <Text style={styles.filterButtonText}>
            {selectedIds?.length > 0 ? 'Selected...' : locale.AllExpertise}
          </Text>
          <Icon name={icons.CHEVRON_DOWN} size={10} tint={colors.black} />
        </TouchableOpacity>
      </View>
      <BottomSheet
        visible={visible}
        setVisible={setVisible}
        snapPoints={['40%', '80%']}
        index={1}>
        <List
          handleSelection={handleSelection}
          closeSheet={closeSheet}
          selectedIds={selectedIds}
        />
      </BottomSheet>
    </>
  );
}

function List({
  handleSelection = () => null,
  closeSheet = () => null,
  selectedIds = [],
}) {
  const [isFetchingMore, setIsFetchingMore] = React.useState(false);
  const [selected, setSelected] = React.useState(selectedIds);

  const {data: expertiseData, fetchMore} = useQuery(GET_ALL_EXPERTISE, {
    variables: {
      limit: EXPERTISE_LIMIT,
    },
    notifyOnNetworkStatusChange: true,
  });

  const cursorRef = React.useRef([]);

  let cursor = expertiseData?.getAllExpertise?.data?.pageInfo?.endCursor;
  let hasMore = expertiseData?.getAllExpertise?.data?.pageInfo?.hasMore;
  const list = expertiseData?.getAllExpertise?.data?.edges;

  const handleFetchMoreExpertise = useCallback(async () => {
    if (cursor !== null && hasMore && !cursorRef.current?.includes(cursor)) {
      cursorRef.current?.push(cursor);
      setIsFetchingMore(true);
      try {
        await fetchMore?.({
          variables: {
            after: cursor,
            limit: EXPERTISE_LIMIT,
          },
          updateQuery(previousResult, {fetchMoreResult}) {
            setIsFetchingMore(false);

            const newCursor =
              fetchMoreResult?.getAllExpertise?.data?.pageInfo?.endCursor;

            return {
              getAllExpertise: {
                data: {
                  edges: [
                    ...previousResult?.getAllExpertise?.data?.edges,
                    ...fetchMoreResult?.getAllExpertise?.data?.edges,
                  ],
                  pageInfo: {
                    endCursor: newCursor,
                    hasMore:
                      fetchMoreResult?.getAllExpertise?.data?.pageInfo?.hasMore,
                  },
                  __typename: previousResult?.getAllExpertise?.__typename,
                },
                code: fetchMoreResult?.getAllExpertise?.code,
                errors: fetchMoreResult?.getAllExpertise?.errors,
                message: fetchMoreResult?.getAllExpertise?.message,
                success: fetchMoreResult?.getAllExpertise?.success,
              },
            };
          },
        });
      } catch (_) {
        setIsFetchingMore(false);
      }
    }
  }, [cursor, fetchMore, hasMore]);

  const handleRowPressed = id => {
    setSelected(previousState =>
      selected?.length == 0
        ? [id]
        : previousState?.find(i => +i == +id)
        ? previousState?.filter(i => +i !== +id)
        : [...previousState, id],
    );
  };

  const handleClear = () => {
    setSelected([]);
    handleSelection([]);
    closeSheet();
  };

  const handleApply = () => {
    handleSelection(selected?.map(item => +item));
    closeSheet();
  };

  return (
    <>
      {selected?.length > 0 ? (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            marginBottom: 10,
          }}>
          <Button
            text={locale.Clear}
            style={{
              minHeight: 20,
              backgroundColor: colors.redBadge,
              marginLeft: 'auto',
              padding: 0,
              paddingVertical: 5,
              paddingHorizontal: 15,
              minWidth: 10,
              marginRight: 10,
            }}
            scaleAnimationThreshold={0.96}
            onPress={handleClear}
            textStyle={{fontSize: 14}}
          />
          <Button
            text={locale.Apply}
            style={{
              minHeight: 20,
              backgroundColor: colors.primary,
              marginLeft: 'auto',
              padding: 0,
              paddingVertical: 5,
              paddingHorizontal: 15,
              minWidth: 10,
              marginRight: 20,
            }}
            scaleAnimationThreshold={0.96}
            onPress={handleApply}
            textStyle={{fontSize: 14}}
          />
        </View>
      ) : null}
      <BottomSheetFlatList
        data={list}
        keyExtractor={item => item?.id?.toString()}
        showsVerticalScrollIndicator={false}
        renderItem={({item}) => (
          <ListRow
            isSelected={selected?.find(i => i == item?.id) ? true : false}
            item={item}
            onPress={handleRowPressed}
          />
        )}
        onEndReachedThreshold={0.5}
        onEndReached={handleFetchMoreExpertise}
        scrollToOverflowEnabled={true}
        scrollEventThrottle={1900}
        initialNumToRender={10}
        ListFooterComponent={
          <>
            {isFetchingMore ? (
              <View
                style={{
                  marginBottom: 100,
                  flex: 1,
                }}>
                <ActivityIndicator size={30} />
              </View>
            ) : null}
          </>
        }
      />
    </>
  );
}

const ListRow = React.memo(
  ({onPress = () => null, item = null, isSelected = false}) => {
    return (
      <Pressable
        onPress={() => onPress(+item?.id)}
        key={item?.id}
        style={styles.modalSheetContainer}>
        <Text style={{color: colors.black}}>{item?.name}</Text>
        <View style={isSelected ? styles.checkedBox : styles.uncheckedBox}>
          {isSelected ? (
            <Icon name={icons.CHECKED_RADIO} tint={colors.primary} size={20} />
          ) : (
            <Icon name={icons.UNCHECKMARK} tint={colors.grey} size={20} />
          )}
        </View>
      </Pressable>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  label: {
    color: colors.black,
    fontSize: 12,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Medium',
    opacity: 0.7,
  },
  nameLabel: {
    fontSize: fontSize.lg,
    color: colors.black,
    fontFamily: 'Inter-SemiBold',
    maxWidth: '85%',
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  cardsGroup: {
    width: CARD_WIDTH,
    marginBottom: spacings.lg,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: colors.lightGrey,
    overflow: 'hidden',
  },
  modalSheetContainer: {
    paddingVertical: spacings.lg,
    marginHorizontal: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconWrapper: {
    padding: spacings.lg,
  },
  imageStyles: {
    width: '100%',
    height: CARD_WIDTH / 1.5,
    borderRadius: 2,
  },
  leftIconWrapper: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
  },
  profileImageStyle: {
    height: 20,
    width: 20,
    borderRadius: 30,
    marginRight: spacings.sm,
    borderWidth: 1,
    borderColor: colors.grey,
  },
  desc: {
    marginTop: spacings.md,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacings.sm,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 20,
    width: 20,
    borderRadius: 2,
    backgroundColor: colors.primary,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  uncheckedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 20,
    width: 20,
    borderRadius: 2,
    backgroundColor: colors.lightGrey,
    borderWidth: 2,
    borderColor: colors.grey,
  },
  filterWrapper: {
    paddingBottom: spacings.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey,
    marginBottom: spacings.lg,
    marginHorizontal: 20,
  },
  filterButton: {
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderColor: colors.black,
    alignSelf: 'flex-start',
    paddingVertical: spacings.md,
    paddingHorizontal: spacings.md,
    borderRadius: 5,
  },
  filterButtonText: {
    color: colors.black,
    fontSize: fontSize.md,
    fontFamily: 'Inter-Medium',
    marginRight: 10,
  },
});

export default Index;
