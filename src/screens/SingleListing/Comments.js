import React, {useLayoutEffect, useEffect, useCallback, useState} from 'react';
import {
  StyleSheet,
  TextInput,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';
import dayjs from 'dayjs';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Animated, {FadeInDown} from 'react-native-reanimated';
import {useFocusEffect} from '@react-navigation/native';

import ProfileImage from '../../components/ProfileImage';
import {ADD_COMMENT_TO_POST} from '../../apollo/mutations';
import Icon from '../../components/Icon';
import {
  GET_ALL_POST_COMMENTS,
  GET_POST_AUTHENTICATION_TYPE_ONLY,
} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {useCredential} from '../../tools/hooks/useUser';
import RefreshControl from '../../components/RefreshControl';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import locale from '../../assets/locale.json';
import TextAutoLink from '../../components/TextAutoLink';
import ActivityIndicator from '../../components/ActivityIndicator';
import FastImage from '../../components/FastImage';
import {useWindowDimensions} from '../../tools/hooks';
import {screens} from '../../assets/strings';
import ImageViewer from '../../components/ImageViewer';
import {showToast} from '../../components/Toast';
import UsernameAutocompleteSheet from './UsernameAutocompleteSheet';

const LIMIT = 100;

const isIOS = Platform.OS == 'ios';

const Comments = ({route, navigation}) => {
  const postId = route?.params?.postId;
  const showPostNavigationLink = route?.params?.showPostNavigationLink ?? false;

  const scrollRef = React.useRef();

  const credential = useCredential();
  const isMember = credential?.role_id == 2;

  const [height, setHeight] = useState(40);
  const [comment, setComment] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  const [sendViewHeight, setSendViewHeight] = React.useState(0);
  const {bottom: bottomOffset} = useSafeAreaInsets();

  const {data, refetch, loading} = useQuery(GET_ALL_POST_COMMENTS, {
    variables: {
      postId: postId,
      limit: LIMIT, // TODO PAGINATION
    },
  });

  const {data: postData} = useQuery(GET_POST_AUTHENTICATION_TYPE_ONLY, {
    variables: {
      id: postId,
    },
  });

  const [
    addCommentToPost,
    {loading: isAddCommentMutationLoading},
  ] = useMutation(ADD_COMMENT_TO_POST, {
    onCompleted: refetch,
  });

  useEffect(() => {
    return () => {
      Keyboard.dismiss();
    };
  }, []);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: data?.getPostComments?.data?.edges
        ? `Comments (${data?.getPostComments?.data?.edges?.length})`
        : 'Comments',
    });
    console.log("isMember========", postData?.getPost?.data?.authentication_type?.id)
  }, [data?.getPostComments?.data?.edges, navigation]);

  const handleProfileView = (userId, isExpert = false) => {
    navigation.navigate(
      isExpert ? screens.EXPERTS_PROFILE : screens.PUBLIC_PROFILES,
      {
        ...(isExpert ? {expertUserId: userId} : {params: userId}),
      },
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (_) {}
    }, [refetch]),
  );

  const renderItem = useCallback(
    ({item}) => {
      return item?.post_image_comment == null ? (
        <View style={styles.commentCard}>
          <TouchableOpacity
            onPress={handleProfileView.bind(
              null,
              item?.user?.id,
              item?.user?.role === 'EXPERT',
            )}>
            <ProfileImage url={item?.user?.image} style={{marginRight: 10}} />
          </TouchableOpacity>
          <View style={styles.textWrapper}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text style={styles.username}>{item?.user?.username}</Text>
              <Text style={styles.date}>
                {dayjs(item?.created_at)?.fromNow()}
              </Text>
            </View>
            <Text style={styles.commentText}>
              <TextAutoLink text={item?.message} />
            </Text>
          </View>
        </View>
      ) : (
        <View
          style={{
            backgroundColor: colors.white,
            marginTop: 15,
            marginHorizontal: spacings.lg,
            padding: spacings.md,
            borderRadius: 4,
          }}>
          <View style={{flexDirection: 'row'}}>
            <ProfileImage url={item?.user?.image} style={{marginRight: 10}} />
            <View style={styles.textWrapper}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 5,
                  flexWrap: 'wrap',
                }}>
                <Text style={styles.username} numberOfLines={1}>
                  {item?.user?.username}
                </Text>
                <Text style={styles.date}>
                  {dayjs(item?.created_at)?.fromNow()}
                </Text>
              </View>
              <Text style={styles.commentText}>
                <TextAutoLink text={item?.post_image_comment?.message} />
              </Text>
              <ImageComment
                imageURL={item?.post_image_comment?.item_image_url}
                key={item?.post_image_comment?.id}
              />
            </View>
          </View>
        </View>
      );
    },
    [handleProfileView],
  );

  const commentOnPost = useCallback(async () => {
    try {
      const {data: _data} = await addCommentToPost({
        variables: {
          postId: parseInt(postId, 10),
          type: 'TEXT',
          message: comment,
        },
      });
      const success = _data?.addCommentToPost?.success;
      if (success) {
        setComment('');
        setHeight(40);
        Keyboard.dismiss();
      } else {
        throw new Error();
      }
    } catch (_) {
      showToast({message: locale.SomethingWentWrongPleaseTryAgain});
    }
  }, [addCommentToPost, comment, postId]);

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  const canComment = React.useMemo(
    () => isMember && postData?.getPost?.data?.authentication_type?.id == 1,
    [isMember, postData?.getPost?.data?.authentication_type?.id],
  );

  const handleViewPost = React.useCallback(() => {
    navigation.push(screens.SINGLE_LISTING, {
      params: postId,
    });    
  }, [navigation.push, postId]);

  return Array.isArray(data?.getPostComments?.data?.edges) ? (
    <KeyboardAvoidingView
      style={[styles.container, canComment ? {marginBottom: bottomOffset} : {}]}
      {...(isIOS
        ? {
            behavior: isIOS ? 'position' : 'height',
            keyboardVerticalOffset: sendViewHeight,
          }
        : {})}
      contentContainerStyle={{flex: 1}}>
      <FlatList
        data={data?.getPostComments?.data?.edges}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.commentWrapper,
          Array.isArray(data?.getPostComments?.data?.edges) &&
          data?.getPostComments?.data?.edges?.length > 0
            ? {}
            : {flex: 1},
        ]}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <DefaultEmptyComponent
              lottieStyle={{width: 80, height: 80, alignSelf: 'center'}}
              text={locale.ThereAreNoCommentsAtTheMoment}
              textStyle={{fontSize: 14}}
            />
          </View>
        }
        ListHeaderComponent={
          showPostNavigationLink ? (
            <TouchableOpacity
              onPress={handleViewPost}
              activeOpacity={0.5}
              style={{
                alignSelf: 'flex-end',
                paddingHorizontal: 20,
              }}>
              <Text
                style={{
                  fontFamily: 'Inter-Medium',
                  fontSize: 16,
                  color: colors.primary,
                }}>
                View Full Post
              </Text>
            </TouchableOpacity>
          ) : null
        }
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        initialNumToRender={10}
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
      />
      {canComment ? (
        <CommentInput
          key="comment-input"
          comment={comment}
          setComment={setComment}
          height={height}
          setHeight={setHeight}
          sendViewHeight={sendViewHeight}
          setSendViewHeight={setSendViewHeight}
          onSubmit={commentOnPost}
          renderRightComponent={
            <TouchableOpacity
              style={[styles.button]}
              onPress={commentOnPost}
              disabled={comment?.length == 0}>
              {isAddCommentMutationLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <Icon name="send" size={20} tint={colors.white} />
              )}
            </TouchableOpacity>
          }
        />
      ) : null}
    </KeyboardAvoidingView>
  ) : loading ? (
    <View style={styles.loadingContainer}>
      <ActivityIndicator color={colors.primary} />
    </View>
  ) : null;
};

function ImageComment({imageURL}) {
  const {
    window: {width: windowWidth},
  } = useWindowDimensions();

  const [visible, setVisible] = React.useState(false);

  return (
    <>
      <TouchableOpacity activeOpacity={0.5} onPress={() => setVisible(true)}>
        <FastImage
          source={{uri: imageURL}}
          style={{
            width: 0.4 * windowWidth,
            height: 0.3 * windowWidth,
            marginTop: 20,
            borderRadius: 8,
          }}
        />
      </TouchableOpacity>

      <ImageViewer
        visible={visible}
        setVisible={setVisible}
        images={[{url: imageURL}]}
      />
    </>
  );
}

const SELECTION_INITIAL_STATE = {
  isMatched: false,
  cursorEnd: 0,
  text: '',
};
function CommentInput({
  setSendViewHeight = () => null,
  comment = '',
  setComment = () => null,
  height = 0,
  setHeight = () => null,
  renderRightComponent = null,
  onSubmit = () => null,
}) {
  const inputRef = React.useRef();
  const firstMountRef = React.useRef(true);
  const selectionTimerRef = React.useRef();

  const [nativeSelection, setNativeSelection] = React.useState({
    start: 0,
    end: 0,
  });

  const [selection, setSelection] = React.useState({
    ...SELECTION_INITIAL_STATE,
  });

  const handleSelectionChange = React.useCallback((text, end) => {
    if (text?.length > 0) {
      const selection = text?.slice(0, end);
      const split = selection?.split(' ');
      const currentWord = split?.length > 0 ? split[split.length - 1] : null;
      const isMatched =
        !(currentWord == null) &&
        /[@][A-Za-z][A-Za-z0-9]*(?:[.|_][A-Za-z0-9]+)*/g.test(currentWord);
      setSelection(() => ({
        isMatched,
        cursorEnd: end,
        text: isMatched ? currentWord : '',
      }));
    } else {
      setSelection({...SELECTION_INITIAL_STATE});
    }
  }, []);

  React.useEffect(() => {
    if (!firstMountRef.current) {
      if (selectionTimerRef.current) {
        clearTimeout(selectionTimerRef.current);
      }
      selectionTimerRef.current = setTimeout(() => {
        handleSelectionChange(comment, nativeSelection.end);
      }, 100);
    } else {
      firstMountRef.current = false;
    }
    return () => {
      clearTimeout(selectionTimerRef.current);
    };
  }, [nativeSelection, comment]);

  const handleUserItemPress = React.useCallback(
    user => {
      if (selection.isMatched) {
        setComment(previousComment => {
          return `${previousComment?.slice(
            0,
            selection.cursorEnd - selection.text.length,
          )}@${user?.username} ${previousComment.slice(selection.cursorEnd)}`;
        });
        setSelection({...SELECTION_INITIAL_STATE});
      }
    },
    [selection],
  );

  const handleSubmit = () => {
    inputRef.current?.blur();
    setSelection({...SELECTION_INITIAL_STATE});
    onSubmit();
  };

  const handleOnBlur = () => {
    setSelection({...SELECTION_INITIAL_STATE});
  };

  return (
    <View
      style={{
        flex: 1,
        ...(selection.isMatched
          ? {
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              opacity: 1,
            }
          : {}),
      }}>
      <Animated.View
        entering={FadeInDown.delay(500).springify().stiffness(500).damping(50)}
        style={[styles.commentSection, {zIndex: 10}]}
        onLayout={evt => {
          setSendViewHeight(evt?.nativeEvent?.layout?.height);
        }}>
        <TextInput
          ref={inputRef}
          placeholder="Write comment..."
          value={comment}
          onChangeText={setComment}
          multiline={true}
          placeholderTextColor={colors.grey}
          selectionColor={colors.darkGrey}
          style={[styles.inputStyle, {height: height}]}
          onContentSizeChange={e =>
            setHeight(e?.nativeEvent?.contentSize?.height)
          }
          maxLength={256}
          onSelectionChange={({nativeEvent: {selection}}) => {
            setNativeSelection(selection);
          }}
          onBlur={handleOnBlur}
        />
        {React.cloneElement(renderRightComponent, {
          onPress: handleSubmit,
        })}
      </Animated.View>
      <UsernameAutocompleteSheet
        term={selection.isMatched ? selection.text : ''}
        onItemPress={handleUserItemPress}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGrey,
    paddingTop: 15,
  },
  commentSection: {
    backgroundColor: colors.black,
    flexDirection: 'row',
    paddingVertical: spacings.lg,
    alignItems: 'center',
    bottom: 0,
    position: 'absolute',
  },
  commentWrapper: {
    paddingBottom: 100,
  },
  commentCard: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    marginTop: 15,
    marginHorizontal: spacings.lg,
    padding: spacings.md,
    borderRadius: 4,
  },
  commentText: {
    color: colors.black,
    fontSize: fontSize.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    color: colors.black,
    fontSize: fontSize.md,
  },
  textWrapper: {
    marginLeft: spacings.sm,
    flex: 1,
  },
  date: {
    color: '#828282',
    fontSize: fontSize.sm,
  },
  username: {
    fontWeight: 'bold',
    color: colors.black,
    fontSize: fontSize.md,
  },
  inputStyle: {
    flex: 1,
    backgroundColor: '#222',
    marginRight: spacings.sm,
    borderRadius: 4,
    paddingLeft: spacings.md,
    marginLeft: spacings.lg,
    color: colors.white,
    minHeight: 45,
  },
  dpStyles: {
    height: 40,
    width: 40,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: colors.lightGrey,
  },
  button: {
    padding: spacings.md,
    marginRight: spacings.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
});

export default Comments;
