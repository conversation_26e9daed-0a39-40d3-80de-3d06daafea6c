import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {useMutation} from '@apollo/client';
import {useNavigation} from '@react-navigation/native';

import Layout from '../../layout';
import TextInput from '../../components/TextInput';
import locale from '../../assets/locale.json';
import styles from './styles';
import {useInput} from '../../tools/hooks';
import colors from '../../assets/colors';
import Button from '../../components/Button';
import {SEND_CONTACT_MESSAGE} from '../../apollo/mutations';
import InfoModal from '../../components/Modal/InfoModal';
import {screens} from '../../assets/strings';
import {useUser} from '../../tools/hooks';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

export default function ContactUs() {
  const {user} = useUser({focused: true});
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const {
    values,
    invalid,
    checkIsEmpty,
    handleChange,
    setInvalid,
    resetValues,
  } = useInput(
    ['name', 'title', 'description'],
    ['name', 'title', 'description'],
    {name: user?.first_name},
  );

  const navigation = useNavigation();

  const {name, title, description} = values;
  const {
    name: invalidName,
    title: invalidTitle,
    description: invalidDescription,
  } = invalid;

  const [sendContactMessage, {loading}] = useMutation(SEND_CONTACT_MESSAGE);

  const submit = React.useCallback(async () => {
    try {
      const {data} = await sendContactMessage({
        variables: {
          title,
          description,
          firstName: name,
        },
      });
      const success = data?.sendContactMessage?.success;
      if (success) {
        resetValues();
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: false,
          data: {
            title: locale.ThankYou,
            description: locale['YourMessageHasBeenSent...'],
          },
        }));
      } else {
        throw new Error();
      }
    } catch (_) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
        isError: true,
        data: {
          title: locale.Error,
          description: locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    }
  }, [name, title, description, resetValues, sendContactMessage]);

  const handleSubmit = React.useCallback(() => {
    if (checkIsEmpty[1]) {
      setInvalid(previousState => ({...previousState, ...checkIsEmpty[0]}));
      return;
    }
    submit();
  }, [checkIsEmpty, setInvalid, submit]);

  const handleInputChange = React.useCallback(
    (type, value) => {
      if (invalid[type]) {
        setInvalid(preState => ({...preState, [type]: false}));
      }
      handleChange(type, value);
    },
    [invalid, setInvalid, handleChange],
  );

  const renderFirstName = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <Text style={styles.inputLabel}>
          <Text style={{color: colors.warning}}>*</Text> {locale.FirstName}
        </Text>
        <TextInput
          value={name}
          onChange={value => handleInputChange('name', value)}
          isInvalid={invalidName}
          invalidText={locale.FirstNameIsRequired}
        />
      </View>
    ),
    [handleInputChange, invalidName, name],
  );

  const renderTitleInput = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <Text style={styles.inputLabel}>
          <Text style={{color: colors.warning}}>*</Text> {locale.Subject}
        </Text>
        <TextInput
          value={title}
          onChange={value => handleInputChange('title', value)}
          isInvalid={invalidTitle}
          invalidText={locale.SubjectIsRequired}
        />
      </View>
    ),
    [handleInputChange, invalidTitle, title],
  );

  const renderDescriptionInput = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <Text style={styles.inputLabel}>
          <Text style={{color: colors.warning}}>*</Text> {locale.Message}
        </Text>
        <TextInput
          value={description}
          onChange={value => handleInputChange('description', value)}
          isInvalid={invalidDescription}
          invalidText={locale.MessageIsRequired}
          numberOfLines={6}
          textAlignVertical="top"
          multiline
          textArea
        />
      </View>
    ),
    [handleInputChange, invalidDescription, description],
  );

  return (
    <Layout contentContainerStyle={styles.container}>
      <Text style={styles.heading}>{locale.ContactUs}</Text>
      <View>
        <TouchableOpacity
          style={{
            paddingVertical: 5,
          }}
          onPress={() => {
            navigation.navigate(screens.FREQUENTLY_ASKED_QUESTIONS);
          }}>
          <Text
            style={{
              fontSize: 16,
              fontFamily: 'Inter-Medium',
              color: colors.primary,
            }}>
            You can always visit our 
            <Text style={{textDecorationLine: 'underline'}}>FAQ</Text> page that
            might answer your question.
          </Text>
        </TouchableOpacity>
        {renderFirstName}
        {renderTitleInput}
        {renderDescriptionInput}
      </View>
      <View style={{flex: 1}} />
      <Button
        text={locale.Submit}
        style={{paddingVertical: 15}}
        onPress={handleSubmit}
        isLoading={loading}
        disabled={loading}
      />
      <Layout.Overlay>
        <InfoModal
          setVisible={() => {
            if (!popup.isError) {
              navigation.navigate(screens.TAB_NAVIGATOR);
            }
            setPopup(previousState => ({
              ...previousState,
              state: false,
            }));
          }}
          popUp={popup}
        />
      </Layout.Overlay>
    </Layout>
  );
}
