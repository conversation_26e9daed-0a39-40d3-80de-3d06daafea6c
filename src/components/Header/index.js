import React from 'react';
import {Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import colors from '../../assets/colors';
import styles from './styles.js';
import HeaderButton from './HeaderButton.js';

export default function Header({
  title = '',
  backgroundColor = colors.black,
  borderColor = colors.white,
  left,
  right,
  iconStyle = {},
  noShadow = false,
  enableTopSafeAreaInset = false,
}) {
  const {top} = useSafeAreaInsets();
  return (
    <View
      style={[
        {
          backgroundColor,
          borderColor,
          zIndex: 1,
          ...(enableTopSafeAreaInset ? {marginTop: top} : {}),
          ...styles.containerWrapper,
        },
        !noShadow
          ? {
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }
          : {},
      ]}>
      <View
        style={{
          ...styles.container,
        }}>
        <HeaderButton
          action={left}
          style={styles.headerLeft}
          iconStyle={iconStyle}
        />
        <View style={styles.titleContainer}>
          <Text style={styles.title} numberOfLines={1}>
            {title}
          </Text>
        </View>
        <HeaderButton
          action={right}
          style={styles.headerRight}
          iconStyle={iconStyle}
        />
      </View>
    </View>
  );
}
