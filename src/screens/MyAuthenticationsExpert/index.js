import React, {useCallback, useState} from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Text,
  RefreshControl,
  Platform,
} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

import MyAuthenticatonsLoader from '../../components/Loaders/MyAuthenticatonsLoader';
import {GET_ALL_POSTS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import MyAuthCardExpert from '../../components/MyAuthCardExpert';
import {fontSize} from '../../assets/font';
import {icons, screens} from '../../assets/strings';
import Icon from '../../components/Icon';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import BottomSheet from '../../components/BottomSheet';
import locale from '../../assets/locale.json';
import {DELETE_POST, CLAIM_NFT, SET_POST_STATUS, SET_POST_APPRAISAL_STATUS} from '../../apollo/mutations';
import InfoModal from '../../components/Modal/InfoModal';
import ListActionRow from '../../components/ListActionRow';
import {useAuthenticationInfo, usePostShareURLConstructor} from '../../tools/hooks';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import checkPermission from '../../tools/permissions';
import { permissionStorage } from '../../tools/utils';
import {showToast} from '../../components/Toast';
import Share from 'react-native-share';
import RNFetchBlob from 'rn-fetch-blob';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';

import axios from 'axios';
import {API_AI_PREDICT_URL, 
  API_AI_PREDICT_URL_DEV,
  API_AI_PREDICT_EXPERT_URL, 
  API_AI_PREDICT_EXPERT_URL_DEV, 
  API_AI_PREDICT_APPRAISAL_URL_DEV, 
  API_AI_PREDICT_APPRAISAL_URL} from '@env';
import RNFS from 'react-native-fs';
import AIInfoModal from '../../components/Modal/AIInfoModal';
import { navigate } from '../../navigators/NavigationService';
import { Console } from 'console';
import { GET_EXPERT_QUEUE } from '../../tools/api';


const SHEET_INITIAL_STATE = {
  visible: false,
  authenticationId: null,
  completeItem: null,
  authenticationType: null,
  canClaimNFT: false,
};

const {height: SCREEN_HEIGHT} = Dimensions.get('screen');

const VOTES = [
  {
    id: 'REAL',
    name: 'Pass',
  },
  {
    id: 'FAKE',
    name: 'Fail',
  },
  {
    id: 'UNDECIDED',
    name: 'Undecided',
  },
  {
    id: 'UNDETERMINED',
    name: 'Undetermined',
  },
];

const LIMIT = 100;

const Index = ({navigation}) => {
  const [authType, setAuthType] = useState(null);
  const [decision, setDecision] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [sheetState, setSheetState] = React.useState(SHEET_INITIAL_STATE);
  const [currentPrivateStatus, setCurrentPrivateStatus] = React.useState(false);
  const [currentAppraisalPrivateStatus, setCurrentAppraisalPrivateStatus] = React.useState(false);
  const [loading, setLoading] = useState(true);
  const [postData, setPostData] = useState(null);

  const [permissionGranted, setPermissionGranted] = React.useState(false);
  const permissionRef = React.useRef(false);
  const isIOS = Platform.OS === 'ios';

  const scrollRef = React.useRef();

  /*const {data, refetch, loading} = useQuery(GET_ALL_POSTS, {
    
    variables: {
      limit: LIMIT, // TODO add pagination
      filters: {
        myself: true,
        ...(decision == null ? {} : {decision}),
        ...(authType == null ? {} : {authenticate_type: authType}),
      },
      cacheId: APOLLO_CACHE_IDS.getPosts_MY_AUTHENTICATIONS,
    },
  });*/

  const refetch = async () => {
    setLoading(true);
    try {

      const {data: response} = await GET_EXPERT_QUEUE();
      if (response?.success) {
        const data = response?.data;
        setPostData(data);
      }
      //const posts = await fetchPosts(decision, authType);
      //setData(posts);
    } catch (error) {
      //setError(error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    console.log("Kapil=====>", authType);
    try {
      await refetch();
    } catch (error) {
    } finally {
      setRefreshing(false);
    }
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch();
        }
      } catch (_) {}
    }, []),
  );

  const handleVoteSelection = value => {
    setDecision(value);
  };

  const handleAuthSelection = value => {
    setAuthType(value);
  };

  const handleMenuPressed = item => {

    /*const url = usePostShareURLConstructor({
      authenticationKey: item.authentication_key,
    });
    handleDownload(url.url)*/
    //console.log("item=====>", item)

    //setCurrentPrivateStatus(item?.post_status_type === 'PRIVATE');
    //setCurrentAppraisalPrivateStatus(item?.is_private === true);
    setSheetState({
      visible: true,
      authenticationId: item?.id,
      completeItem: item,
      canClaimNFT:
        (item?.authentication_type?.id == 3 && item?.can_claim_nft) || false,
      authenticationType: item?.authentication_type?.id,
    });
  };

  const resetSheet = () => {
    setSheetState(SHEET_INITIAL_STATE);
  };

  const renderItem = React.useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          onPress={() =>
            navigation.navigate(screens.SINGLE_LISTING, {
              //params: item?.id,
              key: item?.authentication_key,
            })
          }
          onLongPress={handleMenuPressed.bind(null, item)}
          scaleAnimationThreshold={0.97}>
          <MyAuthCardExpert
            item={item}
            onMenuPressed={handleMenuPressed}
            showAuthenticationPendingStatus
          />
        </TouchAwareButton>
      );
    },
    [navigation.navigate, handleMenuPressed],
  );

  const renderSheet = React.useMemo(
    () => (
      
      <BottomSheet
        visible={sheetState.visible}
        setVisible={resetSheet}
        automaticallyAdjustHeight
        key={sheetState.authenticationId}>
        <View style={{minHeight: 50, padding: 20}}>
          {/* {sheetState.canClaimNFT ? (
            <ClaimNFT
              authenticationId={sheetState.authenticationId}
              resetSheet={resetSheet}
              refetch={refetch}
              navigationPush={navigation.navigate}
            />
          ) : null} */}
          {/* {[2, 3].includes(sheetState.authenticationType) ? (
            <PostStatusButton
              authenticationId={sheetState.authenticationId}
              resetSheet={resetSheet}
              refetch={refetch}
              isPrivate={currentPrivateStatus}
            />
          ) : null} */}
{/* 
          {[2, 3, 5].includes(sheetState.authenticationType) ? (
            <PostAppraisalButton
              authenticationId={sheetState.authenticationId}
              resetSheet={resetSheet}
              refetch={refetch}
              isPrivate={currentAppraisalPrivateStatus}
            />
          ) : null} */}

<PostAIExpertButton
              completeItem={sheetState.completeItem}
              title={'AI Lens Beta'}
              url={process.env.NODE_ENV === 'development' ? API_AI_PREDICT_URL_DEV : API_AI_PREDICT_URL}
              appraisalURL={process.env.NODE_ENV === 'development' ? API_AI_PREDICT_APPRAISAL_URL_DEV : API_AI_PREDICT_APPRAISAL_URL}
              resetSheet={resetSheet}
              refetch={refetch}
            />

        {/* {[2, 3, 5].includes(sheetState.authenticationType) ? (
            <PostAIExpertButton
              completeItem={sheetState.completeItem}
              title={'AI Lens Beta'}
              url={process.env.NODE_ENV === 'development' ? API_AI_PREDICT_URL_DEV : API_AI_PREDICT_URL}
              appraisalURL={process.env.NODE_ENV === 'development' ? API_AI_PREDICT_APPRAISAL_URL_DEV : API_AI_PREDICT_APPRAISAL_URL}
              resetSheet={resetSheet}
              refetch={refetch}
            />
          ) : null} */}

        {/* {[2, 3, 5].includes(sheetState.authenticationType) ? (
            <PostAIExpertButton
              completeItem={sheetState.completeItem}
              title={`AI Expert Feedback`}
              url={process.env.NODE_ENV === 'development' ? API_AI_PREDICT_EXPERT_URL_DEV : API_AI_PREDICT_EXPERT_URL}
              appraisalURL={process.env.NODE_ENV === 'development' ? API_AI_PREDICT_APPRAISAL_URL_DEV : API_AI_PREDICT_APPRAISAL_URL}
              resetSheet={resetSheet}
              refetch={refetch}
              
            />
          ) : null} */}

         
{/*             
          <DeleteButton
            authenticationId={sheetState.authenticationId}
            resetSheet={resetSheet}
            refetch={refetch}
          /> */}
        </View>
      </BottomSheet>
    ),
    [
      sheetState,
      resetSheet,
      refetch,
      currentPrivateStatus,
      navigation.navigate,
    ],
  );

  async function downloadImageBase64(qrCodeURL, filename) {
    
    try {
      const response = await fetch(qrCodeURL);
      const blob = await response.blob();
      const base64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]); // Remove the data URL prefix
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
  
      //await RNFS.writeFile(downloadDest, base64, 'base64');
      return base64;
    } catch (error) {
      console.error('Error downloading image:', error);
      throw error;
    }
  }
  
  async function handleDownload(downloadURL) {
      
    const qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data='+downloadURL;
  
    try {
      if (!permissionGranted) {
        permissionRef.current = true;
        await checkPermission(
          permissionStorage,
          setPermissionGranted,
          true,
          true,
          false,
        );
      } else {
        console.log("URL======ssss>", qrCodeUrl);
        const filename = `image_code.jpg`;
        const base64Data = await downloadImageBase64(qrCodeUrl, filename);
        
  
        if (isIOS) {
          await Share.open({
            //url: `data:image/png;base64,${svgRef.current}`,            
            url: `data:image/png;base64,${base64Data}`,            
            failOnCancel: false,
          });
        } else {
          const fs = RNFetchBlob.fs;
          const PictureDir = fs.dirs.CacheDir;
          const path = `${PictureDir}/image_${+new Date()}.png`;
          //await fs.writeFile(path, svgRef.current, 'base64');
          await fs.writeFile(path, base64Data, 'base64');
          await CameraRoll.save(path, 'photo');
          showToast({type: 'success', message: locale.QrCodeSaved});
        }
      }
    } catch (_) {
      showToast({message: locale['SomethingWentWrong.']});
    }
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={postData}
        renderItem={renderItem}
        keyExtractor={item => item?.id?.toString?.()}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          postData == null && loading ? null : (
            <View style={styles.emptyView}>
              <Icon
                name={icons.UPLOAD_PHOTO}
                size={70}
                tint={colors.darkGrey}
              />
              <Text style={styles.emptyViewText}>{locale.NoPostsFound}</Text>
            </View>
          )
        }
        // ListHeaderComponent={
        //   <Header
        //     handleVoteSelection={handleVoteSelection}
        //     handleAuthSelection={handleAuthSelection}
        //   />
        // }
        ListFooterComponent={
          postData == null && loading ? (
            <MyAuthenticatonsLoader />
          ) : null
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            progressBackgroundColor={colors.lightGrey}
            colors={[colors.primary, colors.black]}
            tintColor={colors.primary}
            onRefresh={onRefresh}
          />
        }
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
      />
      {renderSheet}
    </View>
  );
};

const FILTER_TYPE = {
  VOTE: 'VOTE',
  TYPE: 'TYPE',
};

function Header({
  handleAuthSelection = () => null,
  handleVoteSelection = () => null,
}) {
  const [filterType, setFilterType] = React.useState(FILTER_TYPE.TYPE);
  const [visible, setVisible] = useState(false);
  const [selectedType, setSelectedType] = React.useState(null);
  const [selectedVote, setSelectedVote] = React.useState(null);

  const {authenticationTypes} = useAuthenticationInfo();

  const resetSheet = () => {
    setVisible(false);
  };

  const handleFilterSelection = React.useCallback((type = FILTER_TYPE.TYPE) => {
    setFilterType(type);
    setVisible(true);
  }, []);

  const handleItemSelection = React.useCallback(
    (item, type = FILTER_TYPE.TYPE) => {
      if (type == FILTER_TYPE.TYPE) {
        let _type;
        setSelectedType(previousState => {
          _type =
            previousState == null
              ? item
              : previousState?.id == item?.id
                ? null
                : item;
          return _type;
        });
        handleAuthSelection(_type?.id ?? null);
      } else {
        let _decision;
        setSelectedVote(previousState => {
          _decision =
            previousState == null
              ? item
              : previousState?.id == item?.id
                ? null
                : item;
          return _decision;
        });
        handleVoteSelection(_decision?.id ?? null);
      }
      resetSheet();
    },
    [resetSheet],
  );

  return (
    <View style={styles.topBarButtonsWrapper}>
      <TouchableOpacity
        style={styles.topBarButtons}
        onPress={() => handleFilterSelection(FILTER_TYPE.TYPE)}>
        <Text style={styles.topBarButtonsText} numberOfLines={1}>
          Type: {selectedType == null ? 'All' : selectedType?.name}
        </Text>
        <Icon
          name={icons.CHEVRON_DOWN}
          tint={colors.darkGrey}
          size={fontSize.md}
        />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.topBarButtons}
        onPress={() => handleFilterSelection(FILTER_TYPE.VOTE)}>
        <Text style={styles.topBarButtonsText} numberOfLines={1}>
          Result: {selectedVote == null ? 'All' : selectedVote?.name}
        </Text>
        <Icon
          name={icons.CHEVRON_DOWN}
          tint={colors.darkGrey}
          size={fontSize.md}
        />
      </TouchableOpacity>
      <BottomSheet
        visible={visible}
        setVisible={resetSheet}
        automaticallyAdjustHeight
        key={filterType}>
        <View style={{minHeight: 50, padding: 20}}>
          {filterType == FILTER_TYPE.TYPE
            ? authenticationTypes
              ?.filter(item => !(item?.id == 4))
              ?.map(item => (
                <TouchableOpacity
                  key={item?.id}
                  style={styles.optionWrapper}
                  onPress={() => handleItemSelection(item, FILTER_TYPE.TYPE)}>
                  <Text style={styles.modalLabel}>{item?.name}</Text>
                  <Icon
                    name={
                      selectedType?.id == item?.id
                        ? icons.CHECKED_RADIO
                        : icons.UNCHECKED_RADIO
                    }
                    size={20}
                    tint={
                      selectedType?.id == item?.id
                        ? colors.primary
                        : colors.grey
                    }
                  />
                </TouchableOpacity>
              ))
            : VOTES?.map(item => (
              <TouchableOpacity
                key={item?.id}
                style={styles.optionWrapper}
                onPress={() => handleItemSelection(item, FILTER_TYPE.VOTE)}>
                <Text style={styles.modalLabel}>{item?.name}</Text>
                <Icon
                  name={
                    selectedVote?.id == item?.id
                      ? icons.CHECKED_RADIO
                      : icons.UNCHECKED_RADIO
                  }
                  size={20}
                  tint={
                    selectedVote?.id == item?.id
                      ? colors.primary
                      : colors.grey
                  }
                />
              </TouchableOpacity>
            ))}
        </View>
      </BottomSheet>
    </View>
  );
}


function PostAIExpertButton({
  completeItem = null,
  title = null,
  url=null,
  appraisalURL=null,
  resetSheet = () => null,
  refetch = () => null,
}) {

  const [popup, setPopup] = React.useState(false);
  const [popupOnResponse, setPopupOnResponse] = React.useState(false);
  const [aiPopupOnResponse, setAIPopupOnResponse] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState(locale.SomethingWentWrongPleaseTryAgain);
  const [successMessage, setSuccessMessage] = React.useState("");
  const [successList, setSuccessList] = React.useState([]);
  const [aiPassResultImageURLs, setAIPassResultImageURLs] = React.useState([]);
  const [aiFailResultImageURLs, setAIFailResultImageURLs] = React.useState([]);

  const imageUrls = [
    { uri: completeItem.images.front, title: "Front" },
    //{ uri: completeItem.images.back, title: "Back" },    
    //{ uri: completeItem.images.front_tag, title: "Tag" },        
    //{ uri: completeItem.images.lower_hem_stitching, title: "Stitching Hem" },
    //{ uri: completeItem.images.arm_hem_stitching, title: "Stitching Sleeve" },
    //{ uri: completeItem.images.copyright, title: "Copyright" },        
    //completeItem.images.closeup_back,    
    //completeItem.images.back_tag,
    //completeItem.images.extra,   
].filter(url => url.uri !== null && url.uri !== undefined);

  // const handleNavigate = (aiModelResult) => {
    
  //   const front = {images: []};
  //   const back = {images: []};
  //   const tag = {images: []};
  //   const copyright = {images: []};
  //   const stitichingSleeve = {images: []};
  //   const stitichingHem = {images: []};


  //   aiFailResultImageURLs.forEach((item, index) => {
  //     // You can adjust this according to how you want to structure your navigation data
  //     if (item.title == 'Front') {
  //       front.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //     } else if (item.title == 'Back') {
  //       back.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //     } else if (item.title == 'Tag') {
  //       tag.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //     } else if (item.title == 'Copyright') {
  //       copyright.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //     } else if (item.title == 'Stitching Sleeve') {
  //       stitichingSleeve.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //     } else if (item.title == 'Stitching Hem') {
  //       stitichingHem.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //     }
  //   });
    
  //   aiPassResultImageURLs.forEach((item, index) => {
  //     // You can adjust this according to how you want to structure your navigation data
  //     const aiModel = aiModelResult[index];
  //     if (item.title == 'Front') {
  //       front.images.push({type: item.title, uri: item.uri, passed: item.passed});

  //       const appraisalValue = aiModel.appraisal_value;
  //       aiModel.images.forEach((urlItem, urlsIndex) => {
  //         front.images.push({type: item.title, uri: urlItem, passed: item.passed});
  //       })
  //     } else if (item.title == 'Back') {
  //       back.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //       aiModel.images.forEach((urlItem, urlsIndex) => {
  //         back.images.push({type: item.title, uri: urlItem, passed: item.passed});
  //       })
  //     } else if (item.title == 'Tag') {
  //       tag.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //       aiModel.images.forEach((urlItem, urlsIndex) => {
  //         tag.images.push({type: item.title, uri: urlItem, passed: item.passed});
  //       })
  //     } else if (item.title == 'Copyright') {
  //       copyright.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //       aiModel.images.forEach((urlItem, urlsIndex) => {
  //         copyright.images.push({type: item.title, uri: urlItem, passed: item.passed});
  //       })
  //     } else if (item.title == 'Stitching Sleeve') {
  //       stitichingSleeve.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //       aiModel.images.forEach((urlItem, urlsIndex) => {
  //         stitichingSleeve.images.push({type: item.title, uri: urlItem, passed: item.passed});
  //       })
  //     } else if (item.title == 'Stitching Hem') {
  //       stitichingHem.images.push({type: item.title, uri: item.uri, passed: item.passed});
  //       aiModel.images.forEach((urlItem, urlsIndex) => {
  //         stitichingHem.images.push({type: item.title, uri: urlItem, passed: item.passed});
  //       })
  //     }
  //   });
  //   navigate(screens.AIRESULT, {front, back, tag, copyright, stitichingSleeve, stitichingHem});
  // };


// const handlePostAIForAppraisal = React.useCallback(async (localPaths, results) => {
    
//   try {
//     if (localPaths.length > 0) {

//       imageUrls.forEach((item, index) => {

//         const result = results[index];
//         const result2 = localPaths[index];

//         if (result.prediction == 'fake') {
//           aiFailResultImageURLs.push({title: item.title, uri: item.uri, passed: false});
//         } else {
//           aiPassResultImageURLs.push({title: item.title, uri: item.uri, passed: true});
//         }
//       })
      
//       // Upload the locally saved images
//       const formData = new FormData();
//       for (const imagePath of aiPassResultImageURLs) {
      
//         const fileNew = {
//           uri: imagePath.uri,
//           type: 'image/jpeg',
//           name: imagePath.uri.split('/').pop(),
//         };
//         formData.append('images', fileNew);
//       }
//       try {
//         await fetch(appraisalURL, {
//           method: 'POST',
//           body: formData,
//           headers: {
//             'Content-Type': 'multipart/form-data',
//           },
//         }).then((response) => {
//           if (!response.ok) {
//             throw new Error('Network response was not ok', appraisalURL);
//           }
//           return response.json();
//         })
//         .then((data) => {
          
//           if (data.results.length > 0) {          
//             /*let appraisalValue = "";
//             for (const [index, results] of data.results.entries()) {
//               appraisalValue = results.appraisal_value
//               break
//             }

//             const newItem = {
//               textBefore: "Pass",
//               imageUri: "",
//               //textAfter: `$${appraisalURL}`,
//               textAfter: `$${Math.round(+appraisalValue)}`,
//             };

            
//             const arrayS = [newItem];
//             const finalResult = arrayS.join("\n");
//               setSuccessMessage(finalResult)
//               setSuccessList(arrayS)
//               setPopupOnResponse(true);*/

//               //setAIPopupOnResponse(true)              
              
              
//               handlePress('');
//               handleNavigate(data.results);
//               /*navigate(screens .AIRESULT, {
                
//                 front: {
//                   images: [
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: true },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                   ],
//                 },
               
//                 tag: {
//                   images: [
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: true },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                     { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
//                   ],
//                 },
               
//               })*/
            
//           }
//         })

//         //const { front, back, tag, copyright, stitichingSleeve, stitichingHem} = route.params;
//         .catch((error) => {
//           console.error('appraisalURL Error uploading image:', error.message);
//           setErrorMessage(error.message)
//           setPopup(true);
//         });
      
//     } catch (error) {
//       console.error('appraisalURL Error uploading imagesssssss:', appraisalURL);
//       setErrorMessage(error.message)
//       setPopup(true);
//       }
//     }     
//   } catch (error) {
//     setErrorMessage(error.message)
//     setPopup(true);
//     console.log("error ", error)
//   } finally {
//     setLoading(false);
//   }
// }, [completeItem]);


  // const handlePostAINormalType = React.useCallback(async () => {
  //   let isAPICalled = 0
  //   setLoading(true);
  //   try {
  //     if (imageUrls.length > 0) {
  //       const localPaths = await Promise.all(imageUrls.map((url, index) => {
  //         const filename = `image_${index}.jpg`; // Use a unique filename
  //         return downloadImage(url.uri, filename);
  //       }));

  //       // Upload the locally saved images
  //       const formData = new FormData();
  //       for (const imagePath of localPaths) {
        
  //         const fileNew = {
  //           uri: imagePath,
  //           type: 'image/jpeg',
  //           name: imagePath.split('/').pop(),
  //         };
  //         formData.append('images', fileNew);
  //       }
  //       try {
  //         await fetch(url, {
  //           method: 'POST',
  //           body: formData,
  //           headers: {
  //             'Content-Type': 'multipart/form-data',
  //           },
  //         }).then((response) => {
  //           if (!response.ok) {
  //             throw new Error('Network response was not ok', url);
  //           }
  //           return response.json();
  //         })
  //         .then((data) => {
  //           console.log('Predication Result:', data);
  //           if (data.predictions.length > 0) {          
              
  //             isAPICalled = 1
  //             handlePostAIForAppraisal(localPaths, data.predictions)
              
  //             if (false) {


  //               const arrayS = []
  //               /*for (const [index, prediction] of data.predictions.entries()) {
                  
  //                 if (prediction.prediction == 'fake') {
  //                   const newItem = {
  //                     textBefore: "Fail",
  //                     imageUri: "",
  //                     textAfter: `${Math.round(+prediction.confidence)}%`,
  //                   };
  //                   arrayS.push(newItem)
  //                 } else if (prediction.prediction == 'real') {
  //                   const newItem = {
  //                     textBefore: "Pass",
  //                     imageUri: "",
  //                     textAfter: `${Math.round(+prediction.confidence)}%`,
  //                   };
  //                   arrayS.push(newItem);
  //                 }
  //               }*/
  //               const fakeConfidence = []
  //               const realConfidence = []
  //               const allConfidence = []

  //               for (const [index, prediction] of data.predictions.entries()) {
  //                 allConfidence.push(+prediction.confidence)
  //                 if (prediction.prediction == 'fake') {
  //                   fakeConfidence.push(+prediction.confidence)
  //                 } else if (prediction.prediction == 'real') {
  //                   realConfidence.push(+prediction.confidence)
  //                 }
  //                 //arrayS.push(index++ +" Image " + prediction.prediction + "\n")
  //                 //arrayS.push(`Image: ${index+1} - ${prediction.prediction}`);
  //                 //arrayS.push(`Image: ${index}, Prediction: ${prediction.prediction}, Confidence: ${prediction.confidence}`);
  //               }
  //               //const result = arrayS.join("\n");
  //               if (fakeConfidence.length > 0) { // it's Fail

  //                 const fakeSum = fakeConfidence.reduce((acc, value) => acc + value, 0);
  //                 const fakeAverage = fakeSum / fakeConfidence.length;
  //                 if (fakeAverage < 60) {
  //                   const newItem = {
  //                     textBefore: "Undetermined",
  //                     imageUri: "",
  //                     textAfter: `NA`,
  //                     //textAfter: `${Math.round(+fakeAverage)}%`,
  //                   };
  //                   arrayS.push(newItem)
  //                 } else {
  //                   const newItem = {
  //                     textBefore: "Fail",
  //                     imageUri: "",
  //                     textAfter: `NA`,
  //                     //textAfter: `${Math.round(+fakeAverage)}%`,
  //                   };
  //                   arrayS.push(newItem)
  //                 }

  //                 const result = arrayS.join("\n");
  //                 setSuccessMessage(result)
  //                 setSuccessList(arrayS)
  //                 setPopupOnResponse(true);
  //               } else { // It's Pass

  //                 isAPICalled = 1
  //                 handlePostAIForAppraisal(localPaths)

  //                 /*let realAverage = 0
  //                 if(realConfidence.length > 0) {
  //                   const realSum = realConfidence.reduce((acc, value) => acc + value, 0);
  //                   realAverage = realSum / realConfidence.length;
  //                 }
  
  //                 const newItem = {
  //                   textBefore: "Pass",
  //                   imageUri: "",
  //                   textAfter: `${Math.round(+realAverage)}%`,
  //                 };
  //                 arrayS.push(newItem);
  
  //                 const result = arrayS.join("\n");
  //                 setSuccessMessage(result)
  //                 setSuccessList(arrayS)
  //                 setPopupOnResponse(true);*/
  //               }


  //               /*let fakeAverage = 0
  //               if(fakeConfidence.length > 0) {
  //                 const fakeSum = fakeConfidence.reduce((acc, value) => acc + value, 0);
  //                 fakeAverage= fakeSum / fakeConfidence.length;
  //               }
  
  //               let realAverage = 0
  //               if(realConfidence.length > 0) {
  //                 const realSum = realConfidence.reduce((acc, value) => acc + value, 0);
  //                 realAverage = realSum / realConfidence.length;
  //               }
  
  //               let allAverage = 0
  //               if(allConfidence.length > 0) {
  //                 const allSum = allConfidence.reduce((acc, value) => acc + value, 0);
  //                 allAverage = allSum / allConfidence.length;
  //               }
  
  //               let result = ""
                
  //               if(allAverage <= 60) {
  //                 result = "AI Predication Undetermined. Confidence level " + Math.round(allAverage) + "%"
  //               } else if(realAverage > fakeAverage) {
  //                 result = "AI Predication Pass. Confidence level " + Math.round(realAverage) + "%"
  //               } else {
  //                 result = "AI Predication Fail. Confidence level " + Math.round(fakeAverage) + "%"
  //               }
  //               console.log(`Result All=${allConfidence}, Real=${realConfidence}, Fail=${fakeConfidence}`)
  //               console.log(`Result All=${allAverage}, Real=${realAverage}, Fail=${fakeAverage}`)*/
  //             }
  //           }
  //           //refetch?.();
  //           //resetSheet();
  //         })
  //         .catch((error) => {
  //           console.error('Error uploading image:', error.message);
  //           setErrorMessage(error.message)
  //           setPopup(true);
  //         });
        
  //     } catch (error) {
  //       console.error('Error uploading image:', error.message);
  //       setErrorMessage(error.message)
  //       setPopup(true);
  //       }
  //     }     
  //   } catch (error) {
  //     setErrorMessage(error.message)
  //     setPopup(true);
  //     console.log("error ", error)
  //   } finally {
  //     if(isAPICalled == 0) {
  //       setLoading(false);
  //     }      
  //   }
  // }, [completeItem]);



  const handleAINavigate = (similarImagesList, overallResult) => {
    
   
    navigate(screens.AIRESULT, {similarImagesList, overallResult});
  };

  
  const handlePostAIForPredictImages_old = React.useCallback(async (localPaths, results) => {
    
    try {
      if (localPaths.length > 0) {
  
        const similarImagesList = [];
        const imagesList = [];
                
        results.forEach((item, index) => {
          const similarImages = item["similar images"];              
          if(similarImages["front"]) {
            imagesList.push(similarImages["front"]);
            similarImagesList.push({type: "Front", uri: similarImages["front"], passed: false});
          } 
          
          if(similarImages["arm_hem_stitching"]) {
            imagesList.push(similarImages["arm_hem_stitching"]);
            similarImagesList.push({type: "Stitching Sleeve", uri: similarImages["arm_hem_stitching"], passed: false});
          } 
          
          if(similarImages["back"]) {
            imagesList.push(similarImages["back"]);
            similarImagesList.push({type: "Back", uri: similarImages["back"], passed: false});
          } 
          
          if(similarImages["back_tag"]) {
            imagesList.push(similarImages["back_tag"]);
            similarImagesList.push({type: "Back Tag", uri: similarImages["back_tag"], passed: false});
          } 
          
          if(similarImages["closeup_back"]) {
            imagesList.push(similarImages["closeup_back"]);
            similarImagesList.push({type: "Closeup Back", uri: similarImages["closeup_back"], passed: false});
          } 
          
          if(similarImages["closeup_front"]) {
            imagesList.push(similarImages["closeup_front"]);
            similarImagesList.push({type: "Closeup Front", uri: similarImages["closeup_front"], passed: false});
          } 
          
          if(similarImages["copyright"]) {
            imagesList.push(similarImages["copyright"]);
            similarImagesList.push({type: "Copyright", uri: similarImages["copyright"], passed: false});
          } 
          
          if(similarImages["front_tag"]) {
            imagesList.push(similarImages["front_tag"]);
            similarImagesList.push({type: "Front Tag", uri: similarImages["front_tag"], passed: false});
          } 
          
          if(similarImages["lower_hem_stitching"]) {
            imagesList.push(similarImages["lower_hem_stitching"]);
            similarImagesList.push({type: "Stitching Hem", uri: similarImages["lower_hem_stitching"], passed: false});
          }
        })

        console.log("results=====>", results)
        
        const localPaths = await Promise.all(imagesList.map((url, index) => {
          const filename = `image_${index}.jpg`; // Use a unique filename          
          return downloadImage(url, filename);
        }));


        const formData = new FormData();
        for (const imagePath of localPaths) {

          const fileNew = {
            uri: imagePath,
            type: 'image/jpeg',
            name: imagePath.split('/').pop(),
          };
          formData.append('images', fileNew);
        }
        try {
          await fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }).then((response) => {
            if (!response.ok) {
              throw new Error('Network response was not ok', appraisalURL);
            }
            return response.json();
          })
          .then((data) => {
            
            console.log('Appraisal Result:', data);  
            if (data.predictions.length > 0) {          
              
              let fakeCount = 0;
              let realCount = 0;
              for (const [index, results] of data.predictions.entries()) {
                
                if(results.prediction == 'real') {
                  similarImagesList[index].passed = true;
                  realCount = realCount + 1;
                } else {
                  fakeCount = fakeCount + 1;
                }
              }

              let overallResult = "Overall Prediction is: Fake";
              if(realCount > fakeCount) {
                overallResult = "Overall Prediction is: Pass";
              }

              /*let appraisalValue = "";
              for (const [index, results] of data.results.entries()) {
                appraisalValue = results.appraisal_value
                break
              }
  
              const newItem = {
                textBefore: "Pass",
                imageUri: "",
                //textAfter: `$${appraisalURL}`,
                textAfter: `$${Math.round(+appraisalValue)}`,
              };
  
              
              const arrayS = [newItem];
              const finalResult = arrayS.join("\n");
                setSuccessMessage(finalResult)
                setSuccessList(arrayS)
                setPopupOnResponse(true);*/
  
                //setAIPopupOnResponse(true)              
                
                
                handlePress('');
                handleAINavigate(similarImagesList, overallResult);
                /*navigate(screens .AIRESULT, {
                  
                  front: {
                    images: [
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: true },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                    ],
                  },
                 
                  tag: {
                    images: [
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: true },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                      { uri: 'https://s3.amazonaws.com/com.images.legiteem8/7620d05b-42ae-4f40-ae29-979021b3ce8a-202409122191%40blurHash%3DSk1LQlJHYXlXQmF5fnFvZg%3D%3D.jpeg', passed: false },
                    ],
                  },
                 
                })*/
              
            }
          })
  
          //const { front, back, tag, copyright, stitichingSleeve, stitichingHem} = route.params;
          .catch((error) => {
            console.error('appraisalURL Error uploading image:', error.message);
            setErrorMessage(error.message)
            setPopup(true);
          });
        
      } catch (error) {
        console.error('appraisalURL Error uploading imagesssssss:', appraisalURL);
        setErrorMessage(error.message)
        setPopup(true);
        }
      }     
    } catch (error) {
      setErrorMessage(error.message)
      setPopup(true);
      console.log("error ", error)
    } finally {
      setLoading(false);
    }
  }, [completeItem]);

  const handlePostAIForPredictImages = React.useCallback(async (localPaths, results) => {
    
    try {
      if (localPaths.length > 0) {
  
        const front = {images: []};
        const frontCloseUp = {images: []};
        const frontTag = {images: []};
        const back = {images: []};
        const backCloseup = {images: []};
        const backTag = {images: []};
        
        const copyright = {images: []};
        const stitichingHem = {images: []};
        const stitichingSleeve = {images: []};
        //const probabilityData = [];
        const predictionData = [];
        const keysData = [];
                        
        results.forEach((item, index) => {
          const similarImages = item["similar images"];
          //const probability = item["probability"];    
          const predictions = item["predictions"];    
          const keys = item["keys"];    
          
          //probabilityData.push(...probability)
          predictionData.push(...predictions)
          keysData.push(...keys)
          if(similarImages.front) {
            similarImages.front.forEach((imageItems, imageIndex) => {
              front.images.push({type: 'Front', uri: imageItems, passed: null});
            })
          } 
          
          if(similarImages.closeup_front) {
            similarImages.closeup_front.forEach((imageItems, imageIndex) => {
              frontCloseUp.images.push({type: 'Front Closeup', uri: imageItems, passed: null});
            })
          }
          
          if(similarImages.front_tag) {

            similarImages.front_tag.forEach((imageItems, imageIndex) => {
              frontTag.images.push({type: 'Front Tag', uri: imageItems, passed: null});
            })
          }
          
          if(similarImages.back) {

            similarImages.back.forEach((imageItems, imageIndex) => {
              back.images.push({type: 'Back', uri: imageItems, passed: null});
            })
          } 

          if(similarImages.closeup_back) {

            similarImages.closeup_back.forEach((imageItems, imageIndex) => {
              backCloseup.images.push({type: 'Back Closeup', uri: imageItems, passed: null});
            })
          } 
          
          if(similarImages.back_tag) {
            similarImages.back_tag.forEach((imageItems, imageIndex) => {
              backTag.images.push({type: 'Back Tag', uri: imageItems, passed: null});
            })
          }

          if(similarImages.copyright) {
            similarImages.copyright.forEach((imageItems, imageIndex) => {
              copyright.images.push({type: 'Copyright', uri: imageItems, passed: null});
            })
          }

          if(similarImages.arm_hem_stitching) {
            similarImages.arm_hem_stitching.forEach((imageItems, imageIndex) => {
              stitichingSleeve.images.push({type: 'Stitching Sleeve', uri: imageItems, passed: null});
            })
          }

          if(similarImages.lower_hem_stitching) {
            similarImages.lower_hem_stitching.forEach((imageItems, imageIndex) => {
              stitichingHem.images.push({type: 'Stitching Hem', uri: imageItems, passed: null});
            })
          }
          
        })


        let predication = false;
        handlePress('');
        navigate(screens.AIRESULT, {front, frontCloseUp, frontTag, 
          back, backTag, backCloseup, 
          copyright, stitichingSleeve, stitichingHem, url, predication, predictionData, keysData});



      //   const formData = new FormData();
      //   for (const imagePath of localPaths) {

      //     console.log("imagePath======>", imagePath)
      //     const fileNew = {
      //       uri: imagePath,
      //       type: 'image/jpeg',
      //       name: imagePath.split('/').pop(),
      //     };
      //     formData.append('images', fileNew);
      //   }

      //   try {
      //     console.log("url........",url);
      //     console.log("localPaths........",localPaths.length);
      //     await fetch(url, {
      //       method: 'POST',
      //       body: formData,
      //       headers: {
      //         'Content-Type': 'multipart/form-data',
      //       },
      //     }).then((response) => {
      //       if (!response.ok) {
      //         throw new Error('Network response was not ok', appraisalURL);
      //       }
      //       return response.json();
      //     })
      //     .then((data) => {
            
      //       console.log('Appraisal Result:', data);  
      //       if (data.predictions.length > 0) {          
              
      //         let fakeCount = 0;
      //         let realCount = 0;
      //         let predication = false;
      //         for (const [index, results] of data.predictions.entries()) {
                
      //           if(results.prediction == 'real') {
      //             //similarImagesList[index].passed = true;
      //             realCount = realCount + 1;
      //             predication = true;
      //           } else {
      //             fakeCount = fakeCount + 1;
      //           }
      //         }

      //         /*let overallResult = "Overall Prediction is: Fake";
      //         if(realCount > fakeCount) {
      //           overallResult = "Overall Prediction is: Pass";
      //         }*/              
              
      //           handlePress('');
      //   navigate(screens.AIRESULT, {front, frontCloseUp, frontTag, 
      //     back, backTag, backCloseup, 
      //     copyright, stitichingSleeve, stitichingHem, url, predication});
      //       }
      //     })
  
      //     //const { front, back, tag, copyright, stitichingSleeve, stitichingHem} = route.params;
      //     .catch((error) => {
      //       console.error('appraisalURL Error uploading image:', error.message);
      //       setErrorMessage(error.message)
      //       setPopup(true);
      //     });
        
      // } catch (error) {
      //   console.error('appraisalURL Error uploading imagesssssss:', appraisalURL);
      //   setErrorMessage(error.message)
      //   setPopup(true);
      //   }
      }     
    } catch (error) {
      setErrorMessage(error.message)
      setPopup(true);
      console.log("error ", error)
    } finally {
      setLoading(false);
    }
  }, [completeItem]);
  

  const handlePostAIAppraisal = React.useCallback(async () => {
    let isAPICalled = 0
    setLoading(true);
    try {
      if (imageUrls.length > 0) {
        const localPaths = await Promise.all(imageUrls.map((url, index) => {
          const filename = `image_${index}.jpg`; // Use a unique filename
          return downloadImage(url.uri, filename);
        }));

        // Upload the locally saved images
        const formData = new FormData();
        for (const imagePath of localPaths) {
        
          const fileNew = {
            uri: imagePath,
            type: 'image/jpeg',
            name: imagePath.split('/').pop(),
          };
          formData.append('images', fileNew);
        }
        try {
          await fetch(appraisalURL, {
            method: 'POST',
            body: formData,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }).then((response) => {
            if (!response.ok) {
              throw new Error('Network response was not ok', url);
            }
            return response.json();
          })
          .then((data) => {
            console.log('Predication Result:', data.results);
            if (data.results.length > 0) {          
              
              isAPICalled = 1
              handlePostAIForPredictImages(localPaths, data.results)
            }
          })
          .catch((error) => {
            console.error('Error uploading image:', error.message);
            setErrorMessage(error.message)
            setPopup(true);
          });
        
      } catch (error) {
        console.error('Error uploading image:', error.message);
        setErrorMessage(error.message)
        setPopup(true);
        }
      }     
    } catch (error) {
      setErrorMessage(error.message)
      setPopup(true);
      console.log("error ", error)
    } finally {
      if(isAPICalled == 0) {
        setLoading(false);
      }      
    }
  }, [completeItem]);

  const handlePress = (message) => {
    setPopupOnResponse(false)
    refetch?.();
    resetSheet();
  };

  return (
    <View style={{ marginBottom: 15 }}>
      <ListActionRow
        iconName="ai_lens"
        iconContainerStyle={{ backgroundColor: colors.white }}    
        actionText={title}
        isLoading={loading}
        disabled={loading}
        onPress={handlePostAIAppraisal}
        iconSize={30}
        
      />
      <InfoModal
        setVisible={setPopup}
        popUp={{
          isError: true,
          state: popup,
          data: {
            title: locale.Error,
            //description: locale.SomethingWentWrongPleaseTryAgain,
            description: errorMessage,
          },
        }}
      />

      <AIInfoModal
        setVisible={handlePress}
        popUp={{
          isError: false,
          state: popupOnResponse,
          data: {
            title: locale.success,
            //description: locale.SomethingWentWrongPleaseTryAgain,
            description: successMessage,
            list: successList,
          },
        }}
      />

      {/* <AIResultDialog
        visible={aiPopupOnResponse}
        onClose={() => setAIPopupOnResponse(false)}
        completeItem={completeItem} />
         */}
      
    </View>
  );
}





const CLAIM_NFT_POPUP_INITIAL_STATE = {
  state: false,
  errorText: '',
  isError: true,
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  topBarButtonsWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
    marginTop: spacings.md,
  },
  topBarButtonsText: {
    color: colors.black,
    fontSize: fontSize.md,
    textTransform: 'capitalize',
  },
  topBarButtons: {
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 150,
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
    backgroundColor: colors.lightGrey,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 2.3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '100%',
  },
  modalTitle: {
    fontSize: fontSize.xxl,
    color: colors.black,
    fontWeight: 'bold',
    marginBottom: spacings.md,
  },
  modalLabel: {
    fontSize: fontSize.md,
    color: colors.black,
  },
  optionWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacings.sm,
    paddingVertical: spacings.md,
  },
  listRow: {
    fontSize: 18,
    fontFamily: 'Inter-Medium',
    color: colors.black,
  },
});

// Helper function to get a Blob from a remote image URL
async function downloadImage(imageUrl, filename) {
  const isIOS = Platform.OS == 'ios';
  const downloadDest = isIOS ? `${RNFS.DocumentDirectoryPath}/${filename}` 
  : `file://${RNFS.DocumentDirectoryPath}/${filename}`;

  try {
    const response = await fetch(imageUrl);
    
    // const fileStream = RNFS.createWriteStream(downloadDest);
    // response.data.pipe(fileStream);

    // return new Promise((resolve, reject) => {
    //   fileStream.on('finish', () => resolve(downloadDest));
    //   fileStream.on('error', (err) => reject(err));
    // });

    const blob = await response.blob();
    
    
    const base64 = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result.split(',')[1]); // Remove the data URL prefix
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });

    await RNFS.writeFile(downloadDest, base64, 'base64');
    return downloadDest;
  } catch (error) {
    console.error('Error downloading image:', error);
    throw error;
  }
}

export default Index;

class Prediction {
  constructor(confidence, prediction) {
    this.confidence = confidence;
    this.prediction = prediction;
  }
}
