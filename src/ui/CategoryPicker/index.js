import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import {useQuery} from '@apollo/client';

import {GET_CATEGORIES} from '../../apollo/queries';
import BottomSheet from '../../components/BottomSheet';
import Button from '../../components/Button';
import locale from '../../assets/locale.json';
import styles from './styles';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';

function CategoryPicker({
  children = null,
  onClear = () => null,
  onApply = () => null,
  selectedItems = [],
}) {
  const [sheetVisible, setSheetVisible] = React.useState(false);

  const handlePress = () => {
    setSheetVisible(true);
  };

  const closeSheet = () => [setSheetVisible(false)];

  return (
    <>
      {/* Render the caller UI yourself, the picker will be handled automatically */}
      {children({openPicker: handlePress})}
      <BottomSheet
        visible={sheetVisible}
        setVisible={setSheetVisible}
        snapPoints={['40%', '80%']}
        index={1}>
        <SheetContent
          onClear={onClear}
          onApply={onApply}
          closeSheet={closeSheet}
          selectedItems={selectedItems}
        />
      </BottomSheet>
    </>
  );
}

function SheetContent({
  onClear = () => null,
  onApply = () => null,
  closeSheet = () => null,
  selectedItems = [],
}) {
  const [selected, setSelected] = React.useState(() => selectedItems ?? []);

  const {data, refetch} = useQuery(GET_CATEGORIES);

  const list = React.useMemo(() => data?.getCategories?.data, [
    data?.getCategories?.data,
  ]);

  const handleRowPressed = item => {
    setSelected(previousState =>
      selected?.length == 0
        ? [item]
        : previousState?.find(i => i?.id == item?.id)
        ? previousState?.filter(i => i?.id !== item?.id)
        : [...previousState, item],
    );
  };

  const handleClear = () => {
    setSelected([]);
    onClear();
    closeSheet();
  };

  const handleApply = () => {
    onApply(selected);
    closeSheet();
  };

  const renderItem = React.useCallback(
    ({item}) => (
      <ListRow
        item={item}
        onPress={handleRowPressed}
        isSelected={selected?.find(i => item?.id == i?.id)}
      />
    ),
    [selected, handleRowPressed],
  );

  React.useEffect(() => {
    if (selected?.length == 0) {
      refetch?.();
    }
  }, []); // on mount

  return (
    <>
      {selected?.length > 0 ? (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            marginBottom: 10,
          }}>
          <Button
            text={locale.Clear}
            style={{
              ...styles.filterUtilityButton,
              backgroundColor: colors.redBadge,
            }}
            scaleAnimationThreshold={0.96}
            onPress={handleClear}
            textStyle={{fontSize: 14}}
          />
          <Button
            text={locale.Apply}
            style={{
              ...styles.filterUtilityButton,
              backgroundColor: colors.primary,
            }}
            scaleAnimationThreshold={0.96}
            onPress={handleApply}
            textStyle={{fontSize: 14}}
          />
        </View>
      ) : null}
      <BottomSheetFlatList
        keyExtractor={item => item?.id?.toString?.()}
        data={list}
        renderItem={renderItem}
        contentContainerStyle={{paddingBottom: 50}}
        initialNumToRender={20}
      />
    </>
  );
}

function ListRow({item = null, onPress = () => null, isSelected = false}) {
  return (
    <TouchableOpacity style={styles.listRow} onPress={onPress.bind(null, item)}>
      <Text style={styles.listRowText}>{item?.name}</Text>
      <Icon
        style={{
          ...styles.radioButton,
          backgroundColor: isSelected ? colors.primary : colors.grey,
        }}
        name={isSelected ? 'checked_radio' : 'unchecked_radio'}
        tint={isSelected ? colors.primary : colors.grey}
        size={20}
      />
    </TouchableOpacity>
  );
}

export default CategoryPicker;
