import React from 'react';
import dayjs from 'dayjs';
import { Platform } from 'react-native';

const defaultOptions = {
  containsSymbol: {
    allowedSymbols: [],
  },
  fixedNumberTo: {
    onlyFixIfItHasDecimal: false,
    numberOfDecimalPlaces: 2,
  },
};

export const permissionStorage = Platform.OS === 'android' ? 
  (Platform.Version >= 33 ? 'storage_new' : 'storage') : 'storage';
  
//export const permissionStorage = 'storage';


export const validateEmail = email => {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  const reStartAndEnd = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}/g;

  if (re.test(email) && reStartAndEnd.test(email)) {
    return true;
  }
  return false;
};

export const dateFromNow = date =>
  dayjs(date).isValid() ? dayjs(date).fromNow() : '';

export const containsSymbol = function (
  string = '',
  options = defaultOptions.containsSymbol,
) {
  options = {...defaultOptions.containsSymbol, ...options};

  let target = `!@#$%^&*()_+\\-=\\[\\]{};':"\\|,.<>\\/?`;

  if (options.allowedSymbols.length) {
    options.allowedSymbols.forEach(symbol => {
      if (target.includes(symbol)) {
        target = target?.replace(symbol, '');
      }
    });
  }

  return new RegExp(`[${target}]+`, 'g').test(string);
};

export const validateUsername = function (username) {
  if (!username) {
    throw new Error('Username is required.');
  }
  const isUsernameInvalid = containsSymbol(username, {
    allowedSymbols: ['_'],
  });

  if (isUsernameInvalid) {
    return {
      isValid: false,
      message:
        "Invalid username. Username should not contain any symbol except '_'",
    };
  }

  if (!/[a-zA-Z]/.test(username)) {
    return {
      isValid: false,
      message:
        'Invalid username. Username should contain at least one alphabet.',
    };
  }

  if (/\s/.test(username)) {
    return {
      isValid: false,
      message: 'Invalid username. Username should not contain whitespace.',
    };
  }
  if (username?.length < 3) {
    return {
      isValid: false,
      message: 'Username should be at least 3 characters long.',
    };
  }
  return {
    isValid: true,
  };
};

export const stripHTTPSFromURL = url => {
  if (typeof url !== 'string' || url == null || url?.length == 0) {
    return '';
  }
  const hasHTTP = url?.startsWith?.('http://');
  const hasHTTPS = url?.startsWith?.('https://');

  return hasHTTP || hasHTTPS ? url?.slice?.(hasHTTP ? 7 : 8, url?.length) : url;
};

export const formatURL = url => {
  if (typeof url !== 'string' || url == null || url?.length == 0) {
    return '';
  }
  const hasHTTP = url?.startsWith?.('http://');
  const hasHTTPS = url?.startsWith?.('https://');

  if (hasHTTPS || hasHTTP) {
    return url;
  }

  return `https://${url}`;
};

export function normalizeNumberToNDecimalPlaces(value, decimalPoints = 2) {
  return value.indexOf('.') > 0
    ? value.split('.').length >= decimalPoints
      ? value.split('.')[0] +
        '.' +
        value.split('.')[1].substring(-1, decimalPoints)
      : value
    : value;
}

export function fixedNumberTo(
  numberOrString,
  options = defaultOptions.fixedNumberTo,
) {
  options = {...defaultOptions.fixedNumberTo, ...options};
  return numberOrString?.toString?.()?.includes('.')
    ? (+numberOrString)?.toFixed?.(2)
    : numberOrString;
}

export function replaceAll(str, match, replacement) {
  return str.split(match).join(replacement);
}

export function formatDigits(num, digits = 1) {
  const lookup = [
    {value: 1, symbol: ''},
    {value: 1e3, symbol: 'k'},
    {value: 1e6, symbol: 'M'},
    {value: 1e9, symbol: 'G'},
    {value: 1e12, symbol: 'T'},
    {value: 1e15, symbol: 'P'},
    {value: 1e18, symbol: 'E'},
  ];
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  var item = lookup
    .slice()
    .reverse()
    .find(function (item) {
      return num >= item.value;
    });
  return item
    ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol
    : '0';
}
