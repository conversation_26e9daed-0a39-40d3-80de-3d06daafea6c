import {StyleSheet, Dimensions} from 'react-native';

import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';

const {width} = Dimensions.get('window');
const WIDTH = width - spacings.lg;
const CARD_WIDTH = WIDTH / 4;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  visibleSection: {},
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacings.xl,
    paddingVertical: 15,
    borderRadius: 4,
  },
  buttonText: {
    fontSize: fontSize.lg,
    fontFamily: 'Inter-Medium',
  },
  subHeading: {
    fontSize: fontSize.lg,
    color: colors.black,
    marginVertical: spacings.lg,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  heading: {
    fontSize: 25,
    color: colors.black,
    textAlign: 'center',
    marginTop: spacings.md,
    fontFamily: 'Gugi-Regular',
  },
  howItWorksButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.grey,
  },
});

const recentItemsStyles = StyleSheet.create({
  container: {
    width: '100%',
    paddingHorizontal: 2,
  },
  labelBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacings.md,
    paddingHorizontal: spacings.lg,
    alignItems: 'center',
  },
  label: {
    fontSize: fontSize.md,
    color: colors.black,
    fontFamily: 'Gugi-Regular',
  },
  cardsGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  brandWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 2,
    shadowColor: colors.black,
    height: CARD_WIDTH / 1.5,
    width: CARD_WIDTH,
    borderRadius: 10,
  },
  brandLogo: {
    borderRadius: 10,
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
    backgroundColor: colors.black,
  },
  categoryWrapper: {
    backgroundColor: colors.lightGrey,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    shadowColor: colors.black,
    height: CARD_WIDTH / 1.5,
    width: CARD_WIDTH,
  },
  category: {
    fontSize: 13,
    color: colors.black,
    textAlign: 'center',
    fontFamily: 'Inter-Medium',
  },
  contentContainer: {
    paddingStart: spacings.lg,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});

export {styles, recentItemsStyles};
