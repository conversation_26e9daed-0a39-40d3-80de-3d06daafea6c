import {useState, useEffect, useRef} from 'react';
import {Platform} from 'react-native';
import {InteractionManager} from 'react-native';

const defaultOptions = {
  allowedPlatforms: ['android', 'ios'],
};

const useInteractionManager = (options = defaultOptions) => {
  options = {...defaultOptions, ...options};

  const isThisPlatformAllowed = options.allowedPlatforms.includes(Platform.OS);

  const [interactionsComplete, setInteractionsComplete] = useState(false);

  const subscriptionRef = useRef(null);

  const transitionRef = useRef(null);

  useEffect(() => {
    if (isThisPlatformAllowed) {
      subscriptionRef.current = InteractionManager.runAfterInteractions(() => {
        transitionRef.current?.animateNextTransition();
        setInteractionsComplete(true);
        subscriptionRef.current = null;
      });
    }

    return () => {
      subscriptionRef.current?.cancel?.();
    };
  }, [isThisPlatformAllowed]);

  return {
    isInteractionsComplete: isThisPlatformAllowed ? interactionsComplete : true,
    transitionRef,
  };
};

export default useInteractionManager;
