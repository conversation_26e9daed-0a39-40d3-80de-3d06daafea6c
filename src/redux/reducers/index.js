import {combineReducers} from 'redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {persistReducer} from 'redux-persist';
import autoMergeLevel2 from 'redux-persist/lib/stateReconciler/autoMergeLevel2';

import uploadReducer from './uploadsReducer';
import aiuploadsReducer from './aiuploadsReducer';
import authReducer from './authReducer';
import authenticationReducer from './authenticationReducer';

const rootReducer = combineReducers({
  auth: persistReducer(
    {key: 'authState', storage: AsyncStorage, stateReconciler: autoMergeLevel2},
    authReducer,
  ),
  uploadMedia: persistReducer(
    {
      key: 'mediaState',
      storage: AsyncStorage,
      stateReconciler: autoMergeLevel2,
    },
    uploadReducer,
  ),
  uploadAIMedia: persistReducer(
    {
      key: 'aiMediaState',
      storage: AsyncStorage,
      stateReconciler: autoMergeLevel2,
    },
    aiuploadsReducer,
  ),
  authentication: persistReducer(
    {
      key: 'authentication',
      storage: AsyncStorage,
      stateReconciler: autoMergeLevel2,
    },
    authenticationReducer,
  ),
});

export default rootReducer;
