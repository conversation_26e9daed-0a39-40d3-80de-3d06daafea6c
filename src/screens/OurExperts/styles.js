import {StyleSheet, Dimensions} from 'react-native';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';

const {width: WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  headerContainer: {
    paddingVertical: spacings.md,
    paddingHorizontal: spacings.lg,
    backgroundColor: colors.lightGrey,
    marginBottom: spacings.md,
  },
  imageWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacings.md,
  },
  profilePhoto: {
    height: 72,
    width: 72,
    borderRadius: 72,
    borderWidth: 2,
    borderColor: colors.grey,
  },
  levelsWrapper: {
    flexDirection: 'row',
    marginTop: spacings.sm,
    alignItems: 'center',
  },
  popularity: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: spacings.sm,
    marginBottom: spacings.md,
  },
  popularityWrapper: {
    alignItems: 'center',
  },
  popularityStat: {
    fontWeight: 'bold',
    color: colors.black,
    fontSize: 18,
  },
  socialButtonsWrapper: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacings.md,
  },
  socialButtons: {
    backgroundColor: colors.black,
    borderRadius: 25,
    width: WIDTH / 2.5,
    paddingVertical: spacings.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  socialButtonsText: {
    color: colors.white,
    textAlign: 'center',
    fontSize: fontSize.md,
    marginLeft: spacings.sm,
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  username: {
    fontWeight: 'bold',
    color: colors.black,
    fontSize: 18,
  },
  textSection: {
    marginLeft: spacings.md,
  },
  levelsButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 5,
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 5,
  },
  levelsText: {
    color: colors.black,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Medium',
    fontSize: 12,
  },
  descWrapper: {
    //   marginTop: spacings.md,
  },
  desc: {
    color: colors.darkFont,
    fontSize: fontSize.md,
    fontFamily: 'Inter-Medium',
  },
  link: {
    color: colors.primary,
    fontFamily: 'Inter-Regular',
    fontSize: fontSize.md,
  },
  noviceBadge: {
    width: 22,
    height: 22,
    resizeMode: 'contain',
    backgroundColor: colors.almostWhite,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.lg,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});
