import React, {useCallback} from 'react';
import {Text, View, TouchableOpacity, Linking} from 'react-native';

import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {styles} from './styles';
import {screens} from '../../assets/strings';
import {spacings} from '../../assets/spacing';
import {useNavigation} from '@react-navigation/native';
import Tooltip from '../../components/Tooltip';
import locale from '../../assets/locale.json';
import ProfileImage from '../../components/ProfileImage';
import {fixedNumberTo} from '../../tools/utils';
import {useUser} from '../../tools/hooks';
import TouchAwareButton from '../../components/Button/TouchAwareButton';

const StatsCard = ({data}) => {
  const navigation = useNavigation();

  const post = data?.getPost.data;

  const {user} = useUser();

  const isAuthenticationByMe = React.useMemo(() => post?.user?.id == user?.id, [
    user?.id,
    post?.user?.id,
  ]);

  const authenticationDecision =
    data?.getPost.data?.authentication_result?.expert_result?.decision;

  const handleColor = useCallback(() => {
    switch (authenticationDecision) {
      case 'REAL':
        return colors.greenBadge;
      case 'FAKE':
        return colors.warning;
      case 'UNDECIDED':
        return colors.grey;
      case 'UNDETERMINED':
        return colors.amber;
      default:
        return colors.grey;
    }
  }, [authenticationDecision]);

  const handleDecision = () => {
    switch (
      data?.getPost?.data?.authentication_result?.expert_result?.decision
    ) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
        return 'UNDETERMINED';
      default:
        return 'PENDING';
    }
  };

  const canGetNFT = React.useMemo(
    () =>
      isAuthenticationByMe &&
      [2, 5].includes(+post?.authentication_type?.id) &&
      post?.authentication_result?.expert_result?.decision === 'REAL',
    [
      isAuthenticationByMe,
      post?.authentication_type?.id,
      post?.authentication_result?.expert_result?.decision,
    ],
  );

  const handleGetNFT = React.useCallback(() => {
    navigation.navigate(screens.AUTHENTICATE, {
      isGetNFT: true,
      getNFTDetails: {
        authenticationId: post?.queue_id,
        authenticationType: post?.authentication_type?.id,
      },
    });
  }, [navigation, post?.queue_id, post?.authentication_type?.id]);

  const isNFTCertified = React.useMemo(
    () => post?.authentication_type?.id == 3 && !(post?.nft_detail == null),
    [post?.authentication_type?.id, post?.nft_detail],
  );

  return authenticationDecision === 'REAL' ? (
    <>
      <View
        style={[
          styles.statsContainer,
          {
            borderWidth: 2,
            borderColor: colors.greenBadge,
            borderRadius: 20,
            margin: 20,
          },
        ]}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text style={[styles.resultsHeading, {textAlign: 'center'}]}>
            {locale.AnalysisResults}{' '}
          </Text>
          <Icon
            name="expertAuthenticationEmblem"
            size={30}
            tint={colors.greenBadge}
            containerStyle={{marginLeft: 5}}
          />
          {isNFTCertified ? (
            <Icon
              name="nftEmblem"
              size={25}
              tint={colors.yellow}
              containerStyle={{marginLeft: 10}}
            />
          ) : null}
        </View>
        <View
          style={[
            styles.appraisalWrapper,
            {justifyContent: 'space-between', alignItems: 'center'},
          ]}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text
              style={[
                styles.appraisalLabel,
                {
                  color: colors.primary,
                  alignItems: 'center',
                  fontFamily: 'Inter-Bold',
                },
              ]}>
              {locale.ExpertsVote}:{' '}
            </Text>
            <Tooltip
              enableSheetContent
              title={locale.ExpertsVote}
              description="An expert has evaluated this t-shirt with the intention of issuing an app-based, or blockchain-based certificate of authenticity."
            />
          </View>

          <Text
            style={{
              textTransform: 'capitalize',
              color: handleColor(),
              fontSize: fontSize.xxl,
              fontFamily: 'Inter-Bold',
            }}>
            {handleDecision()}
          </Text>
        </View>
        {data?.getPost?.data?.authentication_result?.expert_result
                    ?.appraisal_value ? 

                    //!isAuthenticationByMe && data?.getPost?.data?.is_private ? null :
                    data?.getPost?.data?.is_private ? null :
                    <View
          style={[
            styles.appraisalWrapper,
            {
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingTop: 15,
            },
          ]}>
          <Text
            style={[
              styles.appraisalLabel,
              {
                alignItems: 'center',
              },
            ]}>
            {locale.Appraisal}:{' '}
          </Text>
          <Text
            style={{
              textTransform: 'capitalize',
              color: handleColor(),
              fontSize: fontSize.xxl,
              fontFamily: 'Inter-Bold',
            }}>
              {
                 '$' + fixedNumberTo(
                  data?.getPost?.data?.authentication_result?.expert_result
                    ?.appraisal_value,
                )
              }            
          </Text>
        </View>
                     : null}
        
        {isNFTCertified ? (
          <View
            style={[
              styles.appraisalWrapper,
              {
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: 15,
              },
            ]}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text
                style={[
                  styles.appraisalLabel,
                  {
                    alignItems: 'center',
                    textTransform: 'none',
                  },
                ]}>
                {locale.NFTCertified}{' '}
              </Text>
              <Tooltip
                enableSheetContent
                title={locale.NFTCertified}
                description="An expert has issued a blockchain-based certificate of authenticity to establish digital provenance."
                style={{marginLeft: 5}}
              />
            </View>
          </View>
        ) : null}
        <View style={styles.userContainerTopSection}>
          {data?.getPost?.data?.authentication_result?.decision ===
          'UNDECIDED' ? null : (
            <View
              style={[
                styles.userId,
                {
                  marginTop: 10,
                  flex: 1,
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                },
              ]}>
              <Text style={[styles.appraisalLabel, {marginRight: spacings.sm}]}>
                {locale.AuthenticationBy}:
              </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate(screens.EXPERTS_PROFILE, {
                    expertUserId:
                      data?.getPost?.data?.authentication_result?.expert_result
                        ?.expert?.user?.id,
                  });
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <ProfileImage
                  url={
                    data?.getPost?.data?.authentication_result?.expert_result
                      ?.expert?.user?.image
                  }
                  size={30}
                />
                <Text
                  style={[
                    styles.sm,
                    {
                      marginLeft: spacings.sm,
                      color: colors.black,
                      fontFamily: 'Inter-Medium',
                      fontSize: 16,
                      maxWidth: '90%',
                    },
                  ]}
                  numberOfLines={1}>
                  {
                    data?.getPost?.data?.authentication_result?.expert_result
                      ?.expert?.user?.username
                  }
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
        {canGetNFT ? (
          <TouchAwareButton
            onPress={handleGetNFT}
            style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
            <Icon
              name="nftCertification"
              size={20}
              tint={colors.yellow}
              containerStyle={{marginRight: 10}}
            />
            <Text
              style={[
                styles.appraisalLabel,
                {
                  textTransform: 'none',
                  color: colors.primary,
                  fontFamily: 'Inter-Bold',
                },
              ]}>
              {locale.GetNFT}
            </Text>
          </TouchAwareButton>
        ) : null}
      </View>
      {isNFTCertified && post?.nft_detail?.etherscan_url?.length > 0 ? (
        <View
          style={{
            marginHorizontal: 20,
            borderTopWidth: 2,
            borderTopColor: colors.lightGrey,
            borderBottomWidth: 2,
            borderBottomColor: colors.lightGrey,
            paddingVertical: 20,
          }}>
          <Text style={[styles.resultsHeading]}>{locale.NFTCredentials}</Text>
          <Text
            onPress={() => Linking.openURL(post?.nft_detail?.etherscan_url)}
            style={[
              styles.appraisalLabel,
              {
                color: colors.primary,
                alignItems: 'center',
                fontFamily: 'Inter-Bold',
                marginTop: 10,
              },
            ]}>
            {locale.PolygonScanLink}
          </Text>
        </View>
      ) : null}
    </>
  ) : (
    <View style={{padding: 20}}>
      <Text style={[styles.resultsHeading]}>Results</Text>
      <View
        style={[
          styles.appraisalWrapper,
          {justifyContent: 'space-between', alignItems: 'center'},
        ]}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {!(authenticationDecision == null) &&
          authenticationDecision !== 'UNDECIDED' ? (
            <Icon
              name={
                authenticationDecision == 'REAL'
                  ? 'expertCertified'
                  : 'cancel_x'
              }
              size={authenticationDecision == 'REAL' ? 20 : 10}
              tint={
                authenticationDecision == 'REAL'
                  ? colors.secondary
                  : colors.white
              }
              containerStyle={{
                marginRight: 10,
                ...(!(authenticationDecision == 'REAL')
                  ? {
                      backgroundColor: colors.warning,
                      padding: 5,
                      borderRadius: 10,
                    }
                  : {}),
              }}
            />
          ) : null}
          <Text
            style={[
              styles.appraisalLabel,
              {
                color: colors.primary,
                alignItems: 'center',
                fontFamily: 'Inter-Bold',
              },
            ]}>
            {locale.ExpertsVote}:{' '}
          </Text>
          <Tooltip
            enableSheetContent
            title={locale.ExpertsVote}
            description="An expert has evaluated this t-shirt with the intention of issuing an app-based, or blockchain-based certificate of authenticity."
          />
        </View>

        <Text
          style={{
            textTransform: 'capitalize',
            color: handleColor(),
            fontSize: fontSize.lg,
            fontFamily: 'Inter-Bold',
          }}>
          {handleDecision()}
        </Text>
      </View>

      { <View style={styles.userContainerTopSection}>

        
          {!(authenticationDecision == null) &&
          authenticationDecision !== 'UNDECIDED' ? (
            <View
              style={[
                styles.userId,
                {
                  marginTop: 10,
                  flex: 1,
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                },
              ]}>
              <Text style={[styles.appraisalLabel, {marginRight: spacings.sm}]}>
                {locale.AuthenticationBy}:
              </Text>
              <TouchableOpacity
                onPress={() => {
                  navigation.navigate(screens.EXPERTS_PROFILE, {
                    expertUserId:
                      data?.getPost?.data?.authentication_result?.expert_result
                        ?.expert?.user?.id,
                  });
                }}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 10,
                }}>
                <ProfileImage
                  url={
                    data?.getPost?.data?.authentication_result?.expert_result
                      ?.expert?.user?.image
                  }
                  size={30}
                />
                <Text
                  style={[
                    styles.sm,
                    {
                      marginLeft: spacings.sm,
                      color: colors.black,
                      fontFamily: 'Inter-Medium',
                      fontSize: 16,
                      maxWidth: '90%',
                    },
                  ]}
                  numberOfLines={1}>
                  {
                    data?.getPost?.data?.authentication_result?.expert_result
                      ?.expert?.user?.username
                  }
                </Text>
              </TouchableOpacity>
            </View>
          ) : null }
        </View> }
    </View>
  );
};

export default StatsCard;
