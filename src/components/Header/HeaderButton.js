import React from 'react';
import {Text, TouchableOpacity, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import colors from '../../assets/colors';
import Icon from '../Icon';
import styles from './styles.js';

const isIOS = Platform.OS === 'ios';

export default function HeaderButton({action, style = {}, iconStyle = {}}) {
  const navigation = useNavigation();
  let buttonConfig = {
    back: {
      icon: {
        name: isIOS ? 'chevron' : 'backNavigation',
        tint: colors.white,
        size: 18,
      },
      action: () => {
        if (navigation.canGoBack()) {
          navigation.goBack();
        }
      },
      suffix: isIOS ? 'Back' : '',
    },
  };

  let config;
  if (typeof action === 'string') {
    config = buttonConfig[action];
  } else {
    config = action;
  }
  return (
    <TouchableOpacity
      activeOpacity={1}
      style={{
        ...styles.headerButton,
        ...style,
      }}
      onPress={config?.action || (() => null)}>
      <Text
        style={{
          color: config?.icon?.tint || 'black',
          fontSize: 16,
          ...config?.prefixStyle,
        }}
        allowFontScaling={false}>
        {config?.prefix}
      </Text>
      {!!config && !!config?.icon ? (
        <Icon
          {...config?.icon}
          style={
            config?.icon?.name === 'chevron'
              ? {...iconStyle, transform: [{rotate: '180deg'}]}
              : iconStyle
          }
        />
      ) : null}
      {!!config?.render ? config.render() : null}
      <Text
        style={{
          color: config?.icon?.tint || 'black',
          fontSize: 18,
          ...config?.suffixStyle,
        }}
        allowFontScaling={false}>
        {config?.suffix}
      </Text>
    </TouchableOpacity>
  );
}
