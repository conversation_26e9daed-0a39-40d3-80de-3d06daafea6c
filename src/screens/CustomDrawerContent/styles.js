import {StyleSheet} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

export const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  emptyContainer: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCard: {
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
    paddingTop: spacings.lg,
    paddingBottom: spacings.xxxl,
    borderRadius: 3,
  },
  closeButtonStyle: {
    marginBottom: 20,
  },
  popupButtonStyle: {
    backgroundColor: colors.primary,
    height: spacings.xxxl,
    marginTop: spacings.lg,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacings.sm,
  },
  popupTextStyle: {
    color: colors.white,
    fontSize: fontSize.md,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  personDetailsText: {
    fontSize: fontSize.sm,
    color: colors.white,
    fontWeight: 'bold',
  },
  personDetailsWrapper: {
    marginLeft: spacings.lg,
    justifyContent: 'space-evenly',
    height: 50,
  },
  imageStyles: {
    height: 55,
    width: 55,
    borderRadius: 55,
    borderWidth: 1,
    borderColor: colors.white,
  },
  cardText: {
    marginBottom: spacings.xl,
    color: colors.black,
    fontSize: fontSize.md,
    textAlign: 'center',
  },
  imageWrapper: {
    paddingHorizontal: 5,
    borderBottomWidth: 3,
    borderBottomColor: colors.grey,
    paddingBottom: 10,
    borderRadius: 0,
  },
  iconWrapper: {
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawerCloseButton: {
    position: 'absolute',
    right: 0,
    padding: spacings.lg,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});
