import React, {useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useIsFocused, useFocusEffect} from '@react-navigation/native';
import IAP from 'react-native-iap';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {screens} from '../../assets/strings';
import locale from '../../assets/locale.json';
import Lottie from '../../components/Lottie';
import {useWindowDimensions} from '../../tools/hooks';
import {useAuthenticationInfo} from '../../tools/hooks';
import Button from '../../components/Button';

const PaymentMethod = ({route, navigation}) => {
  const [authId] = useState(route?.params?.authenticationId);
  const [authenticationType] = useState(route?.params?.authenticationType);
  const [getNFT] = React.useState(route?.params?.getNFTDetails ?? null);

  const [error, setError] = React.useState('');
  const [verified, setVerified] = React.useState(false);

  const navigationRef = React.useRef(false);

  const isFocused = useIsFocused();
  const {data} = useAuthenticationInfo({isFocused});

  const {
    window: {width},
  } = useWindowDimensions();

  const authenticationDetail = React.useMemo(
    () =>
      getNFT == null
        ? data?.type?.find?.(item => item?.id == authenticationType)
        : {
            ...getNFT,
            iap_price_ios:
              data?.upgradeToExpertCertifiedNFTFromExpertCertified_IAP_Pricing,
            id: getNFT?.authenticationId,
            type: getNFT?.authenticationType,
          },
    [
      data?.type,
      data?.upgradeToExpertCertifiedNFTFromExpertCertified_IAP_Pricing,
      authenticationType,
      getNFT,
    ],
  );

  const iapKeyForAuthentication = React.useMemo(
    () =>
      getNFT == null
        ? authenticationDetail?.iap_key_ios
        : data?.upgradeToExpertCertifiedNFTFromExpertCertified_IAP_Key,
    [
      authenticationDetail?.iap_key_ios,
      data?.upgradeToExpertCertifiedNFTFromExpertCertified_IAP_Key,
    ],
  );

  const handleNavigationToCheckout = React.useCallback(() => {
    setError('');
    navigation.navigate(screens.CHECKOUT, {
      authId,
      authenticationType,
      getNFTDetails: getNFT,
      iapKeyForAuthentication,
      authenticationDetail,
    });
  }, [
    iapKeyForAuthentication,
    authId,
    authenticationType,
    getNFT,
    authenticationDetail,
    navigation.navigate,
  ]);

  const verifyConnection = React.useCallback(async () => {
    try {
      await IAP.initConnection();
      IAP.clearProductsIOS();
      const availablePurchases = await IAP.getProducts([
        iapKeyForAuthentication,
      ]);
      const product = availablePurchases?.find(
        purchase => purchase?.productId == iapKeyForAuthentication,
      );
      if (!product) {
        setError(locale.ProductNotFound);
      } else {
        console.log("Available Product...", product);
        setVerified(true);
        if (!navigationRef.current) {
          navigationRef.current = true;
          handleNavigationToCheckout();
        }
      }
    } catch (_error) {
      setError(locale.ErrorContactingAppleServer);
    }
  }, [iapKeyForAuthentication, handleNavigationToCheckout]);

  useFocusEffect(
    React.useCallback(() => {
      if (!(iapKeyForAuthentication == null)) {
        verifyConnection();
      }
    }, [verifyConnection, iapKeyForAuthentication]),
  );

  return (
    <View style={styles.container}>
      <Lottie
        name={
          verified ? 'success' : error?.length > 0 ? 'error' : 'gradientLoader'
        }
        loop={!verified && error?.length == 0}
        style={{width: 0.2 * width, height: 0.2 * width}}
        {...(verified ? {colorFilterPresetName: 'primary'} : {})}
      />
      <Text style={styles.title}>
        {verified
          ? locale.Verified
          : error?.length > 0
          ? error
          : locale.PleaseWait}
      </Text>
      {verified ? (
        <Button
          text={locale.ProceedToCheckout}
          onPress={handleNavigationToCheckout}
          style={{paddingHorizontal: 20}}
        />
      ) : error?.length > 0 ? (
        <Button text={locale.Retry} onPress={verifyConnection} />
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontFamily: 'Gugi-Regular',
    marginVertical: 20,
    textAlign: 'center',
    maxWidth: '60%',
    lineHeight: 25,
  },
});
export default PaymentMethod;
