import React, {useState} from 'react';
import {View, Text, Dimensions, TouchableOpacity} from 'react-native';
import {
  DrawerContentScrollView,
  DrawerItemList,
  DrawerItem,
} from '@react-navigation/drawer';
import {DrawerActions, useNavigation} from '@react-navigation/native';

import Icon from '../../components/Icon';
import CustomDrawerContentLoader from '../../components/Loaders/CustomDrawerContentLoader';
import useLogout from '../../tools/hooks/useLogout';
import {
  icons,
  navLabels,
  images,
  logoutScreen,
  screens,
} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {styles} from './styles';
import {spacings} from '../../assets/spacing';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';
import useUser, {useCredential} from '../../tools/hooks/useUser';
import FastImage from '../../components/FastImage';
import {showToast} from '../../components/Toast';
import DeleteInfoModal from '../../components/Modal/DeleteInfoModal';
import useDeleteAccount from '../../tools/hooks/useDeleteAccount';

const {width: WIDTH} = Dimensions.get('screen');

const Index = props => {
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  const {user, isLoading: loading, error} = useUser();
  const credential = useCredential();

  const {logout, isLogoutLoading} = useLogout({
    onError: () => {
      showToast({
        message: locale.SomethingWentWrongPleaseTryAgain,
        position: 'bottom',
        type: 'error',
      });
    },
  });

  const {deleteAccount, isDeleteLoading} = useDeleteAccount({
    onError: () => {
      showToast({
        message: locale.SomethingWentWrongPleaseTryAgain,
        position: 'bottom',
        type: 'error',
      });
    },
  })
  
  /*const {logout, isDeleteLoading} = useDeleteAccount({
    onError: () => {
      showToast({
        message: locale.SomethingWentWrongPleaseTryAgain,
        position: 'bottom',
        type: 'error',
      });
    },
  });*/



  const handleDrawerToggle = () => {
    props.navigation.dispatch(DrawerActions.toggleDrawer());
  };

  return (
    <>
      <DrawerContentScrollView {...props}>
        <DrawerItem
          label={''}
          labelStyle={{color: colors.grey}}
          icon={() =>
            loading || error ? (
              <CustomDrawerContentLoader />
            ) : (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: WIDTH - spacings.lg,
                  marginTop: spacings.lg,
                }}>
                <TouchableOpacity
                  style={styles.profileCard}
                  onPress={() =>
                    props.navigation.navigate(
                      credential?.isExpert
                        ? screens.EXPERTS_PROFILE
                        : screens.USER_PROFILE,
                      {
                        ...(credential?.isExpert
                          ? {expertUserId: credential?.user_id}
                          : {}),
                      },
                    )
                  }>
                  <FastImage
                    source={
                      user?.image
                        ? {uri: user?.image}
                        : images.PROFILE_PLACEHOLDER
                    }
                    style={styles.imageStyles}
                  />
                  <View style={styles.personDetailsWrapper}>
                    <Text style={styles.personDetailsText}>
                      {user?.username}
                    </Text>
                    <Text
                      style={[
                        styles.personDetailsText,
                        {color: colors.secondary},
                      ]}>
                      {logoutScreen.MY_PUBLIC_PROFILE}
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.drawerCloseButton}
                  onPress={handleDrawerToggle}>
                  <Icon
                    name={icons.CANCEL_X}
                    size={fontSize.lg}
                    tint={colors.white}
                  />
                </TouchableOpacity>
              </View>
            )
          }
        />
        <DrawerItem
          label={navLabels.HOME_TABS}
          onPress={() => {
            navigation.navigate(screens.TAB_NAVIGATOR, {screen: screens.HOME});
          }}
          labelStyle={{
            color: colors.grey,
            fontFamily: 'Inter-SemiBold',
            fontSize: fontSize.lg,
            textTransform: 'capitalize',
          }}
          style={{
            borderTopColor: colors.darkGrey,
            borderTopWidth: 1,
            paddingTop: 10,
          }}
          icon={({size}) => (
            <View style={styles.iconWrapper}>
              <Icon name={icons.HOME_ICON} size={size} tint={'#9E9E9E'} />
            </View>
          )}
        />
        <DrawerItemList {...props} />
        <DrawerItem
          label={navLabels.LOGOUT}
          onPress={() => setModalVisible(true)}
          labelStyle={{
            color: colors.grey,
            fontFamily: 'Inter-SemiBold',
            fontSize: fontSize.lg,
            textTransform: 'capitalize',
          }}
          icon={({size}) => (
            <View style={styles.iconWrapper}>
              <Icon name={icons.LOGOUT} size={size} tint={'#9E9E9E'} />
            </View>
          )}
        />

        <DrawerItem
          label={navLabels.DELETE}
          onPress={() => setDeleteModalVisible(true)}
          labelStyle={{
            color: colors.grey,
            fontFamily: 'Inter-SemiBold',
            fontSize: fontSize.lg,
            textTransform: 'capitalize',
          }}
          icon={({size}) => (
            <View style={styles.iconWrapper}>
              <Icon name={icons.TRASH_ICON} size={size} tint={'#9E9E9E'} />
            </View>
          )}
        />

      </DrawerContentScrollView>
      <InfoModal
        setVisible={setModalVisible}
        showLottieIcon={false}
        popUp={{
          state: modalVisible,
          data: {
            title: locale.Logout,
            description: locale.AreYouSureYouWantToLogout,
          },
        }}
        showDefaultButton={false}>
        <View style={{flexDirection: 'row'}}>
          <Button
            text={locale.No}
            style={{marginRight: 10, backgroundColor: colors.darkGrey}}
            onPress={() => setModalVisible(false)}
          />
          <Button
            text={locale.Yes}
            onPress={logout}
            disabled={isLogoutLoading}
            isLoading={isLogoutLoading}
            style={{backgroundColor: colors.secondaryRed}}
          />
        </View>
      </InfoModal>

      <DeleteInfoModal
        setVisible={setDeleteModalVisible}
        showLottieIcon={false}
        popUp={{
          state: deleteModalVisible,
          data: {
            title: locale.DeleteAccount,
            description: locale.AreYouSureYouWantToDelete,
          },
        }}
        showDefaultButton={false}>
        <View style={{flexDirection: 'row'}}>
          <Button
            text={locale.No}
            style={{marginRight: 10, backgroundColor: colors.darkGrey}}
            onPress={() => setDeleteModalVisible(false)}
          />
          <Button
            text={locale.Yes}
            onPress={deleteAccount}
            disabled={isDeleteLoading}
            isLoading={isDeleteLoading}
            style={{backgroundColor: colors.secondaryRed}}
          />
        </View>
      </DeleteInfoModal>
    </>
  );
};

export default Index;
