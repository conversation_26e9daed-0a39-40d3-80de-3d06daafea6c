/* eslint-disable prettier/prettier */
import {gql} from '@apollo/client';

export const UPDATE_PROFILE = gql`
  mutation updateProfile(
    $username: String
    $email: String
    $first_name: String
    $last_name: String
    $image: String
    $bio: String
    $social_media_link: String
    $allow_message_from_non_follower: <PERSON><PERSON><PERSON>
  ) {
    updateProfile(
      username: $username
      email: $email
      first_name: $first_name
      last_name: $last_name
      image: $image
      bio: $bio
      social_media_link: $social_media_link
      allow_message_from_non_follower: $allow_message_from_non_follower
    ) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const SET_ALL_NOTIFICATIONS_AS_READ = gql`
  mutation setAllNotificationAsRead {
    setAllNotificationAsRead {
      success
      message
      code
      errors {
        path
        message
      }
    }
  }
`;

export const UPDATE_PUSH_NOTIFICATION_SETTING = gql`
  mutation updatePushNotificationSetting(
    $allow_direct_message: Boolean
    $allow_post_reaction: Boolean
    $allow_authentication_submission_status: Boolean
    $allow_follow_status: Boolean
    $allow_in_app_direct_message: <PERSON>olean
    $allow_in_app_post_reaction: Boolean
    $allow_in_app_authentication_submission_status: Boolean
    $allow_in_app_follow_status: Boolean
  ) {
    updatePushNotificationSetting(
      allow_direct_message: $allow_direct_message
      allow_post_reaction: $allow_post_reaction
      allow_authentication_submission_status: $allow_authentication_submission_status
      allow_follow_status: $allow_follow_status

      allow_in_app_direct_message: $allow_in_app_direct_message
      allow_in_app_post_reaction: $allow_in_app_post_reaction
      allow_in_app_authentication_submission_status: $allow_in_app_authentication_submission_status
      allow_in_app_follow_status: $allow_in_app_follow_status
    ) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const DEACTIVATE_DEVICE_FOR_NOTIFICATIONS = gql`
  mutation deactivateDeviceForNotifications($deviceId: String!) {
    deactivateDeviceForNotifications(deviceId: $deviceId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const SET_SINGLE_NOTIFICATION_AS_READ = gql`
  mutation setNotificationAsRead($id: ID!) {
    setNotificationAsRead(id: $id) {
      success
      message
      code
      errors {
        path
        message
      }
    }
  }
`;

export const SYNC_DEVICE = gql`
  mutation syncDevice(
    $deviceId: String!
    $platform: DevicePlatform!
    $fcmToken: String!
    $deviceName: String
    $IPAddress: String
  ) {
    syncDevice(
      deviceId: $deviceId
      platform: $platform
      fcmToken: $fcmToken
      deviceName: $deviceName
      IPAddress: $IPAddress
    ) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const CHANGE_PASSWORD = gql`
  mutation changePassword($oldPassword: String, $newPassword: String) {
    changePassword(oldPassword: $oldPassword, newPassword: $newPassword) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const UNFOLLOW_USER = gql`
  mutation unfollowUser($userId: ID!) {
    unfollowUser(userId: $userId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;
export const FOLLOW_USER = gql`
  mutation userId($userId: ID!) {
    followUser(userId: $userId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const DELETE_A_CHAT = gql`
  mutation deleteChat($userId: Int!) {
    deleteChat(userId: $userId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;
export const BLOCK_USER = gql`
  mutation blockUser($userId: ID!) {
    blockUser(userId: $userId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const SEND_CHAT_MESSAGE = gql`
  mutation sendChatMessage(
    $toUserId: ID!
    $text: String
    $messageType: ChatMessageType!
  ) {
    sendChatMessage(
      toUserId: $toUserId
      text: $text
      messageType: $messageType
    ) {
      success
      message
      data {
        id
        text
        image_url
        message_type
        created_at
      }
      errors {
        path
        message
      }
    }
  }
`;

export const UPLOAD_FILE = gql`
  mutation uploadFile($file: Upload!) {
    uploadFile(file: $file) {
      success
      data {
        id
        path
      }
      message
      code
      errors {
        path
        message
      }
    }
  }
`;
export const ADD_COMMENT_TO_POST_IMAGE = gql`
  mutation addCommentToPostImage(
    $postId: ID!
    $authenticationImageType: AuthenticationImageType!
    $type: MessageType!
    $message: String
    $imageURL: String
  ) {
    addCommentToPostImage(
      postId: $postId
      authenticationImageType: $authenticationImageType
      type: $type
      message: $message
      imageURL: $imageURL
    ) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const ADD_COMMENT_TO_POST = gql`
  mutation addCommentToPost(
    $postId: ID!
    $type: MessageType!
    $message: String
    $imageURL: String
  ) {
    addCommentToPost(
      postId: $postId
      message: $message
      type: $type
      imageURL: $imageURL
    ) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const ADD_VOTE_TO_POST = gql`
  mutation addVoteToPost(
    $postId: ID!
    $decision: AuthenticationDecision!
    $appraisal_value: Float!
    $comment: String
  ) {
    addVoteToPost(
      postId: $postId
      decision: $decision
      appraisal_value: $appraisal_value
      comment: $comment
    ) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const APPLY_AUTHENTICATOR_CODE = gql`
  mutation applyAuthenticatorCode($code: String!, $queueId: ID!) {
    applyAuthenticatorCode(code: $code, queueId: $queueId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const APPLY_COUPON_CODE = gql`
  mutation applyCouponCode(
    $code: String!
    $queueId: ID!
    $isUpgradeFromExpertCertifiedToExpertCertifiedNFT: Boolean = false
  ) {
    applyCouponCode(
      code: $code
      queueId: $queueId
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT: $isUpgradeFromExpertCertifiedToExpertCertifiedNFT
    ) {
      success
      message
      errors {
        path
        message
      }
      code
      data {
        discountId
        isFullDiscount
      }
    }
  }
`;

export const CONFIRM_IAP_FOR_IOS = gql`
  mutation confirmIAPForIOS(
    $authenticationId: Int!
    $receipt: String!
    $iap_key: String!
    $isUpgradeFromExpertCertifiedToExpertCertifiedNFT: Boolean = false
    $data: String
    $discountId: Int
  ) {
    confirmIAPForIOS(
      authenticationId: $authenticationId
      receipt: $receipt
      iap_key: $iap_key
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT: $isUpgradeFromExpertCertifiedToExpertCertifiedNFT
      data: $data
      discountId: $discountId
    ) {
      success
      message
      errors {
        path
        message
      }
      code
      data {
        key
      }
    }
  }
`;

export const LOG_ACTIVITY_TO_SERVER = gql`
  mutation logActivity(
    $type: ActivityLogType!
    $data: String
    $message: String
  ) {
    logActivity(type: $type, data: $data, message: $message) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const CREATE_AUTHENTICATION = gql`
  mutation createAuthentication($input: AuthenticationInput!) {
    createAuthentication(input: $input) {
      success
      message
      errors {
        path
        message
      }
      code
      data {
        id
        authenticationKey
      }
    }
  }
`;

export const CREATE_PAYMENT_INTENT = gql`
  mutation createPaymentIntent(
    $authenticationId: Int!
    $discountId: Int
    $isUpgradeFromExpertCertifiedToExpertCertifiedNFT: Boolean
  ) {
    createPaymentIntent(
      authenticationId: $authenticationId
      discountId: $discountId
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT: $isUpgradeFromExpertCertifiedToExpertCertifiedNFT
    ) {
      success
      data {
        client_secret
        subtotal
        tax
        total
        ephemeralKey
        customerId
        discountAmount
        totalWithoutDiscount
        isDiscounted
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const CREATE_CUSTOM_BRAND = gql`
  mutation createCustomBrand($name: String!) {
    createCustomBrand(name: $name) {
      success
      message
      errors {
        path
        message
      }
      code
      data {
        id
        name
      }
    }
  }
`;

export const CONFRIM_PAYMENT = gql`
  mutation confrimPayment($paymentIntent: String!) {
    confirmPayment(paymentIntent: $paymentIntent) {
      success
      data {
        key
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const CONFIRM_CARD_PAYMENT = gql`
  mutation confirmCardPayment(
    $cardId: String!
    $authenticationId: Int!
    $discountId: Int
    $isUpgradeFromExpertCertifiedToExpertCertifiedNFT: Boolean
  ) {
    confirmCardPayment(
      cardId: $cardId
      authenticationId: $authenticationId
      discountId: $discountId
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT: $isUpgradeFromExpertCertifiedToExpertCertifiedNFT
    ) {
      success
      data {
        key
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const CONFIRM_PAYMENT_FOR_FULL_DISCOUNT = gql`
  mutation confirmPaymentForFullDiscount(
    $authenticationId: Int!
    $discountId: Int!
    $isUpgradeFromExpertCertifiedToExpertCertifiedNFT: Boolean
  ) {
    confirmPaymentForFullDiscount(
      authenticationId: $authenticationId
      discountId: $discountId
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT: $isUpgradeFromExpertCertifiedToExpertCertifiedNFT
    ) {
      success
      data {
        key
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const UPDATE_CARD = gql`
  mutation updateCard($cardId: String!, $name: String, $setAsDefault: Boolean) {
    updateCard(cardId: $cardId, name: $name, setAsDefault: $setAsDefault) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const DELETE_CARD = gql`
  mutation deleteCard($cardId: String!) {
    deleteCard(cardId: $cardId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const DELETE_CHAT_MESSAGE = gql`
  mutation deleteChatMessage($messageId: Int!) {
    deleteChatMessage(messageId: $messageId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const SET_ALL_CHAT_MESSAGES_AS_READ = gql`
  mutation setAllChatMessagesAsRead($userId: Int!) {
    setAllChatMessagesAsRead(userId: $userId) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const CREATE_CARD = gql`
  mutation createCard($token: String!, $setAsDefault: Boolean = false) {
    createCard(token: $token, setAsDefault: $setAsDefault) {
      success
      data {
        id
        name
        number
        brand
        expiry_month
        expiry_year
        is_default
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const DEACTIVATE_DEVICE = gql`
  mutation {
    deActivateDevice {
      success
      errors {
        path
        message
      }
      message
      code
    }
  }
`;

export const DELETE_POST = gql`
  mutation deletePost($postId: ID!) {
    deletePost(postId: $postId) {
      code
      success
      message
    }
  }
`;

export const DELETE_POST_VOTE = gql`
  mutation deleteVoteOfPost($id: ID!) {
    deleteVoteOfPost(id: $id) {
      code
      success
      message
    }
  }
`;

export const SET_POST_STATUS = gql`
  mutation deletePost($postId: ID!, $type: PostStatusType!) {
    setPostStatus(postId: $postId, type: $type) {
      code
      success
      message
    }
  }
`;



export const SET_POST_APPRAISAL_STATUS = gql`
  mutation editPrivate($appraisalId: ID!, $is_private: Boolean!) {
    editPrivate(appraisalId: $appraisalId, is_private: $is_private) {
      code
      success
      message
    }
  }
`;

export const CLAIM_NFT = gql`
  mutation claimNFT($authenticationId: Int!, $walletAddress: String!) {
    claimNFT(
      authenticationId: $authenticationId
      walletAddress: $walletAddress
    ) {
      success
      message
      code
      errors {
        path
        message
      }
    }
  }
`;
export const RESUBMIT_REJECTED_AUTHENTICATION = gql`
  mutation resubmitRejectedAuthentication(
    $authenticationId: ID!
    $input: AuthenticationInput!
  ) {
    resubmitRejectedAuthentication(
      authenticationId: $authenticationId
      input: $input
    ) {
      success
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const SEND_CONTACT_MESSAGE = gql`
  mutation sendContactMessage(
    $title: String!
    $description: String!
    $firstName: String
  ) {
    sendContactMessage(
      title: $title
      description: $description
      firstName: $firstName
    ) {
      success
      message
      code
    }
  }
`;

export const LIKE_POST = gql`
  mutation likePost($postId: ID!) {
    likePost(postId: $postId) {
      success
      message
      code
    }
  }
`;

export const UNLIKE_POST = gql`
  mutation unlikePost($postId: ID!) {
    unlikePost(postId: $postId) {
      success
      message
      code
    }
  }
`;

export const SUBSCRIBE_TO_POST_UPDATES = gql`
  mutation subscribeToPostUpdates(
    $postId: Int!
    $allowOnNewVote: Boolean!
    $allowOnNewComment: Boolean!
    $allowOnFinalDecision: Boolean!
  ) {
    subscribeToPostUpdates(
      postId: $postId
      allowOnNewVote: $allowOnNewVote
      allowOnNewComment: $allowOnNewComment
      allowOnFinalDecision: $allowOnFinalDecision
    ) {
      success
      message
      code
    }
  }
`;

export const UNSUBSCRIBE_FROM_POST_UPDATES = gql`
  mutation unsubscribeFromPostUpdates($postId: Int!) {
    unsubscribeFromPostUpdates(postId: $postId) {
      success
      message
      code
    }
  }
`;

export const REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION = gql`
  mutation requestToBeTaggedAsPreviousOwnerOfAuthentication(
    $postId: Int!
    $date: Date!
  ) {
    requestToBeTaggedAsPreviousOwnerOfAuthentication(
      postId: $postId
      date: $date
    ) {
      success
      message
      code
      errors {
        path
        message
      }
    }
  }
`;

export const TAG_PREVIOUS_OWNER_OF_AUTHENTICATION = gql`
  mutation tagPreviousOwnerOfAuthentication(
    $postId: Int!
    $userId: Int!
    $date: Date!
  ) {
    tagPreviousOwnerOfAuthentication(
      postId: $postId
      userId: $userId
      date: $date
    ) {
      success
      message
      code
      errors {
        path
        message
      }
    }
  }
`;

export const ACCEPT_TAG_PREVIOUS_OWNER_OF_AUTHENTICATION = gql`
  mutation acceptTagPreviousOwnerOfAuthentication($postOwnershipId: ID!) {
    acceptTagPreviousOwnerOfAuthentication(postOwnershipId: $postOwnershipId) {
      code
      success
      message
    }
  }
`;

export const REJECT_TAG_PREVIOUS_OWNER_OF_AUTHENTICATION = gql`
  mutation rejectTagPreviousOwnerOfAuthentication($postOwnershipId: ID!) {
    rejectTagPreviousOwnerOfAuthentication(postOwnershipId: $postOwnershipId) {
      code
      success
      message
    }
  }
`;

export const ACCEPT_REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION = gql`
  mutation acceptRequestToBeTaggedAsPreviousOwnerOfAuthentication(
    $postOwnershipId: ID!
  ) {
    acceptRequestToBeTaggedAsPreviousOwnerOfAuthentication(
      postOwnershipId: $postOwnershipId
    ) {
      code
      success
      message
    }
  }
`;

export const REJECT_REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION = gql`
  mutation rejectRequestToBeTaggedAsPreviousOwnerOfAuthentication(
    $postOwnershipId: ID!
  ) {
    rejectRequestToBeTaggedAsPreviousOwnerOfAuthentication(
      postOwnershipId: $postOwnershipId
    ) {
      code
      success
      message
    }
  }
`;

export const IS_WALLET_ADDRESS_VALID = gql`
  mutation isWalletAddressValid($walletAddress: String!) {
    isWalletAddressValid(walletAddress: $walletAddress) {
      code
      success
      message
    }
  }
`;
