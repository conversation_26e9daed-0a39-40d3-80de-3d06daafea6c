require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '13.0'


target 'authenteec8' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false
  )

  permissions_path = '../node_modules/react-native-permissions/ios'

  pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications"
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera"
  pod 'Permission-MediaLibrary', :path => "#{permissions_path}/MediaLibrary"
  pod 'Permission-Microphone', :path => "#{permissions_path}/Microphone"
  pod 'Permission-PhotoLibrary', :path => "#{permissions_path}/PhotoLibrary"

  pod 'react-native-webview', :path => '../node_modules/react-native-webview'
  pod 'RNFS', :path => '../node_modules/react-native-fs'



  target 'authenteec8Tests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  # use_flipper!()

  # post_install do |installer|
  #   react_native_post_install(installer)
  #   __apply_Xcode_12_5_M1_post_install_workaround(installer)
  # end
  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
        config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
        config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
       end
    end
  end
end
