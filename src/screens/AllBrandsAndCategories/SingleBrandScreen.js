import React, {useState, useCallback} from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import Icon from '../../components/Icon';
import HomeListingsLoader from '../../components/Loaders/HomeListingsLoader';
import {GET_ALL_POSTS, GET_BRAND} from '../../apollo/queries';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import {icons, screens, images} from '../../assets/strings';
import ActivityCard from '../../components/ActivityCard';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import {CategoryPicker, AuthenticationDecisionPicker} from '../../ui';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import locale from '../../assets/locale.json';
import RefreshControl from '../../components/RefreshControl';
import FastImage from '../../components/FastImage';
import {useInteractionManager} from '../../tools/hooks';

const {height: SCREEN_HEIGHT} = Dimensions.get('screen');

const LIMIT = 100;

const SingleBrandScreen = ({route, navigation}) => {
  const brandFromRoute = route?.params?.params;
  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const [categories, setCategories] = useState([]);
  const [selectedDecision, setSelectedDecision] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  const {data, refetch: refetchBrand} = useQuery(GET_BRAND, {
    variables: {
      id: brandFromRoute?.id,
    },
  });
  const brandData = data?.getBrand?.data ?? brandFromRoute;

  const categoryIds = React.useMemo(
    () => categories?.map(category => category?.id),
    [categories],
  );

  const {
    data: brandPosts,
    loading: brandPostLoading,
    refetch: refetchPosts,
  } = useQuery(GET_ALL_POSTS, {
    variables: {
      limit: LIMIT, // TODO PAGINATION
      filters: {
        brand_ids: [brandFromRoute?.id],
        ...(categoryIds?.length ? {category_ids: categoryIds} : {}),
        ...(selectedDecision == null ? {} : {decision: selectedDecision?.id}),
      },
      cacheId: APOLLO_CACHE_IDS.getPosts_POSTS_BY_BRAND,
    },
  });

  const handleCategoryChange = _categoryIds => {
    setCategories(_categoryIds);
  };

  const onRefresh = useCallback(async () => {
    try {
      setRefreshing(true);
      await Promise.all([refetchBrand(), refetchPosts()]);
    } catch (_) {
    } finally {
      setRefreshing(false);
    }
  }, [refetchBrand, refetchPosts]);

  useFocusEffect(
    React.useCallback(() => {
      (async function () {
        try {
          await Promise.all([refetchBrand?.(), refetchPosts?.()]);
        } catch (_) {}
      })();
    }, [refetchBrand, refetchPosts]),
  );

  const renderHeader = React.useMemo(() => {
    return (
      <>
        <View style={styles.topBar}>
          <Text style={styles.titleStyle}>{brandData?.name}</Text>
          <View style={styles.brandLogoStyle}>
            <FastImage
              style={styles.logo}
              source={
                brandData?.image_url
                  ? {uri: brandData?.image_url}
                  : images.logo1
              }
            />
          </View>
        </View>

        <View style={styles.filters}>
          <CategoryPicker
            onApply={handleCategoryChange}
            onClear={() => handleCategoryChange([])}
            selectedItems={categories}>
            {({openPicker}) => (
              <TouchableOpacity
                style={[
                  styles.pickerContainer,
                  {
                    backgroundColor:
                      categoryIds?.length > 0
                        ? colors.primary
                        : colors.lightGrey,
                  },
                ]}
                onPress={openPicker}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={[
                    styles.pickerText,
                    {
                      color:
                        categoryIds?.length > 0 ? colors.white : colors.black,
                    },
                  ]}>
                  {categoryIds?.length > 0
                    ? categories?.map(category => category?.name)?.join(', ')
                    : 'All Categories'}
                </Text>
                <Icon
                  name={icons.CHEVRON_DOWN}
                  tint={categoryIds?.length ? colors.white : colors.black}
                  size={spacings.md}
                />
              </TouchableOpacity>
            )}
          </CategoryPicker>

          <AuthenticationDecisionPicker onSelected={setSelectedDecision}>
            {({openPicker}) => (
              <TouchableOpacity
                style={[
                  styles.pickerContainer,
                  {
                    backgroundColor:
                      selectedDecision == null
                        ? colors.lightGrey
                        : colors.primary,
                  },
                ]}
                onPress={openPicker}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={[
                    styles.pickerText,
                    {
                      color:
                        selectedDecision == null ? colors.black : colors.white,
                    },
                  ]}>
                  {!(selectedDecision == null)
                    ? `Status: ${selectedDecision?.name}`
                    : 'Status: All'}
                </Text>
                <Icon
                  name={icons.CHEVRON_DOWN}
                  tint={
                    !(selectedDecision == null) ? colors.white : colors.black
                  }
                  size={spacings.md}
                />
              </TouchableOpacity>
            )}
          </AuthenticationDecisionPicker>
        </View>
      </>
    );
  }, [
    handleCategoryChange,
    brandData,
    categoryIds,
    setSelectedDecision,
    selectedDecision,
  ]);

  const renderEmpty = React.useMemo(() => {
    return isInteractionsComplete ? (
      <View style={styles.emptyView}>
        <DefaultEmptyComponent
          lottieStyle={{width: 100, height: 100}}
          text={locale.NoPostsFound}
        />
      </View>
    ) : null;
  }, [isInteractionsComplete]);

  const renderItem = React.useCallback(
    ({item}) =>
      isInteractionsComplete ? (
        <TouchableOpacity
          onPress={() =>
            navigation.push(screens.SINGLE_LISTING, {params: item.id})
          }>
          <ActivityCard item={item} />
        </TouchableOpacity>
      ) : null,
    [navigation.push, isInteractionsComplete],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  const isLoading = brandPostLoading && brandPosts == null;

  return (
    <View style={styles.container}>
      <FlatList
        data={brandPosts?.getPosts?.data?.edges}
        renderItem={renderItem}
        keyExtractor={extractKey}
        numColumns={2}
        columnWrapperStyle={styles.contentContainer}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={
          isLoading || !isInteractionsComplete ? <HomeListingsLoader /> : null
        }
        ListEmptyComponent={isLoading ? <HomeListingsLoader /> : renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        initialNumToRender={10}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  titleStyle: {
    color: colors.black,
    fontSize: fontSize.md,
    textTransform: 'capitalize',
    fontFamily: 'Gugi-Regular',
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  filters: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    marginVertical: spacings.md,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.grey,
  },
  pickerContainer: {
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 150,
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
    backgroundColor: colors.lightGrey,
    marginBottom: spacings.lg,
  },
  pickerText: {
    color: colors.black,
    fontSize: fontSize.md,
    textTransform: 'capitalize',
  },
  brandLogoStyle: {
    height: 30,
    width: 50,
    resizeMode: 'contain',
  },
  logo: {
    height: '90%',
    width: '90%',
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    marginTop: spacings.lg,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalSheetContainer: {
    paddingVertical: spacings.lg,
    marginHorizontal: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  checkedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 25,
    width: 25,
    borderRadius: 25,
    backgroundColor: colors.primary,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  uncheckedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 25,
    width: 25,
    borderRadius: 25,
    backgroundColor: colors.lightGrey,
    borderWidth: 2,
    borderColor: colors.grey,
  },
});

export default SingleBrandScreen;
