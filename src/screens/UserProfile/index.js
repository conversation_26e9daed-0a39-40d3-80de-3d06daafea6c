import React from 'react';
import {View, FlatList, Text, TouchableOpacity, Pressable} from 'react-native';
import {useLazyQuery} from '@apollo/client';
import {useIsFocused, useFocusEffect} from '@react-navigation/native';

import HomeListingsLoader from '../../components/Loaders/HomeListingsLoader';
import {GET_USER_FEED_ACTIVITY, GET_ALL_POSTS} from '../../apollo/queries';
import ProfileHeader from './Header';
import {styles} from './styles';
import colors from '../../assets/colors';
import {icons, screens, APOLLO_CACHE_IDS} from '../../assets/strings';
import {spacings} from '../../assets/spacing';
import Icon from '../../components/Icon';
import UserActivityCard from '../../components/UserActivityCard';
import locale from '../../assets/locale.json';
import {useUser} from '../../tools/hooks';
import RefreshControl from '../../components/RefreshControl';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ActivityCard from '../../components/ActivityCard';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import QueryManager from '../../components/QueryManager';
import {fontSize} from '../../assets/font';
import BottomSheet from '../../components/BottomSheet';
import ProfileLoader from '../../components/Loaders/ProfileLoader';

const LIMIT = 100;

const filterData = [
  {id: 1, name: 'Newest First', value: null},
  {id: 2, name: 'Voted Only | Fail', value: 'FAKE'},
  {id: 3, name: 'Voted Only | Pass', value: 'REAL'},
  {id: 4, name: 'Posted Only', value: null},
];

const Index = ({navigation}) => {
  const isFocused = useIsFocused();
  const scrollRef = React.useRef();

  const {user, refresh} = useUser({focused: isFocused});

  const [refreshing, setRefreshing] = React.useState(false);

  const [selectedFilter, setSelectedFilter] = React.useState(filterData[0]);

  const showPostsOnly = selectedFilter?.id == 4;

  const [getUserFeedActivity, {data, refetch, loading, error}] = useLazyQuery(
    GET_USER_FEED_ACTIVITY,
    {
      variables: {
        limit: LIMIT,
        userId: user?.id,
        filters: {
          ...(selectedFilter?.id == 3 || selectedFilter?.id == 2
            ? {decision: selectedFilter?.value}
            : {}),
        },
      },
    },
  );

  const [
    getUserPosts,
    {
      data: postsData,
      loading: postsLoading,
      error: postsError,
      refetch: refetchUserPosts,
    },
  ] = useLazyQuery(GET_ALL_POSTS, {
    variables: {
      limit: LIMIT,
      filters: {
        user_id: +user?.id,
      },
      cacheId: APOLLO_CACHE_IDS.getPosts_POSTS_BY_USER,
    },
  });

  const refetchAll = React.useCallback(
    () =>
      Promise.all([
        refresh(),
        selectedFilter.id == 4 ? refetchUserPosts() : refetch(),
      ]),
    [refetch, refresh, selectedFilter, refetchUserPosts],
  );

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      setRefreshing(enableRefreshControl);
      try {
        await refetchAll();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetchAll],
  );

  const handleSelection = item => {
    setSelectedFilter(item);
  };

  React.useEffect(() => {
    (async function () {
      try {
        const {data: _data} = await (selectedFilter?.id == 4
          ? getUserPosts()
          : getUserFeedActivity());
      } catch (_) {}
    })();
  }, [selectedFilter, getUserPosts, getUserFeedActivity]);

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetchAll?.();
        }
      } catch (_) {}
    }, [refetchAll]),
  );

  const renderItem = React.useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          style={styles.cardsGroup}
          onPress={() =>
            navigation.push(screens.SINGLE_LISTING, {params: item?.post?.id})
          }>
          {showPostsOnly ? (
            <ActivityCard item={item} />
          ) : (
            <UserActivityCard item={item} />
          )}
        </TouchAwareButton>
      );
    },
    [navigation.push, showPostsOnly],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  const lists = React.useMemo(
    () =>
      showPostsOnly
        ? postsData?.getPosts?.data?.edges
        : data?.getUserFeedActivity?.data?.edges,
    [
      data?.getUserFeedActivity?.data?.edges,
      postsData?.getPosts?.data?.edges,
      showPostsOnly,
    ],
  );

  return (
    <View style={styles.container}>
      <QueryManager
        data={showPostsOnly ? postsData : data}
        error={showPostsOnly ? postsError : error}
        loading={showPostsOnly ? postsLoading : loading}
        refetch={refetchAll}>
        <QueryManager.Data>
          <FlatList
            data={lists}
            columnWrapperStyle={styles.contentContainer}
            renderItem={renderItem}
            keyExtractor={extractKey}
            numColumns={2}
            ListHeaderComponent={
              <Header
                user={user}
                selectedFilter={selectedFilter}
                handleSelection={handleSelection}
              />
            }
            ListEmptyComponent={
              Array.isArray(lists) ? (
                <View style={styles.emptyView}>
                  <DefaultEmptyComponent
                    text={
                      showPostsOnly ? locale.NoPostsFound : locale.NoActivityYet
                    }
                    lottieStyle={{width: 100, height: 100, alignSelf: 'center'}}
                  />
                </View>
              ) : (showPostsOnly ? postsLoading : loading) ? (
                <HomeListingsLoader />
              ) : null
            }
            showsVerticalScrollIndicator={false}
            initialNumToRender={10}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            onScroll={e => {
              scrollRef.current = e?.nativeEvent?.contentOffset?.y;
            }}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <ProfileLoader />
          <View style={{marginVertical: 10}}>
            <HomeListingsLoader />
          </View>
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale.SomethingWentWrongPleaseTryAgain}
          style={{marginVertical: 10}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
    </View>
  );
};

function Header({user = null, selectedFilter, handleSelection = () => null}) {
  const [sheetVisible, setSheetVisible] = React.useState(false);

  const handleRowSelection = item => {
    handleSelection(item);
    setSheetVisible(false);
  };

  return (
    <>
      <ProfileHeader
        followersCount={user?.followersCount}
        followingCount={user?.followingsCount}
        user={user}
      />
      <View style={styles.userActivityTitleWrapper}>
        <Text style={styles.userActivityTitle}>Your Activity</Text>
        <TouchableOpacity
          style={styles.profileFilterButton}
          onPress={() => setSheetVisible(true)}>
          <Text style={styles.profileFilterButtonText}>
            {selectedFilter?.name}
          </Text>
          <Icon
            name={icons.CHEVRON_DOWN}
            size={fontSize.sm}
            tint={colors.darkGrey}
          />
        </TouchableOpacity>
      </View>
      <BottomSheet
        automaticallyAdjustHeight
        visible={sheetVisible}
        setVisible={setSheetVisible}>
        <View style={{padding: 20}}>
          <Text
            style={{
              color: colors.black,
              fontSize: fontSize.xl,
              fontWeight: 'bold',
            }}>
            Showing
          </Text>
          {filterData?.map(item => (
            <Pressable
              key={item?.id}
              style={{
                paddingVertical: spacings.md,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
              onPress={handleRowSelection.bind(null, item)}>
              <Text style={{color: colors.black, fontSize: fontSize.lg}}>
                {item?.name}
              </Text>
              <Icon
                name={
                  icons[
                    selectedFilter?.id == item?.id
                      ? 'CHECKED_RADIO'
                      : 'UNCHECKED_RADIO'
                  ]
                }
                size={20}
                tint={
                  selectedFilter?.id == item?.id ? colors.primary : colors.grey
                }
              />
            </Pressable>
          ))}
        </View>
      </BottomSheet>
    </>
  );
}

export default Index;
