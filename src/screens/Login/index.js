import React, {useEffect, useCallback, useState, useRef} from 'react';
import {Text, View, TouchableOpacity, ActivityIndicator} from 'react-native';
import {Formik, Field} from 'formik';
import * as yup from 'yup';
import {useDispatch} from 'react-redux';
import locale from '../../assets/locale.json';

import Layout from '../../layout';
import CustomInput from '../../components/AuthInput';
import {
  screens,
  loginScreen,
  passwordErrors,
  icons,
} from '../../assets/strings';
import {styles} from './styles';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {FORGOT_PASSWORD, LOGIN} from '../../tools/api';
import {loginSuccess} from '../../redux/actions/auth';
import Icon from '../../components/Icon';
import { showToast } from '../../components/Toast';

const LoginSchema = yup.object().shape({
  email: yup.string().email('Invalid email').required('The email is required'),
  loginpassword: yup.string().required(passwordErrors.REQUIRED),
});

const Index = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);
  const [passwordHidden, setPasswordHidden] = useState(true);
  const dispatch = useDispatch();

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setError(null);
      isMounted.current = false;
    };
  }, []);

  const signIn = useCallback(
    async credentials => {
      setError('');
      setIsLoading(true);
      try {
        const response = await LOGIN(credentials);
        const res = response?.data;
        if (res?.success) {
          setIsLoading(false);
          if(res?.two_factor) {
            if (res?.message?.length > 0) {
              showToast({message: res?.message});
            }             
            res.email = credentials.email          
            res.isFromProfile = false
            navigation.navigate(screens.FA2_VALIDATE_CODE, {params: res});                 
          } else {
            const access_token = res?.data?.access_token;
            const refresh_token = res?.data?.refresh_token;
            const userId = res?.data?.user_id || '';
            const roleId = res?.data?.role_id ?? 2;
            const is2FA = false;
            const userEmail = credentials.email;
            dispatch(
              loginSuccess({
                access_token,
                refresh_token,
                userId,
                roleId,
                is2FA,
                userEmail
              }),
            );
          }          
        } else {
          setIsLoading(false);
        }
      } catch (e) {
        setError(
          e?.response?.data?.message ?? locale.SomethingWentWrongPleaseTryAgain,
        );
        setIsLoading(false);
      }
    },
    [dispatch],
  );

  const togglePassword = useCallback(() => {
    setPasswordHidden(!passwordHidden);
  }, [passwordHidden]);

  return (
    <Layout contentContainerStyle={styles.container}>
      <Formik
        validationSchema={LoginSchema}
        initialValues={{
          email: '',
          loginpassword: '',
        }}
        onSubmit={async values => {
          let credentials = {
            email: values.email,
            password: values.loginpassword,
          };
          if (isMounted.current === true) {
            signIn(credentials);
          }
        }}>
        {({handleSubmit}) => (
          <>
            <View style={styles.loginContainer}>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>{loginScreen.EMAIL_LABEL}</Text>
                <Field
                  style={styles.inputField}
                  component={CustomInput}
                  name="email"
                  placeholder="<EMAIL>"
                  keyboardType="email-address"
                />
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {loginScreen.PASSWORD_LABEL}
                </Text>
                <View style={styles.passwordWrapper}>
                  <Field
                    style={styles.inputField}
                    component={CustomInput}
                    name="loginpassword"
                    placeholder="Password"
                    keyboardType="default"
                    secureTextEntry={passwordHidden}
                  />
                  <TouchableOpacity
                    style={styles.togglePassword}
                    onPress={togglePassword}>
                    {passwordHidden ? (
                      <Icon
                        name={icons.EYE_OPEN}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    ) : (
                      <Icon
                        name={icons.EYE_CLOSED}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.forgotPasswordSection}>
                <TouchableOpacity
                  onPress={() => navigation.navigate(screens.FORGOT_PASSWORD)}>
                  <Text style={styles.forgotPasswordText}>
                    {loginScreen.FORGOT_PASSWORD_BUTTON}
                  </Text>
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}>
                {isLoading ? (
                  <ActivityIndicator color={colors.white} />
                ) : (
                  <Text style={styles.buttonText}>
                    {loginScreen.BUTTON_LABEL}
                  </Text>
                )}
              </TouchableOpacity>
              {error ? (
                <View style={styles.errorWrapper}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : null}
            </View>
          </>
        )}
      </Formik>
    </Layout>
  );
};

export default Index;
