import {
  View,
  Text,
  TextInput,
  Pressable,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import React from 'react';
import {createStackNavigator, TransitionPresets} from '@react-navigation/stack';
import {
  NavigationContainer,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {useMutation} from '@apollo/client';

import {
  REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION,
  TAG_PREVIOUS_OWNER_OF_AUTHENTICATION,
} from '../../apollo/mutations';
import {useCredential} from '../../tools/hooks/useUser';
import {styles} from './styles';
import locale from '../../assets/locale.json';
import Tooltip from '../../components/Tooltip';
import BottomSheet from '../../components/BottomSheet';
import CalendarPicker from '../../components/CalendarPicker';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import Button from '../../components/Button';
import colors from '../../assets/colors';
import Users from '../SearchScreen/Users';
import Icon from '../../components/Icon';
import {showToast} from '../../components/Toast';
import OwnershipHistory from './OwnershipHistory';

function ProvenanceTagging({
  postAuthorId,
  postId,
  refetchPost = () => null,
  shouldRefresh = false,
}) {
  const {user_id} = useCredential();
  const [sheetIndex, setSheetIndex] = React.useState(0);

  const [sheetVisible, setSheetVisible] = React.useState(false);
  const [refresh, setRefresh] = React.useState(false);

  const isPostByMe = React.useMemo(() => user_id == postAuthorId, [
    user_id,
    postAuthorId,
  ]);

  const handleProvenanceTagging = () => {
    setSheetVisible(true);
  };

  const handleNavigationStateChange = React.useCallback(navigationState => {
    setSheetIndex(navigationState?.index);
  }, []);

  const handleSuccess = () => {
    setRefresh(true);
    refetchPost();
    setSheetVisible(false);
  };

  return (
    <View style={{marginVertical: 10}}>
      <OwnershipHistory
        postId={postId}
        shouldRefresh={refresh || shouldRefresh}
      />
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        <TouchAwareButton onPress={handleProvenanceTagging}>
          <Text style={[styles.provenanceTaggingTitle]}>
            {isPostByMe ? locale.TagPreviousOwner : locale.RequestToBeAdded}
          </Text>
        </TouchAwareButton>
        <Tooltip
          title={locale.ProvenanceTagging}
          description="Users can request to be added to a shirts ownership history which helps us establish a timeline for its provenance. Just request to be added and enter the date  ownership transfer occurred and, If your request is accepted, your username and date of ownership will appear in the shirt's ownership history.  You can also request to have other users added to the history of a shirt you post yourself."
          style={{marginLeft: 10}}
          enableSheetContent
        />
      </View>
      <BottomSheet
        key="provenance-tagging"
        visible={sheetVisible}
        setVisible={setSheetVisible}
        snapPoints={!isPostByMe ? [300] : [400, '85%']}
        index={sheetIndex}
        onDismiss={() => {
          setRefresh(false);
          setSheetIndex(0);
        }}>
        <Navigator
          onStateChange={handleNavigationStateChange}
          props={{
            isPostByMe,
            postId,
            handleSuccess,
          }}
        />
      </BottomSheet>
    </View>
  );
}

const SCREENS = {
  MAIN: 'MAIN',
  USER_SEARCH: 'USER_SEARCH',
};

const Stack = createStackNavigator();

function Navigator({onStateChange = () => null, props}) {
  return (
    <NavigationContainer independent onStateChange={onStateChange}>
      <Stack.Navigator
        defaultScreenOptions={{headerMode: 'screen'}}
        initialRouteName={SCREENS.MAIN}
        screenOptions={{
          ...TransitionPresets.SlideFromRightIOS,
          headerShown: false,
          safeAreaInsets: {top: 0},
          cardStyle: {
            backgroundColor: 'white',
            overflow: 'visible',
          },
        }}>
        <Stack.Screen
          name={SCREENS.MAIN}
          component={Main}
          initialParams={{props}}
        />
        <Stack.Screen name={SCREENS.USER_SEARCH} component={UserSearch} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

function Main() {
  const route = useRoute();
  const {isPostByMe, postId, handleSuccess = () => null} =
    route?.params?.props ?? {};

  const [loading, setLoading] = React.useState(false);
  const [date, setDate] = React.useState();
  const [user, setUser] = React.useState({
    id: null,
    username: null,
  });

  const [requestToBeTaggedAsPreviousOwnerOfAuthentication] = useMutation(
    REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION,
  );

  const [tagPreviousOwnerOfAuthentication] = useMutation(
    TAG_PREVIOUS_OWNER_OF_AUTHENTICATION,
  );

  const handleTagging = React.useCallback(async () => {
    setLoading(true);
    try {
      const {data} = await (isPostByMe
        ? tagPreviousOwnerOfAuthentication({
            variables: {
              postId: +postId,
              userId: +user?.id,
              date,
            },
          })
        : requestToBeTaggedAsPreviousOwnerOfAuthentication({
            variables: {
              postId: +postId,
              date,
            },
          }));
      const target =
        data?.[
          isPostByMe
            ? 'tagPreviousOwnerOfAuthentication'
            : 'requestToBeTaggedAsPreviousOwnerOfAuthentication'
        ];
      const success = target?.success;
      const message = target?.message;
      setLoading(false);
      if (success) {
        handleSuccess();
      } else {
        showToast({message: message ?? locale['SomethingWentWrong.']});
      }
    } catch (error) {
      setLoading(false);
      showToast({message: locale['SomethingWentWrong.']});
    }
  }, [
    isPostByMe,
    user,
    date,
    postId,
    handleSuccess,
    requestToBeTaggedAsPreviousOwnerOfAuthentication,
    tagPreviousOwnerOfAuthentication,
    user?.id,
  ]);

  return (
    <View style={{flex: 1, padding: 20}}>
      <Text style={styles.descTitle}>{locale.ProvenanceTagging}</Text>
      {isPostByMe ? <Owner user={user} setUser={setUser} /> : null}
      <CalendarInput date={date} setDate={setDate} />
      <View
        style={{
          borderWidth: 0.2,
          borderColor: colors.grey,
          marginBottom: 20,
        }}
      />
      <Button
        text={locale.Submit}
        style={{backgroundColor: colors.primary, minHeight: 50}}
        onPress={handleTagging}
        disabled={
          loading ||
          (!isPostByMe ? date == null : user?.id == null && date == null)
        }
        isLoading={loading}
      />
    </View>
  );
}

function Owner({user, setUser = () => null}) {
  const handleUserSearch = () => {
    navigation.navigate(SCREENS.USER_SEARCH);
  };
  const navigation = useNavigation();
  const route = useRoute();

  React.useEffect(() => {
    const userParam = route?.params?.user;
    if (userParam) {
      setUser({
        id: userParam?.id,
        username: userParam?.username,
      });
    }
  }, [route.params, setUser]);
  return (
    <View style={{marginTop: 20}}>
      <Text style={styles.provenanceTaggingInputLabel}>
        <Text style={{color: 'red'}}>*</Text>
        {locale.PreviousOwner}
      </Text>
      <TouchableOpacity
        style={styles.usernameInputField}
        onPress={handleUserSearch}>
        <Text
          style={[
            styles.usernameInputFieldPlaceholder,
            {
              color: user?.username?.length > 0 ? colors.black : colors.grey,
            },
          ]}>
          {user?.username?.length > 0 ? user?.username : locale.EnterUsername}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

function CalendarInput({date, setDate = () => null}) {
  const [datePickerVisible, setDatePickerVisible] = React.useState(false);

  const handleDatePicker = () => {
    setDatePickerVisible(true);
  };
  return (
    <>
      <View style={{marginVertical: 20}}>
        <Text style={styles.provenanceTaggingInputLabel}>
          <Text style={{color: 'red'}}>*</Text>
          {locale.DateOfOwnershipChange}
        </Text>
        <TouchableOpacity
          style={styles.usernameInputField}
          onPress={handleDatePicker}>
          <Text
            style={[
              styles.usernameInputFieldPlaceholder,
              {
                color: date == null ? colors.grey : colors.black,
              },
            ]}>
            {date == null ? locale.ChooseDate : date}
          </Text>
        </TouchableOpacity>
      </View>
      <CalendarPicker
        visible={datePickerVisible}
        setVisible={setDatePickerVisible}
        onDayPress={item => {
          setDate(item?.dateString);
          setDatePickerVisible(false);
        }}
        date={date}
      />
    </>
  );
}

function UserSearch() {
  const {user_id} = useCredential();

  const navigation = useNavigation();

  const timeoutRef = React.useRef();
  const timeout2Ref = React.useRef();

  const [term, setTerm] = React.useState('');
  const inputRef = React.useRef();

  const handleUserPress = React.useCallback(
    item => {
      Keyboard.dismiss();
      timeoutRef.current = setTimeout(() => {
        navigation.navigate(SCREENS.MAIN, {
          user: item,
        });
      }, 300);
    },
    [navigation.navigate],
  );

  const onBackPress = React.useCallback(() => {
    Keyboard.dismiss();
    timeoutRef.current = setTimeout(() => {
      navigation.goBack();
    }, 300);
  }, [navigation.goBack]);

  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      inputRef.current?.focus?.();
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      clearTimeout(timeoutRef.current);
      clearTimeout(timeout2Ref.current);
    };
  }, []);

  return (
    <View style={{flex: 1}}>
      <View
        style={{
          marginHorizontal: 20,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <TouchableOpacity onPress={onBackPress} style={{paddingVertical: 10}}>
          <Icon
            style={{marginRight: 10}}
            name="backNavigation"
            size={20}
            tint={colors.darkGrey}
          />
        </TouchableOpacity>
        <View style={styles.searchBarWrapper}>
          <Icon
            style={{padding: 10}}
            name="search_icon"
            size={25}
            tint={colors.darkGrey}
          />
          <TextInput
            ref={inputRef}
            value={term}
            onChangeText={setTerm}
            selectionColor={colors.primary}
            placeholder={locale.Search}
            autoCapitalize="sentences"
            autoCorrect={false}
            placeholderTextColor={colors.black}
            returnKeyType="search"
            style={styles.searchBar}
          />
        </View>
      </View>
      <Users
        term={term}
        handleItemPress={handleUserPress}
        roleIds={[2]}
        userIdsToBeIgnored={[user_id]}
      />
    </View>
  );
}

export default React.memo(ProvenanceTagging);
