import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  TextInput,
} from 'react-native';
import {useMutation, useLazyQuery} from '@apollo/client';
import IAP from 'react-native-iap';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  APPLY_AUTHENTICATOR_CODE,
  APPLY_COUPON_CODE,
  CONFIRM_IAP_FOR_IOS,
  LOG_ACTIVITY_TO_SERVER,
} from '../../apollo/mutations';
import {GET_PAYMENT_DETAILS} from '../../apollo/queries';
import {
  authenticationTypeScreen,
  screens,
  persistent,
} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Icon from '../../components/Icon';
import Layout from '../../layout';
import ActivityIndicator from '../../components/ActivityIndicator';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';

const POPUP_INITIAL_STATE = {
  state: false,
  text: '',
};

const PAYMENT_STATE = {
  isCouponApplied: false,
  discountId: null,
  data: {
    total: null,
    subtotal: null,
    tax: null,
    discountAmount: null,
    totalWithoutDiscount: null,
    isDiscounted: false,
    isFullDiscount: false,
    iapKeyIOS: null,
  },
};

const INITIAL_PURCHASE_STATE = {
  success: false,
  data: null,
  message: '',
};

const IAP_ERROR_MESSAGE_MAPPING = {
  E_USER_CANCELLED:
    locale.PleaseFinishAllTheInAppPurchaseStepsToCompleteThePurchase,
};

const CheckOut = ({route, navigation}) => {
  const authenticationDetail = route?.params?.authenticationDetail;
  const authId = route?.params?.authId;
  const iapKeyForAuthentication = route?.params?.iapKeyForAuthentication;

  const [getNFT] = React.useState(route?.params?.getNFTDetails ?? null);
  const [purchaseState, setPurchaseState] = React.useState(
    INITIAL_PURCHASE_STATE,
  );

  const listeners = React.useRef({
    purchaseUpdatedListener: null,
    purchaseErrorListener: null,
  });

  const authenticationType = React.useMemo(
    () => route?.params?.authenticationType,
    [route?.params?.authenticationType],
  );

  const [paymentState, setPaymentState] = React.useState({
    ...PAYMENT_STATE,
    data: {
      ...PAYMENT_STATE.data,
      subtotal: authenticationDetail?.iap_price_ios,
      total: authenticationDetail?.iap_price_ios,
      tax: 0,
    },
  });

  const [globalDisable, setGlobalDisable] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authCode, setAuthCode] = useState(null);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [applyAuthenticatorCode] = useMutation(APPLY_AUTHENTICATOR_CODE);
  const [confirmIAPForIOS] = useMutation(CONFIRM_IAP_FOR_IOS);
  const [logActivityToServer] = useMutation(LOG_ACTIVITY_TO_SERVER);

  const detachListeners = () => {
    if (!(listeners.current.purchaseUpdatedListener == null)) {
      listeners.current.purchaseUpdatedListener.remove();
      listeners.current.purchaseUpdatedListener = null;
    }
    if (!(listeners.current.purchaseErrorListener == null)) {
      listeners.current.purchaseErrorListener.remove();
      listeners.current.purchaseErrorListener = null;
    }
  };

  const confirmPayment = React.useCallback(
    async (
      receiptData,
      isRetrying = false,
      iapKey = null,
      discountId = null,
    ) => {
      try {
        if (isRetrying) {
          setIsLoading(true);
        }

        const response = await confirmIAPForIOS({
          variables: {
            authenticationId: +authId,
            receipt: receiptData?.transactionReceipt,
            iap_key: iapKey ?? iapKeyForAuthentication,
            isUpgradeFromExpertCertifiedToExpertCertifiedNFT: !(getNFT == null),
            data: JSON.stringify(receiptData),
            discountId,
          },
        });
        setIsLoading(false);
        const isSuccessful = response?.data?.confirmIAPForIOS?.success;
        const code = response?.data?.confirmIAPForIOS?.code;
        const message = response?.data?.confirmIAPForIOS?.message;

        if (isSuccessful) {
          navigation.navigate(screens.AUTH_SUCCESSFUL, {
            params: response?.data?.confirmIAPForIOS?.data?.key,
            authenticationType: authenticationDetail?.type,
          });
        } else {
          if (code === 'PAYMENT_ALREADY_DONE') {
            navigation.navigate(screens.AUTH_SUCCESSFUL, {
              params: response?.data?.confirmIAPForIOS?.data?.key,
              authenticationType: authenticationDetail?.type,
            });
          } else {
            setPopup({
              state: true,
              text: message ?? locale.SomethingWentWrongPleaseTryAgain,
            });
          }
        }
      } catch (error) {
        setIsLoading(false);
        setPopup({
          state: true,
          text: locale.SomethingWentWrongPleaseTryAgain,
        });
      }
    },
    [
      navigation.navigate,
      authId,
      iapKeyForAuthentication,
      authenticationDetail?.type,
    ],
  );

  const initializePurchaseListener = React.useCallback(
    (iapKey = null, discountId = null) => {
      try {
        detachListeners();
        listeners.current.purchaseUpdatedListener = IAP.purchaseUpdatedListener(
          purchase => {
            // Log to server  silently
            logActivityToServer({
              variables: {
                type: 'IN_APP_PURCHASE_SUCCESS_IOS_CLIENT',
                data: JSON.stringify({purchase, iapKey, discountId, authId}),
                message: 'IOS: In app purchase successful.',
              },
            })
              .then(() => null)
              .catch(() => null);

            setPurchaseState({
              ...INITIAL_PURCHASE_STATE,
              success: true,
              data: purchase,
            });
            IAP.finishTransaction(purchase);
            confirmPayment(purchase, false, iapKey, discountId);
          },
        );
        listeners.current.purchaseErrorListener = IAP.purchaseErrorListener(
          errorResponse => {
            setIsLoading(false);

            setPopup({
              state: true,
              text:
                errorResponse?.message ??
                locale.SomethingWentWrongPleaseTryAgain,
            });
            // Log to server  silently
            logActivityToServer({
              variables: {
                type: 'IN_APP_PURCHASE_ERROR_IOS_CLIENT',
                data: JSON.stringify({
                  error: errorResponse,
                  iapKey,
                  discountId,
                  authId,
                }),
                message: errorResponse?.message ?? '',
              },
            })
              .then(() => null)
              .catch(() => null);
          },
        );
      } catch (_error) {
        setPopup({
          state: true,
          text: locale.SomethingWentWrongPleaseTryAgain,
        });
      }
    },
    [
      detachListeners,
      authenticationDetail?.type,
      confirmPayment,
      logActivityToServer,
      authId,
    ],
  );

  const handlePayment = async () => {
    setIsLoading(true);
    if (authCode) {
      try {
        const authCodeResponse = await applyAuthenticatorCode({
          variables: {
            code: authCode,
            queueId: authId,
          },
        });
        const isSuccess =
          authCodeResponse?.data?.applyAuthenticatorCode?.success;
        const code = authCodeResponse?.data?.applyAuthenticatorCode?.code;
        const message = authCodeResponse?.data?.applyAuthenticatorCode?.message;

        if (!isSuccess) {
          setIsLoading(false);
          if (code?.length) {
            setPopup({
              state: true,
              text: message ?? locale.SomethingWentWrongPleaseTryAgain,
            });
            return;
          } else {
            throw new Error();
          }
        }
      } catch (e) {
        setIsLoading(false);
        setPopup({state: true, text: locale.SomethingWentWrongPleaseTryAgain});
        return;
      }
    }

    if (paymentState.data.isFullDiscount) {
      await confirmPayment(
        {transactionReceipt: (+new Date()).toString()},
        false,
        paymentState.data.iapKeyIOS,
        paymentState.discountId,
      );
    } else {
      // In App purchases
      console.log("Kapil Testing iapKeyForAuthentication", iapKeyForAuthentication);
      try {
        await IAP.initConnection();
        const _iapKey = paymentState.isCouponApplied
          ? paymentState.data.iapKeyIOS
          : iapKeyForAuthentication;
        // await IAP.getProducts([_iapKey]);
        // await IAP.requestPurchase(_iapKey)

        const availablePurchases = await IAP.getProducts([
          _iapKey,
        ]);

        const product = availablePurchases?.find(
          purchase => purchase?.productId == _iapKey,
        );

        if (!product) {
          
          setIsLoading(false);
          const errorCode = error?.code;
          setPopup({
            state: true,
            text:locale.SomethingWentWrongPleaseTryAgain,
          });
          // Log to server  silently
          /*logActivityToServer({
            variables: {
              type: 'IN_APP_PURCHASE_ERROR_IOS_CLIENT',
              data: JSON.stringify({error}),
              message: locale.SomethingWentWrongPleaseTryAgain,
            },
          })
            .then(() => null)
            .catch(() => null);*/
        } else {          
          const sku = product.productId
          await IAP.requestPurchase({sku});
          initializePurchaseListener(
            paymentState.isCouponApplied ? _iapKey : null,
            paymentState.isCouponApplied ? paymentState.discountId : null,
          );
          await IAP.clearProductsIOS();
        }

        
      } catch (error) {
        setIsLoading(false);
        console.log("Kapil Testing iapKeyForAuthentication", iapKeyForAuthentication);
        const errorCode = error?.code;
        setPopup({
          state: true,
          text:
            IAP_ERROR_MESSAGE_MAPPING?.[errorCode] ??
            error?.message ??
            locale.SomethingWentWrongPleaseTryAgain,
        });
        // Log to server  silently
        logActivityToServer({
          variables: {
            type: 'IN_APP_PURCHASE_ERROR_IOS_CLIENT',
            data: JSON.stringify({error}),
            message: error?.message ?? '',
          },
        })
          .then(() => null)
          .catch(() => null);
      }
    }
  };

  const mapTitle = React.useMemo(() => {
    if (getNFT == null) {
      switch (authenticationType) {
        case 3:
          return 'Expert Certified NFT';
        case 5:
          return 'Expert Feedback';
        default:
          return 'Expert Certified';
      }
    } else {
      return 'Get NFT';
    }
  }, [authenticationType, getNFT]);

  React.useEffect(() => {
    return () => {
      detachListeners();
    };
  }, [detachListeners]);

  const isDisabled = globalDisable || isLoading;

  return (
    <Layout style={styles.container}>
      <Text style={styles.labelTitle}>{locale.CheckOut}</Text>
      <View style={styles.taxSection}>
        <View style={styles.desc}>
          <Text style={styles.label}>{mapTitle} (1x)</Text>
          <Text style={styles.label}>
            ${(paymentState?.data?.subtotal).toFixed(2)}
          </Text>
        </View>
        {paymentState.isCouponApplied ? (
          <View style={styles.desc}>
            <Text style={[styles.totalLabel]}>{locale.Discount}</Text>
            <Text style={[styles.totalLabel, {color: colors.green}]}>
              - ${(paymentState?.data?.discountAmount).toFixed(2)}
            </Text>
          </View>
        ) : null}
        <View style={styles.desc}>
          <Text style={[styles.totalLabel]}>
            {authenticationTypeScreen?.TOTAL}
          </Text>
          <Text style={styles.totalLabel}>
            ${(paymentState?.data?.total).toFixed(2)}
          </Text>
        </View>
      </View>
      {getNFT == null ? (
        <View>
          <Text style={styles.label}>Authenticator code</Text>
          <TextInput
            style={styles.inputStyles}
            placeholderTextColor={colors.grey}
            selectionColor={colors.grey}
            value={authCode}
            onChangeText={setAuthCode}
          />
        </View>
      ) : null}

      <ApplyCoupon
        setGlobalDisable={setGlobalDisable}
        setPaymentState={setPaymentState}
        queueId={authId}
        isUpgradeFromExpertCertifiedToExpertCertifiedNFT={!(getNFT == null)}
      />
      <TouchableOpacity
        style={styles.checkoutButton}
        onPress={
          purchaseState.success
            ? confirmPayment.bind(null, purchaseState.data, true)
            : handlePayment
        }
        disabled={isDisabled}>
        {isLoading ? (
          <ActivityIndicator color={colors.white} />
        ) : purchaseState.success ? (
          <>
            <Icon name="reload" tint={colors.white} size={18} />
            <Text style={styles.checkoutButtonText}>{locale.Retry}</Text>
          </>
        ) : (
          <>
            <Icon name="lock" tint={colors.white} size={18} />
            <Text style={styles.checkoutButtonText}>
              {authenticationTypeScreen.PAY_NOW}
            </Text>
          </>
        )}
      </TouchableOpacity>
      <InfoModal
        setVisible={() => setPopup(POPUP_INITIAL_STATE)}
        popUp={{
          state: popup.state,
          isError: true,
          data: {title: locale.Error, description: popup.text},
        }}
      />
    </Layout>
  );
};

function ApplyCoupon({
  setGlobalDisable = () => null,
  queueId = null,
  setPaymentState = () => null,
  isUpgradeFromExpertCertifiedToExpertCertifiedNFT = false,
}) {
  const [couponText, setCouponText] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [applyCouponCode] = useMutation(APPLY_COUPON_CODE);
  const [getDiscountedPaymentDetail] = useLazyQuery(GET_PAYMENT_DETAILS);

  const handleApplyCouponCode = React.useCallback(
    async couponCode => {
      setIsLoading(true);
      setGlobalDisable(true);
      try {
        const {data} = await applyCouponCode({
          variables: {
            code:
              couponText?.length > 0 ? couponText : couponCode ?? couponText,
            queueId,
            ...(isUpgradeFromExpertCertifiedToExpertCertifiedNFT
              ? {isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true}
              : {}),
          },
        });
        const isCouponApplySuccessful = data?.applyCouponCode?.success;
        const isCouponApplyCode = data?.applyCouponCode?.code;
        const isCouponApplyMessage = data?.applyCouponCode?.message;
        const discountId = data?.applyCouponCode?.data?.discountId;

        if (!isCouponApplySuccessful) {
          if (isCouponApplyCode?.length > 0) {
            setPopup(previousState => ({
              ...previousState,
              state: true,
              text: isCouponApplyMessage,
            }));
            if (
              ['COUPON_NOT_FOUND', 'COUPON_ALREADY_USED'].includes(
                isCouponApplyCode,
              )
            ) {
              await AsyncStorage.removeItem(persistent.COUPON_CODE);
            }
          } else {
            throw new Error();
          }
        } else {
          const {
            data: discountedPaymentDetail,
          } = await getDiscountedPaymentDetail({
            variables: {
              authenticationId: +queueId,
              discountId: +discountId,
              isIOS: true,
              ...(isUpgradeFromExpertCertifiedToExpertCertifiedNFT
                ? {isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true}
                : {}),
            },
          });

          const discountedSuccessful =
            discountedPaymentDetail?.getPaymentDetails?.success;
          const discountedCode =
            discountedPaymentDetail?.getPaymentDetails?.code;
          const discountedMessage =
            discountedPaymentDetail?.getPaymentDetails?.message;

          if (!discountedSuccessful) {
            if (discountedCode?.length > 0) {
              setPopup(previousState => ({
                ...previousState,
                state: true,
                text: discountedMessage,
              }));
            } else {
              throw new Error();
            }
          } else {
            const discountedData =
              discountedPaymentDetail?.getPaymentDetails?.data;

            setPaymentState(previousState => ({
              ...previousState,
              isCouponApplied: true,
              discountId,
              data: {
                total: discountedData?.total / 100,
                subtotal: discountedData?.subtotal / 100,
                tax: discountedData?.tax / 100,
                isDiscounted: discountedData?.isDiscounted,
                discountAmount: discountedData?.discountAmount / 100,
                totalWithoutDiscount:
                  discountedData?.totalWithoutDiscount / 100,
                isFullDiscount: discountedData?.isFullDiscount,
                iapKeyIOS: discountedData?.iapKeyIOS,
              },
            }));
          }
        }
      } catch (_) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          text: locale.SomethingWentWrongPleaseTryAgain,
        }));
      } finally {
        setGlobalDisable(false);
        setIsLoading(false);
      }
    },
    [
      couponText,
      queueId,
      setGlobalDisable,
      setPaymentState,
      applyCouponCode,
      getDiscountedPaymentDetail,
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT,
    ],
  );

  useFocusEffect(
    React.useCallback(function () {
      (async () => {
        try {
          const couponCode = await AsyncStorage.getItem(persistent.COUPON_CODE);
          if (couponCode?.length > 0) {
            handleApplyCouponCode(couponCode);
          }
        } catch (_) {}
      })();
    }, []),
  );

  return (
    <View>
      <Text style={styles.label}>Coupon:</Text>
      <View style={{flexDirection: 'row', flex: 1}}>
        <TextInput
          style={[
            styles.inputStyles,
            {textTransform: 'uppercase', flex: 1, marginRight: 10},
          ]}
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
          value={couponText}
          onChangeText={val => setCouponText(val)}
          autoCapitalize="characters"
        />
        <Button
          text={locale.Apply}
          style={{height: 50, borderRadius: 4}}
          isLoading={isLoading}
          disabled={isLoading || couponText?.length == 0}
          textStyle={{fontSize: 16, fontFamily: 'Inter-SemiBold'}}
          onPress={handleApplyCouponCode}
        />
      </View>
      <InfoModal
        setVisible={() => setPopup(POPUP_INITIAL_STATE)}
        popUp={{
          state: popup.state,
          isError: true,
          data: {title: locale.Error, description: popup.text},
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
    marginVertical: 30,
  },
  emptyContainer: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentType: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
    flexWrap: 'wrap',
  },
  inputStyles: {
    height: 50,
    borderWidth: 1,
    color: colors.black,
    borderRadius: 4,
    width: '100%',
    borderColor: colors.grey,
    marginBottom: spacings.lg,
    paddingLeft: spacings.md,
  },
  desc: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
    flexWrap: 'wrap',
  },
  taxSection: {
    marginBottom: spacings.lg,
    paddingBottom: spacings.lg,
  },
  checkoutButton: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.xxl,
    borderRadius: 4,
    flexDirection: 'row',
  },
  checkoutButtonText: {
    fontSize: fontSize.lg,
    color: colors.white,
    marginLeft: spacings.sm,
    fontWeight: 'bold',
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.black,
    marginBottom: spacings.sm,
    fontFamily: 'Inter-Regular',
  },
  totalLabel: {
    fontWeight: 'bold',
    fontSize: fontSize.lg,
    color: colors.black,
    marginBottom: spacings.sm,
  },
  changeButton: {
    alignItems: 'flex-end',
    paddingBottom: spacings.md,
  },
  changeButtonText: {
    fontWeight: 'bold',
    color: colors.primary,
    fontSize: fontSize.lg,
  },
  errorStyles: {
    color: colors.warning,
    fontSize: fontSize.sm,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  labelTitle: {
    fontSize: 20,
    color: colors.black,
    marginBottom: 10,
    fontFamily: 'Gugi-Regular',
  },
});

export default CheckOut;
