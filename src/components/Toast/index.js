import React from 'react';
import Toast from 'react-native-toast-message';

export const showToast = ({
  type = 'error',
  message = '',
  position = 'top',
  duration = 3000,
}) =>
  Toast.show({
    type,
    position,
    text1: message,
    visibilityTime: duration,
    autoHide: true,
    topOffset: 30,
    bottomOffset: 40,
    onShow: () => {},
    onHide: () => {},
    onPress: () => {},
    props: {},
  });

export const hideToast = () => Toast.hide();
