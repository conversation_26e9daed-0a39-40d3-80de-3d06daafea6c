import {View} from 'react-native';
import React from 'react';
import {useRoute, useNavigation} from '@react-navigation/native';
import {useLazyQuery} from '@apollo/client';
import {useDispatch} from 'react-redux';

import {GET_REJECTED_AUTHENTICATION} from '../../apollo/queries/index';
import colors from '../../assets/colors';
import {screens} from '../../assets/strings';
import ActivityIndicator from '../../components/ActivityIndicator';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import {clearAllPhotosFromState} from '../../redux/actions/uploadMedia';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: locale.Error,
    description: '',
  },
};

export default function withAuthenticationResubmission(
  WrappedComponent = null,
) {
  return props => {
    const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
    const [canProceed, setCanProceed] = React.useState(false);

    const dispatch = useDispatch();

    const navigation = useNavigation();
    const route = useRoute();
    const {isAuthenticationResubmission, authenticationId} = route.params ?? {};

    const [getRejectedAuthentication, {data, error}] = useLazyQuery(
      GET_REJECTED_AUTHENTICATION,
      {
        variables: {
          id: authenticationId,
        },
      },
    );

    const resubmissionData = data?.getRejectedAuthentication?.data;

    React.useEffect(() => {
      if (isAuthenticationResubmission && authenticationId) {
        dispatch(clearAllPhotosFromState());
        getRejectedAuthentication();
      } else {
        setCanProceed(true);
      }
    }, [authenticationId, isAuthenticationResubmission, dispatch]);

    React.useEffect(() => {
      if (data) {
        const isSuccess = data?.getRejectedAuthentication?.success;
        const code = data?.getRejectedAuthentication?.code;
        const message = data?.getRejectedAuthentication?.message;
        if (isSuccess) {
          setCanProceed(true);
        } else {
          setPopup(previousState => ({
            ...previousState,
            state: true,
            isError: true,
            data: {
              title: locale.Error,
              description: code?.length
                ? message
                : locale.SomethingWentWrongPleaseTryAgain,
            },
          }));
        }
      }
      if (error) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: true,
          data: {
            title: locale.Error,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }));
      }
    }, [data, error]);

    return (
      <>
        {!canProceed ? (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: colors.white,
            }}>
            <ActivityIndicator size={30} />
          </View>
        ) : (
          <WrappedComponent
            {...props}
            {...{isAuthenticationResubmission, authenticationId}}
            resubmissionData={resubmissionData}
          />
        )}
        <InfoModal
          setVisible={() => {
            setPopup(previousState => ({
              ...previousState,
              state: false,
            }));
            if (navigation.canGoBack()) {
              navigation.goBack();
            } else {
              navigation.navigate(screens.DRAW_NAVIGATOR);
            }
          }}
          popUp={popup}
        />
      </>
    );
  };
}
