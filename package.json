{"name": "authenteec8", "version": "1.0.1", "private": true, "keywords": ["react", "react-native", "blockchain", "dapp", "ethereum", "web3", "starter"], "scripts": {"android": "react-native run-android", "ios": "react-native run-ios --simulator='iPhone 15 Plus'", "start": "react-native start --reset-cache", "releaseMode-android": "react-native run-android --variant release", "releaseMode-ios": "react-native run-ios --configuration release --simulator='iPhone 15 Plus'", "test": "jest --config ./jest.config.js", "lint": "eslint .", "generate-splash": "react-native generate-bootsplash ./src/assets/splash/splash.png --background-color=000000 --logo-width=200", "postinstall": "patch-package"}, "dependencies": {"@apollo/client": "^3.5.8", "@apollo/react-hooks": "^4.0.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@gorhom/bottom-sheet": "^4.4.5", "@notifee/react-native": "^4.1.0", "@react-native-async-storage/async-storage": "1.13.4", "@react-native-camera-roll/camera-roll": "^5.2.0", "@react-native-community/blur": "^3.6.0", "@react-native-community/cli-debugger-ui": "^5.0.1", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/netinfo": "^6.0.0", "@react-native-firebase/app": "^14.3.0", "@react-native-firebase/messaging": "^14.3.0", "@react-native-masked-view/masked-view": "^0.2.6", "@react-navigation/bottom-tabs": "^6.0.9", "@react-navigation/drawer": "^6.1.8", "@react-navigation/material-top-tabs": "^6.0.6", "@react-navigation/native": "^6.0.6", "@react-navigation/stack": "^6.0.11", "@stripe/stripe-react-native": "^0.2.3", "@tradle/react-native-http": "^2.0.0", "@walletconnect/encoding": "1.0.2", "@web3modal/react-native": "^1.0.0-alpha.5", "apollo-upload-client": "^17.0.0", "apollo3-cache-persist": "^0.14.0", "axios": "^0.25.0", "base-64": "1.0.0", "big-integer": "^1.6.51", "buffer": "6.0.3", "dayjs": "^1.10.7", "deepmerge": "^4.2.2", "env": "^0.0.2", "event-emitter": "^0.3.5", "events": "^1.1.1", "formik": "^2.2.9", "global": "^4.4.0", "graphql": "^15.3.0", "https-browserify": "~0.0.0", "localstorage-polyfill": "^1.0.1", "lottie-ios": "~3.4.0", "lottie-react-native": "^5.1.6", "mime": "^3.0.0", "node-libs-browser": "2.2.1", "parse": "^3.4.1", "patch-package": "^6.5.1", "path-browserify": "0.0.0", "postinstall-postinstall": "^2.1.0", "react": "^17.0.2", "react-blockies": "^1.4.1", "react-dom": "^17.0.2", "react-native": "^0.64.4", "react-native-autolink": "^4.0.0", "react-native-blurhash": "^1.1.10", "react-native-bootsplash": "^4.7.5", "react-native-calendars": "^1.1292.0", "react-native-camera": "^4.2.1", "react-native-crypto": "^2.1.0", "react-native-device-info": "^8.4.8", "react-native-dotenv": "2.4.3", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~1.10.2", "react-native-get-random-values": "^1.8.0", "react-native-iap": "^8.0.10", "react-native-image-crop-picker": "^0.37.3", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.5.6", "react-native-localhost": "1.0.0", "react-native-modal": "^13.0.1", "react-native-os": "^1.0.1", "react-native-pager-view": "^5.4.9", "react-native-permissions": "^3.2.0", "react-native-public-ip": "^1.0.2", "react-native-qrcode-svg": "^6.1.2", "react-native-randombytes": "^3.6.1", "react-native-reanimated": "^2.9.1", "react-native-safe-area-context": "3.2.0", "react-native-screens": "~3.4.0", "react-native-share": "^7.3.7", "react-native-skeleton-placeholder": "^5.0.0", "react-native-stream": "0.1.9", "react-native-svg": "9.6.4", "react-native-svg-transformer": "^1.0.0", "react-native-tab-view": "^3.1.1", "react-native-toast-message": "^2.1.1", "react-native-toast-notifications": "^3.2.3", "react-native-web": "~0.17.5", "react-native-webview": "^11.26.1", "react-redux": "^7.2.6", "readable-stream": "^1.0.33", "redux": "^4.1.2", "redux-persist": "^6.0.0", "rn-fetch-blob": "^0.12.0", "rn-nodeify": "^10.3.0", "stream-browserify": "^1.0.0", "traverse": "^0.6.6", "url": "~0.10.1", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "~7.9.0", "@babel/preset-env": "^7.16.7", "@babel/preset-flow": "^7.16.7", "@babel/preset-react": "^7.16.7", "@types/jest": "^26.0.20", "@types/node": "14.14.22", "@types/react": "~16.9.35", "@types/react-native": "~0.63.2", "@typescript-eslint/eslint-plugin": "^4.0.1", "app-root-path": "3.0.0", "babel-plugin-module-resolver": "^4.1.0", "chai": "^4.2.0", "chokidar": "3.5.1", "commitizen": "4.2.3", "cz-conventional-changelog": "^3.2.0", "dotenv": "8.2.0", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.15.6", "eslint": "^7.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-functional": "^3.0.2", "eslint-plugin-import": "^2.22.0", "eslint-plugin-react": "7.22.0", "eslint-plugin-react-native": "3.10.0", "gh-pages": "^3.2.3", "husky": "4.3.8", "jest": "26.6.3", "lint-staged": "10.5.3", "metro-react-native-babel-preset": "^0.66.2", "platform-detect": "3.0.1", "prettier": "2.2.1", "react-native-codegen": "^0.0.12", "react-test-renderer": "17.0.1", "ts-node": "^10.4.0", "typescript": "~4.0.0"}, "jest": {"preset": "react-native", "transform": {"\\.js$": "<rootDir>/node_modules/babel-jest"}}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": "eslint --fix --ext '.ts,.tsx,.js,.jsx' -c .eslintrc.json"}, "react-native": {"crypto": "react-native-crypto", "http": "@tradle/react-native-http", "https": "https-browserify", "os": "react-native-os", "_stream_transform": "readable-stream/transform", "_stream_readable": "readable-stream/readable", "_stream_writable": "readable-stream/writable", "_stream_duplex": "readable-stream/duplex", "_stream_passthrough": "readable-stream/passthrough", "stream": "stream-browserify"}, "browser": {"crypto": "react-native-crypto", "http": "@tradle/react-native-http", "https": "https-browserify", "os": "react-native-os", "_stream_transform": "readable-stream/transform", "_stream_readable": "readable-stream/readable", "_stream_writable": "readable-stream/writable", "_stream_duplex": "readable-stream/duplex", "_stream_passthrough": "readable-stream/passthrough", "stream": "stream-browserify"}}