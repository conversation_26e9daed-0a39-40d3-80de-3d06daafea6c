import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  TextInput,
} from 'react-native';
import {useMutation, useLazyQuery} from '@apollo/client';
import {useStripe} from '@stripe/stripe-react-native';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  CONFIRM_CARD_PAYMENT,
  APPLY_AUTHENTICATOR_CODE,
  CONFRIM_PAYMENT,
  CREATE_PAYMENT_INTENT,
  APPLY_COUPON_CODE,
  CONFIRM_PAYMENT_FOR_FULL_DISCOUNT,
} from '../../apollo/mutations';
import {GET_PAYMENT_DETAILS} from '../../apollo/queries';
import {authenticationTypeScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {icons, screens, persistent} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Icon from '../../components/Icon';
import Layout from '../../layout';
import ActivityIndicator from '../../components/ActivityIndicator';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';

const POPUP_INITIAL_STATE = {
  state: false,
  text: '',
};

const PAYMENT_STATE = {
  isCouponApplied: false,
  discountId: null,
  client_secret: null,
  data: {
    total: null,
    subtotal: null,
    tax: null,
    discountAmount: null,
    totalWithoutDiscount: null,
    isDiscounted: false,
    isFullDiscount: false,
  },
};

const CheckOut = ({route, navigation}) => {
  const otherParams = route?.params?.otherParams;
  const params = route?.params?.params;
  const authId = route?.params?.authId;
  const expressCheckout = route?.params?.expressCheckout;
  const [getNFT] = React.useState(route?.params?.getNFTDetails ?? null);

  const authenticationType = React.useMemo(
    () => route?.params?.authenticationType,
    [route?.params?.authenticationType],
  );

  const [paymentState, setPaymentState] = React.useState({
    ...PAYMENT_STATE,
    client_secret: params?.client_secret,
    data: {
      ...PAYMENT_STATE.data,
      subtotal: params?.subtotal,
      total: params?.total,
      tax: params?.tax,
    },
  });

  const [globalDisable, setGlobalDisable] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authCode, setAuthCode] = useState(null);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [applyAuthenticatorCode] = useMutation(APPLY_AUTHENTICATOR_CODE);
  const [confirmCardPayment] = useMutation(CONFIRM_CARD_PAYMENT);
  const [confirmPayment] = useMutation(CONFRIM_PAYMENT);
  const [confirmPaymentForFullDiscount] = useMutation(
    CONFIRM_PAYMENT_FOR_FULL_DISCOUNT,
  );
  const [createPaymentIntent, {loading: paymentIntentLoading}] = useMutation(
    CREATE_PAYMENT_INTENT,
  );

  const {confirmPayment: confirmStripePayment} = useStripe();

  const handleCardPayment = async () => {
    setIsLoading(true);
    if (authCode) {
      try {
        const authCodeResponse = await applyAuthenticatorCode({
          variables: {
            code: authCode,
            queueId: authId,
          },
        });
        const isSuccess =
          authCodeResponse?.data?.applyAuthenticatorCode?.success;
        const code = authCodeResponse?.data?.applyAuthenticatorCode?.code;
        const message = authCodeResponse?.data?.applyAuthenticatorCode?.message;

        if (!isSuccess) {
          setIsLoading(false);
          if (code?.length) {
            setPopup({
              state: true,
              text: message ?? locale.SomethingWentWrongPleaseTryAgain,
            });
            return;
          } else {
            throw new Error();
          }
        }
      } catch (e) {
        setIsLoading(false);
        setPopup({state: true, text: locale.SomethingWentWrongPleaseTryAgain});
        return;
      }
    }

    if (paymentState?.data?.isFullDiscount) {
      setIsLoading(true);
      try {
        const response = await confirmPaymentForFullDiscount({
          variables: {
            authenticationId: parseInt(authId, 10),
            discountId: paymentState.discountId,
            ...(getNFT == null
              ? {}
              : {
                  isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true,
                }),
          },
        });
        const isSuccessful =
          response?.data?.confirmPaymentForFullDiscount?.success;
        const code = response?.data?.confirmPaymentForFullDiscount?.code;
        const message = response?.data?.confirmPaymentForFullDiscount?.message;

        const authKey =
          response?.data?.confirmPaymentForFullDiscount?.data?.key;

        setIsLoading(false);
        if (isSuccessful) {
          navigation.navigate(screens.AUTH_SUCCESSFUL, {
            params: authKey,
            authenticationType: 2, // Just for confirmation messages
          });
        } else {
          if (code === 'PAYMENT_ALREADY_DONE') {
            navigation.navigate(screens.AUTH_SUCCESSFUL, {
              params: authKey,
            });
          } else {
            setPopup({
              state: true,
              text: message ?? locale.SomethingWentWrongPleaseTryAgain,
            });
          }
        }
      } catch (e) {
        setPopup({state: true, text: locale.SomethingWentWrongPleaseTryAgain});
        setIsLoading(false);
      }
    } else {
      if (expressCheckout) {
        try {
          const {
            paymentIntent: responsePaymentIntent,
            error: confirmError,
          } = await confirmStripePayment(paymentState.client_secret, {
            type: 'Card',
            billingDetails: otherParams,
          });

          if (confirmError) {
            setIsLoading(false);
            setPopup({
              state: true,
              text:
                confirmError.message ?? locale.SomethingWentWrongPleaseTryAgain,
            });
            return;
          }

          const res = await confirmPayment({
            variables: {
              paymentIntent: responsePaymentIntent?.id,
              ...(getNFT == null
                ? {}
                : {
                    isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true,
                  }),
            },
          });

          const isSuccessful = res?.data?.confirmPayment?.success;
          const code = res?.data?.confirmPayment?.code;
          const message = res?.data?.confirmPayment?.message;

          if (isSuccessful) {
            setIsLoading(false);
            navigation.navigate(screens.AUTH_SUCCESSFUL, {
              params: res?.data?.confirmPayment?.data?.key,
            });
          } else {
            if (code?.length) {
              setPopup({
                state: true,
                text: message ?? locale.SomethingWentWrongPleaseTryAgain,
              });
            } else {
              throw new Error();
            }
          }
        } catch (e) {
          setPopup({
            state: true,
            text: locale.SomethingWentWrongPleaseTryAgain,
          });
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(true);
        try {
          const response = await confirmCardPayment({
            variables: {
              cardId: otherParams?.id,
              authenticationId: parseInt(authId, 10),
              ...(paymentState.isCouponApplied
                ? {discountId: paymentState.discountId}
                : {}),
              ...(getNFT == null
                ? {}
                : {
                    isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true,
                  }),
            },
          });
          const isSuccessful = response?.data?.confirmCardPayment?.success;
          const code = response?.data?.confirmCardPayment?.code;
          const message = response?.data?.confirmCardPayment?.message;

          setIsLoading(false);
          if (isSuccessful) {
            navigation.navigate(screens.AUTH_SUCCESSFUL, {
              params: response?.data?.confirmCardPayment?.data?.key,
              authenticationType: 2, // Just for confirmation messages
            });
          } else {
            if (code === 'PAYMENT_ALREADY_DONE') {
              navigation.navigate(screens.AUTH_SUCCESSFUL, {
                params: response?.data?.confirmCardPayment?.data?.key,
              });
            } else {
              setPopup({
                state: true,
                text: message ?? locale.SomethingWentWrongPleaseTryAgain,
              });
            }
          }
        } catch (e) {
          setPopup({
            state: true,
            text: locale.SomethingWentWrongPleaseTryAgain,
          });
        } finally {
          setIsLoading(false);
        }
      }
    }
  };

  const mapTitle = React.useMemo(() => {
    if (getNFT == null) {
      switch (authenticationType) {
        case 3:
          return 'Expert Certified NFT';
        case 5:
          return 'Expert Feedback';
        default:
          return 'Expert Certified';
      }
    } else {
      return 'Get NFT';
    }
  }, [authenticationType, getNFT]);
  const isDisabled = globalDisable || isLoading || paymentIntentLoading;

  return (
    <Layout style={styles.container}>
      <Text style={styles.labelTitle}>{locale.CheckOut}</Text>
      <View style={styles.paymentType}>
        <Text style={styles.label}>
          {authenticationTypeScreen.PAYMENT_METHOD}
        </Text>
        <View>
          <Text style={styles.label}>
            {otherParams?.brand} *
            {otherParams?.number?.slice(-7, otherParams?.number?.length)}
            {otherParams?.last4}
          </Text>
          <TouchableOpacity
            disabled={isDisabled}
            style={styles.changeButton}
            onPress={() => navigation.navigate(screens.PAYMENT_METHOD)}>
            <Text style={styles.changeButtonText}>
              {authenticationTypeScreen.CHANGE}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.taxSection}>
        <View style={styles.desc}>
          <Text style={styles.label}>{mapTitle} (1x)</Text>
          <Text style={styles.label}>
            ${(paymentState?.data?.subtotal * 0.01).toFixed(2)}
          </Text>
        </View>
        <View style={styles.desc}>
          <Text style={styles.label}>{authenticationTypeScreen?.TAX}</Text>
          <Text style={styles.label}>
            ${(paymentState?.data?.tax * 0.01).toFixed(2)}
          </Text>
        </View>
        {paymentState.isCouponApplied ? (
          <View style={styles.desc}>
            <Text style={[styles.totalLabel]}>{locale.Discount}</Text>
            <Text style={[styles.totalLabel, {color: colors.green}]}>
              - ${(paymentState?.data?.discountAmount * 0.01).toFixed(2)}
            </Text>
          </View>
        ) : null}
        <View style={styles.desc}>
          <Text style={[styles.totalLabel]}>
            {authenticationTypeScreen?.TOTAL}
          </Text>
          <Text style={styles.totalLabel}>
            ${(paymentState?.data?.total * 0.01).toFixed(2)}
          </Text>
        </View>
      </View>
      {getNFT == null ? (
        <View>
          <Text style={styles.label}>Authenticator code</Text>
          <TextInput
            style={styles.inputStyles}
            placeholderTextColor={colors.grey}
            selectionColor={colors.grey}
            value={authCode}
            onChangeText={setAuthCode}
          />
        </View>
      ) : null}

      <ApplyCoupon
        setGlobalDisable={setGlobalDisable}
        setPaymentState={setPaymentState}
        queueId={authId}
        createPaymentIntent={createPaymentIntent}
        isOneTimePayment={expressCheckout}
        isUpgradeFromExpertCertifiedToExpertCertifiedNFT={!(getNFT == null)}
      />
      <TouchableOpacity
        style={styles.checkoutButton}
        onPress={handleCardPayment}
        disabled={isDisabled}>
        {isLoading ? (
          <ActivityIndicator color={colors.white} />
        ) : (
          <>
            <Icon name={icons.LOCK} size={fontSize.xl} tint={colors.white} />
            <Text style={styles.checkoutButtonText}>
              {authenticationTypeScreen.PAY_NOW}
            </Text>
          </>
        )}
      </TouchableOpacity>
      <InfoModal
        setVisible={() => setPopup(POPUP_INITIAL_STATE)}
        popUp={{
          state: popup.state,
          isError: true,
          data: {title: locale.Error, description: popup.text},
        }}
      />
    </Layout>
  );
};

function ApplyCoupon({
  setGlobalDisable = () => null,
  queueId = null,
  setPaymentState = () => null,
  createPaymentIntent = () => null,
  isOneTimePayment = true,
  isUpgradeFromExpertCertifiedToExpertCertifiedNFT = false,
}) {
  const [couponText, setCouponText] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [applyCouponCode] = useMutation(APPLY_COUPON_CODE);
  const [getDiscountedPaymentDetail] = useLazyQuery(GET_PAYMENT_DETAILS);

  const handleApplyCouponCode = React.useCallback(
    async couponCode => {
      setIsLoading(true);
      setGlobalDisable(true);
      try {
        const {data} = await applyCouponCode({
          variables: {
            code:
              couponText?.length > 0 ? couponText : couponCode ?? couponText,
            queueId,
            ...(isUpgradeFromExpertCertifiedToExpertCertifiedNFT
              ? {isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true}
              : {}),
          },
        });
        const isCouponApplySuccessful = data?.applyCouponCode?.success;
        const isCouponApplyCode = data?.applyCouponCode?.code;
        const isCouponApplyMessage = data?.applyCouponCode?.message;
        const discountId = data?.applyCouponCode?.data?.discountId;
        const isFullDiscount = data?.applyCouponCode?.data?.isFullDiscount;

        if (!isCouponApplySuccessful) {
          if (isCouponApplyCode?.length > 0) {
            setPopup(previousState => ({
              ...previousState,
              state: true,
              text: isCouponApplyMessage,
            }));
            if (
              ['COUPON_NOT_FOUND', 'COUPON_ALREADY_USED'].includes(
                isCouponApplyCode,
              )
            ) {
              await AsyncStorage.removeItem(persistent.COUPON_CODE);
            }
          } else {
            throw new Error();
          }
        } else {
          if (isOneTimePayment && !isFullDiscount) {
            const {
              data: discountedPaymentIntentData,
            } = await createPaymentIntent({
              variables: {
                authenticationId: +queueId,
                discountId: +discountId,
                ...(isUpgradeFromExpertCertifiedToExpertCertifiedNFT
                  ? {isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true}
                  : {}),
              },
            });

            const discountedSuccessful =
              discountedPaymentIntentData?.createPaymentIntent?.success;
            const discountedCode =
              discountedPaymentIntentData?.createPaymentIntent?.code;
            const discountedMessage =
              discountedPaymentIntentData?.createPaymentIntent?.message;

            if (!discountedSuccessful) {
              if (discountedCode?.length > 0) {
                setPopup(previousState => ({
                  ...previousState,
                  state: true,
                  text: discountedMessage,
                }));
              } else {
                throw new Error();
              }
            } else {
              const discountedData =
                discountedPaymentIntentData?.createPaymentIntent?.data;

              setPaymentState(previousState => ({
                ...previousState,
                isCouponApplied: true,
                discountId,
                client_secret: discountedData?.client_secret,
                data: {
                  ...previousState.data,
                  total: discountedData?.total,
                  subtotal: discountedData?.subtotal,
                  tax: discountedData?.tax,
                  isDiscounted: discountedData?.isDiscounted,
                  discountAmount: discountedData?.discountAmount,
                  totalWithoutDiscount: discountedData?.totalWithoutDiscount,
                  isFullDiscount: discountedData?.isFullDiscount,
                },
              }));
            }
          } else {
            const {
              data: discountedPaymentDetail,
            } = await getDiscountedPaymentDetail({
              variables: {
                authenticationId: +queueId,
                discountId: +discountId,
                ...(isUpgradeFromExpertCertifiedToExpertCertifiedNFT
                  ? {isUpgradeFromExpertCertifiedToExpertCertifiedNFT: true}
                  : {}),
              },
            });

            const discountedSuccessful =
              discountedPaymentDetail?.getPaymentDetails?.success;
            const discountedCode =
              discountedPaymentDetail?.getPaymentDetails?.code;
            const discountedMessage =
              discountedPaymentDetail?.getPaymentDetails?.message;

            if (!discountedSuccessful) {
              if (discountedCode?.length > 0) {
                setPopup(previousState => ({
                  ...previousState,
                  state: true,
                  text: discountedMessage,
                }));
              } else {
                throw new Error();
              }
            } else {
              const discountedData =
                discountedPaymentDetail?.getPaymentDetails?.data;

              setPaymentState(previousState => ({
                ...previousState,
                isCouponApplied: true,
                discountId,
                client_secret: discountedData?.client_secret,
                data: {
                  ...previousState.data,
                  total: discountedData?.total,
                  subtotal: discountedData?.subtotal,
                  tax: discountedData?.tax,
                  isDiscounted: discountedData?.isDiscounted,
                  discountAmount: discountedData?.discountAmount,
                  totalWithoutDiscount: discountedData?.totalWithoutDiscount,
                  isFullDiscount: discountedData?.isFullDiscount,
                },
              }));
            }
          }
        }
      } catch (_) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          text: locale.SomethingWentWrongPleaseTryAgain,
        }));
      } finally {
        setGlobalDisable(false);
        setIsLoading(false);
      }
    },
    [
      couponText,
      queueId,
      setGlobalDisable,
      setPaymentState,
      createPaymentIntent,
      applyCouponCode,
      isOneTimePayment,
      getDiscountedPaymentDetail,
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT,
    ],
  );

  useFocusEffect(
    React.useCallback(function () {
      (async () => {
        try {
          const couponCode = await AsyncStorage.getItem(persistent.COUPON_CODE);
          if (couponCode?.length > 0) {
            handleApplyCouponCode(couponCode);
          }
        } catch (_) {}
      })();
    }, []),
  );

  return (
    <View>
      <Text style={styles.label}>Coupon:</Text>
      <View style={{flexDirection: 'row', flex: 1}}>
        <TextInput
          style={[
            styles.inputStyles,
            {textTransform: 'uppercase', flex: 1, marginRight: 10},
          ]}
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
          value={couponText}
          onChangeText={val => setCouponText(val)}
          autoCapitalize="characters"
        />
        <Button
          text={locale.Apply}
          style={{height: 50, borderRadius: 4}}
          isLoading={isLoading}
          disabled={isLoading || couponText?.length === 0}
          textStyle={{fontSize: 16, fontFamily: 'Inter-SemiBold'}}
          onPress={handleApplyCouponCode}
        />
      </View>
      <InfoModal
        setVisible={() => setPopup(POPUP_INITIAL_STATE)}
        popUp={{
          state: popup.state,
          isError: true,
          data: {title: locale.ApplyCouponError, description: popup.text},
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
    marginVertical: 30,
  },
  emptyContainer: {
    flex: 1,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentType: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
    flexWrap: 'wrap',
  },
  inputStyles: {
    height: 50,
    borderWidth: 1,
    color: colors.black,
    borderRadius: 4,
    width: '100%',
    borderColor: colors.grey,
    marginBottom: spacings.lg,
    paddingLeft: spacings.md,
  },
  desc: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
    flexWrap: 'wrap',
  },
  taxSection: {
    borderTopWidth: 3,
    borderBottomWidth: 3,
    borderBottomColor: colors.lightGrey,
    borderTopColor: colors.lightGrey,
    marginVertical: spacings.lg,
    paddingVertical: spacings.lg,
  },
  checkoutButton: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.xxl,
    borderRadius: 4,
    flexDirection: 'row',
  },
  checkoutButtonText: {
    fontSize: fontSize.lg,
    color: colors.white,
    marginLeft: spacings.sm,
    fontWeight: 'bold',
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.black,
    marginBottom: spacings.sm,
    fontFamily: 'Inter-Regular',
  },
  totalLabel: {
    fontWeight: 'bold',
    fontSize: fontSize.lg,
    color: colors.black,
    marginBottom: spacings.sm,
  },
  changeButton: {
    alignItems: 'flex-end',
    paddingBottom: spacings.md,
  },
  changeButtonText: {
    fontWeight: 'bold',
    color: colors.primary,
    fontSize: fontSize.lg,
  },
  errorStyles: {
    color: colors.warning,
    fontSize: fontSize.sm,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  labelTitle: {
    fontSize: 20,
    color: colors.black,
    marginBottom: 10,
    fontFamily: 'Gugi-Regular',
  },
});

export default CheckOut;
