import React from 'react';
import {StyleSheet} from 'react-native';

import {spacings} from '../../assets/spacing';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';

export default styles = StyleSheet.create({
  optionWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacings.sm,
    paddingVertical: spacings.md,
  },
  listRow: {
    fontSize: 18,
    fontFamily: 'Inter-Medium',
    color: colors.black,
  },
  modalLabel: {
    fontSize: fontSize.md,
    color: colors.black,
  },
});
