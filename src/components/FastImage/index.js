import React from 'react';
import Image from 'react-native-fast-image';
import {Blurhash} from 'react-native-blurhash';
import {StyleSheet, View} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import {decode} from 'base-64';

const INITIAL_OPACITY_VALUE = 0;

const AnimatedBlurHash = Animated.createAnimatedComponent(Blurhash);
const AnimatedFastImage = Animated.createAnimatedComponent(Image);

const DEFAULT_BLUR_HASH = 'LDSF;P-=~oDhIBtRxvMx?aM{M{%g';

const ALLOWED_EXTENSIONS = ['png', 'jpg', 'jpeg'];

export default function FastImage({
  source,
  resizeMode = 'cover',
  style: _style = {},
  enableMountOpacityAnimation = true,
  containerStyle = {},
  blurHash = DEFAULT_BLUR_HASH,
  enableBlurHash = true,
  onLoad = () => null,
}) {
  const opacity = useSharedValue(1);
  const blurHashOpacity = useSharedValue(1);

  const transformOpacity = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));
  const blurHashTransformOpacity = useAnimatedStyle(() => ({
    opacity: blurHashOpacity.value,
  }));

  const handleOnLoad = React.useCallback(
    evt => {
      if (enableMountOpacityAnimation) {
        blurHashOpacity.value = 0;
        opacity.value = withTiming(1, {duration: 300, easing: Easing.linear});
      }
    },
    [opacity.value, blurHashOpacity.value, enableMountOpacityAnimation],
  );

  const handleOnProgress = React.useCallback(() => {
    if (enableMountOpacityAnimation) {
      blurHashOpacity.value = 1;
      opacity.value = INITIAL_OPACITY_VALUE;
    }
  }, [opacity.value, blurHashOpacity.value, enableMountOpacityAnimation]);

  const style = React.useMemo(() => StyleSheet.flatten(_style), [_style]);

  const commonStyle = React.useMemo(
    () => ({
      width: style?.width ?? '100%',
      ...(style?.height ? {height: style?.height} : {}),
      ...(style?.borderRadius ? {borderRadius: style?.borderRadius} : {}),
      overflow: 'hidden',
    }),
    [style],
  );

  const decodeBlurHash = React.useMemo(() => {
    const uri = source?.uri;
    const extension = ALLOWED_EXTENSIONS.find(item => uri?.endsWith(item));

    try {
      if (uri?.length) {
        const matchBlurHash = decodeURIComponent(uri)?.match(
          new RegExp(`@blurHash=([\\s\\S]*?)\\.${extension}`, 'g'),
        );
        if (matchBlurHash?.length > 0) {
          const stripBlurHash = matchBlurHash?.[0]?.slice(
            10,
            -(1 + extension?.length),
          );
          const decodedBlurHash = decode(stripBlurHash);
          if (Blurhash.isBlurhashValid(decodedBlurHash)?.isValid) {
            return decodedBlurHash;
          }
        }
      }
    } catch (_) {}

    return blurHash;
  }, [source?.uri, blurHash]);

  return (
    <View style={[commonStyle, StyleSheet.flatten(containerStyle)]}>
      {enableBlurHash ? (
        <AnimatedBlurHash
          blurhash={decodeBlurHash}
          style={[
            blurHashTransformOpacity,
            commonStyle,
            {
              position: 'absolute',
            },
          ]}
          resizeMode={resizeMode}
        />
      ) : null}
      <AnimatedFastImage
        source={source}
        style={[transformOpacity, style]}
        resizeMode={resizeMode}
        onLoadEnd={handleOnLoad}
        onProgress={handleOnProgress}
        onLoad={onLoad}
      />
    </View>
  );
}
