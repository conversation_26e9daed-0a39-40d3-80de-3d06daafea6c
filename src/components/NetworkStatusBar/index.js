import React from 'react';

import {View, Text} from 'react-native';

import Icon from '../Icon';
import colors from '../../assets/colors';
import {useNetworkInfo} from '../../tools/hooks';
import locale from '../../assets/locale.json';

export default function NetworkStatusBar() {
  const {networkStatus} = useNetworkInfo();

  return !networkStatus.isConnected ? (
    <View
      style={{
        minHeight: 25,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        backgroundColor: colors.black,
      }}>
      <Icon name="noConnection" tint={colors.white} size={18} />
      <Text
        style={{
          fontSize: 14,
          fontFamily: 'Inter-Bold',
          color: colors.white,
          textAlign: 'center',
          marginLeft: 10,
        }}>
        {locale.NoConnectionAvailable}
      </Text>
    </View>
  ) : null;
}
