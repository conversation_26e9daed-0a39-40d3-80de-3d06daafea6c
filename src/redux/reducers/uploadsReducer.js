import actionTypes from '../actionTypes';

const initialState = {
  uploads: {
    show_score: true,
    front_image: null,
    back_image: null,
    front_tag_image: null,
    back_tag_image: null,
    closeup_front_image: null,
    closeup_back_image: null,
    copyright_image: null,
    arm_hem_stitching_image: null,
    lower_hem_stitching_image: null,
    extra: null,
    front_image_upload_method: null,
    back_image_upload_method: null,
    front_tag_image_upload_method: null,
    back_tag_image_upload_method: null,
    closeup_front_image_upload_method: null,
    closeup_back_image_upload_method: null,
    copyright_image_upload_method: null,
    arm_hem_stitching_image_upload_method: null,
    lower_hem_stitching_image_upload_method: null,
    extra_upload_method: null,
  },
  profilePhoto: null,
  imageUploadMethod: null,
};

const uploadReducer = (state = initialState, {type, payload}) => {
  switch (type) {
    case actionTypes.ADD_PHOTO:
      return {
        ...state,
        uploads: [...state.uploads, payload],
      };
    case actionTypes.REMOVE_PHOTO:
      return {
        ...state,
        uploads: [...state.uploads.filter(item => item.name !== payload)],
      };
    case actionTypes.CHANGE_DP:
      return {
        ...state,
        profilePhoto: payload,
      };
    case actionTypes.FRONT_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          front_image: payload.image,
          front_image_upload_method:
            state?.imageUploadMethod?.name === 'front_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.BACK_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          back_image: payload.image,
          back_image_upload_method:
            state?.imageUploadMethod?.name === 'back_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.FRONT_TAG_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          front_tag_image: payload.image,
          front_tag_image_upload_method:
            state?.imageUploadMethod?.name === 'front_tag_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.BACK_TAG_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          back_tag_image: payload.image,
          back_tag_image_upload_method:
            state?.imageUploadMethod?.name === 'back_tag_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.CLOSEUP_FRONT_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          closeup_front_image: payload.image,
          closeup_front_image_upload_method:
            state?.imageUploadMethod?.name === 'closeup_front_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.CLOSEUP_BACK_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          closeup_back_image: payload.image,
          closeup_back_image_upload_method:
            state?.imageUploadMethod?.name === 'closeup_back_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.COPYRIGHT_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          copyright_image: payload.image,
          copyright_image_upload_method:
            state?.imageUploadMethod?.name === 'copyright_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.ARM_HEM_STITCHING_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          arm_hem_stitching_image: payload.image,
          arm_hem_stitching_image_upload_method:
            state?.imageUploadMethod?.name === 'arm_hem_stitching_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.LOWER_HEM_STITCHING_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          lower_hem_stitching_image: payload.image,
          lower_hem_stitching_image_upload_method:
            state?.imageUploadMethod?.name === 'lower_hem_stitching_image'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.EXTRA_IMAGE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          extra: payload.image,
          extra_upload_method:
            state?.imageUploadMethod?.name === 'extra'
              ? state?.imageUploadMethod?.value
              : null,
        },
      };
    case actionTypes.UPLOAD_METHOD:
      return {
        ...state,
        imageUploadMethod: payload,
      };
    case actionTypes.SHOW_SCORE:
      return {
        ...state,
        uploads: {
          ...state.uploads,
          show_score: payload,
        },
      };
    case actionTypes.CLEAR_UPLOADS:
      return initialState;
    default:
      return state;
  }
};

export default uploadReducer;
