import {StyleSheet} from 'react-native';

import {spacings} from '../../assets/spacing';
import colors from '../../assets/colors';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  card: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageStyles: {
    height: 50,
    width: 50,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: colors.lightGrey,
  },
  voteSection: {
    fontSize: 13,
    backgroundColor: '#F2F2F2',
    padding: 5,
    paddingHorizontal: 10,
    borderRadius: 4,
    fontFamily: 'Inter-Medium',
  },
  name: {
    marginLeft: spacings.md,
    fontSize: 16,
    color: colors.black,
    fontFamily: 'Inter-SemiBold',
  },
});

export default styles;
