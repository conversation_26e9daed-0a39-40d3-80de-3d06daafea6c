import React, {useEffect, useCallback, useState, useRef} from 'react';
import {Text, View, TouchableOpacity, ActivityIndicator} from 'react-native';
import {Formik, Field} from 'formik';
import * as yup from 'yup';

import CustomInput from '../../components/AuthInput';
import {screens, forgotPasswordScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {styles} from './style';
import {FORGOT_PASSWORD} from '../../tools/api';
import Layout from '../../layout';

const forgotPasswordSchema = yup.object().shape({
  email: yup.string().email('Invalid email').required('The email is required'),
});

const Index = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setError(null);
      isMounted.current = false;
    };
  }, []);

  const handleEmailConfirmation = useCallback(
    async data => {
      setIsLoading(true);
      try {
        const response = await FORGOT_PASSWORD(data);
        const res = response.data;
        if (res.success === true) {
          setIsLoading(false);
          navigation.navigate(screens.VALIDATE_CODE, {params: data});
        } else {
          setIsLoading(false);
        }
      } catch (e) {
        setError(e.response.data.message);
        setIsLoading(false);
      }
    },
    [navigation],
  );

  return (
    <Layout contentContainerStyle={styles.container}>
      <Formik
        validationSchema={forgotPasswordSchema}
        initialValues={{
          email: '',
        }}
        onSubmit={async values => {
          if (isMounted.current === true) {
            handleEmailConfirmation(values);
          }
        }}>
        {({handleSubmit}) => (
          <>
            <View style={styles.loginContainer}>
              <View style={styles.titleWrapper}>
                <Text style={styles.titleStyle}>
                  {forgotPasswordScreen.SCREEN1_TITLE}
                </Text>
              </View>
              <View>
                <Text style={{color: colors.black}}>
                  {forgotPasswordScreen.SUBTITLE}
                </Text>
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {forgotPasswordScreen.EMAIL_LABEL}
                </Text>
                <Field
                  style={styles.inputField}
                  component={CustomInput}
                  name="email"
                  placeholder="<EMAIL>"
                  keyboardType="email-address"
                />
              </View>
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}>
                {isLoading ? (
                  <ActivityIndicator color={colors.white} />
                ) : (
                  <Text style={styles.buttonText}>
                    {forgotPasswordScreen.BUTTON_LABEL}
                  </Text>
                )}
              </TouchableOpacity>
              {error ? (
                <View style={styles.errorWrapper}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : null}
            </View>
          </>
        )}
      </Formik>
    </Layout>
  );
};

export default Index;
