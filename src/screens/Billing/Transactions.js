import React, {useState, useCallback} from 'react';
import {StyleSheet, FlatList, RefreshControl, Text, View} from 'react-native';
import {useQuery} from '@apollo/client';
import dayjs from 'dayjs';
import {useFocusEffect} from '@react-navigation/native';

import TransactionsLoader from '../../components/Loaders/TransactionsLoader';
import {GET_PAYMENT_TRANSACTIONS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {billingScreen} from '../../assets/strings';

const LIMIT = 100;

const Transactions = () => {
  const scrollRef = React.useRef();

  const [refreshing, setRefreshing] = useState(false);

  const {data, refetch, loading, error} = useQuery(GET_PAYMENT_TRANSACTIONS, {
    variables: {
      limit: LIMIT,
    },
  });

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (_) {}
    }, [refetch]),
  );

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        // cursorRef.current = [];
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  const renderItem = ({item}) => {
    return (
      <View style={styles.card}>
        <View style={styles.leftSection}>
          <Text numberOfLines={3} style={styles.cardTitle}>
            {item?.authentication_details?.title}
          </Text>
          <Text style={styles.date}>
            {dayjs(item?.authentication_details?.created_at).format(
              'DD/MM/YYYY',
            )}
          </Text>
        </View>
        <View style={styles.rightSection}>
          <Text style={styles.cardTitle}>
            {billingScreen.CURRENCY}
            {!isNaN(item?.total + item?.tax)
              ? (item?.total + item?.tax)?.toFixed?.(2)
              : ''}
          </Text>
          <Text
            style={[
              styles.desc,
              {
                color:
                  item?.payment_status === 'COMPLETE'
                    ? colors.green
                    : colors.darkGrey,
              },
            ]}>
            {item?.payment_status === 'COMPLETE' ? 'Successful' : 'Failed'}
          </Text>
        </View>
      </View>
    );
  };

  if (loading || error) {
    return <TransactionsLoader />;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={data?.getPaymentTransactions?.data?.edges}
        renderItem={renderItem}
        keyExtractor={item => item?.id?.toString?.()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={
          data?.getPaymentTransactions?.data?.edges?.length == 0
            ? {flex: 1}
            : {}
        }
        ListEmptyComponent={
          !loading ? (
            <View style={[styles.emptyContainer]}>
              <Text style={styles.emptyContainerText}>
                {billingScreen.NO_TRANSACTIONS_YET}
              </Text>
            </View>
          ) : null
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            progressBackgroundColor={colors.lightGrey}
            colors={[colors.primary, colors.black]}
            tintColor={colors.primary}
            onRefresh={onRefresh}
          />
        }
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
        initialNumToRender={20}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyContainerText: {
    fontSize: fontSize.md,
    color: colors.black,
  },
  card: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.lg,
    borderBottomWidth: 2,
    borderBottomColor: colors.lightGrey,
  },
  cardTitle: {
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    color: colors.black,
    marginBottom: spacings.md,
  },
  rightSection: {
    flex: 3 / 10,
    alignItems: 'flex-end',
  },
  leftSection: {
    flex: 7 / 10,
    alignItems: 'flex-start',
  },
  desc: {
    backgroundColor: colors.lightGrey,
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    fontSize: fontSize.md,
  },
  date: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
  },
  errorContainerText: {
    fontSize: fontSize.md,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});

export default Transactions;
