import React, {useCallback} from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {styles} from './styles';
import {screens} from '../../assets/strings';
import Tooltip from '../../components/Tooltip';
import locale from '../../assets/locale.json';
import {useUser} from '../../tools/hooks';

const StatsCard = ({data}) => {
  const navigation = useNavigation();

  const {user: me} = useUser();
  const authenticationData = data?.getPost?.data;

  const isAuthenticationPending =
    data?.getPost?.data?.authentication_status === 'PENDING';

  const handleDecision = useCallback(() => {
    switch (
      data?.getPost.data?.authentication_result?.community_result?.decision
    ) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
        return 'UNDETERMINED';
      default:
        return 'PENDING';
    }
  }, [data?.getPost.data?.authentication_result?.community_result?.decision]);

  const handleColor = useCallback(() => {
    switch (
      data?.getPost.data?.authentication_result?.community_result?.decision
    ) {
      case 'REAL':
        return colors.secondary;
      case 'FAKE':
        return colors.warning;
      case 'UNDECIDED':
        return colors.black;
      case 'UNDETERMINED':
        return colors.amber;
      default:
        return colors.black;
    }
  }, [data?.getPost.data?.authentication_result?.community_result?.decision]);

  const handleUpgradeToExpertAuthentication = () => {
    navigation.navigate(screens.AUTHENTICATE, {
      isUpgradeToExpertAuthentication: true,
      upgradeData: {
        authenticationId: authenticationData?.queue_id,
        postId: authenticationData?.id,
      },
    });
  };

  const canUpgradeToExpertVerification = React.useMemo(
    () => authenticationData?.user?.id == me?.id,
    [authenticationData],
  );

  const isVotingClosed = React.useMemo(
    () => authenticationData?.is_voting_closed,
    [authenticationData?.is_voting_closed],
  );

  const isRevealing = React.useMemo(
    () => handleDecision() !== 'PENDING' && !isVotingClosed,
    [handleDecision, isVotingClosed],
  );

  return (
    <View style={styles.statsContainer}>
      <View>
        <Text style={styles.resultsHeading}>Results</Text>
      </View>
      {data?.getPost?.data?.authentication_result?.decision !== 'FAKE' ? (
        <View style={styles.appraisalWrapper}>
          <View style={styles.appraisalLabelWrapper}>
            <Icon
              name="price_tag"
              size={20}
              tint={colors.black}
              style={{marginRight: 10}}
            />
            <Text style={styles.appraisalLabel}>Average appraisal</Text>
            <Tooltip
              size={20}
              style={{marginLeft: 10}}
              enableSheetContent
              title={locale.AverageAppraisal}
              description="With every submission our users will input an estimate of a t-shirt’s value, and our platform will calculate an average figure in USD. Our system and administrators also actively monitor submissions to suppress spam appraisals intended to alter the results."
            />
          </View>
          <Text style={styles.appraisalLabel}>
            {data?.getPost?.data?.authentication_result?.decision !==
              'UNDECIDED' && !(data?.getPost?.data?.appraisal_value === null)
              ? `$ ${data?.getPost?.data?.appraisal_value}`
              : locale.Pending}
          </Text>
        </View>
      ) : null}
      <View
        style={[
          styles.appraisalWrapper,
          {justifyContent: 'space-between', alignItems: 'center'},
        ]}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Icon
            name="people_outline"
            size={20}
            tint={colors.black}
            style={{marginRight: 10}}
          />
          <Text
            style={[
              styles.appraisalLabel,
              {
                color: colors.black,
                alignItems: 'center',
              },
            ]}>
            Community votes
          </Text>
          <Tooltip
            size={20}
            style={{marginLeft: 10}}
            enableSheetContent
            title={locale.CommunityVotes}
            description="All registered Legiteem8 users are able to vote on community-based submissions. Each user generates an accuracy score based on their votes. Our system and administrators also actively monitor user votes to suppress spam votes intended to alter the results."
          />
        </View>
        <Text
          style={{
            textTransform: 'capitalize',
            color: handleColor(),
            fontSize: fontSize.lg,
          }}>
          {`${handleDecision()}${isRevealing ? 'ing' : ''}`}
        </Text>
      </View>

      {data?.getPost?.data?.authentication_result?.decision ===
      'UNDECIDED' ? null : (
        <View style={styles.voteCountWrapper}>
          <Text
            onPress={() =>
              navigation.navigate(screens.VOTES, {
                postId: data?.getPost?.data?.id,
              })
            }
            style={styles.viewAllVotes}>
            (View all)
          </Text>
          <Text style={styles.voteCount}>
            (
            {
              data?.getPost?.data?.authentication_result?.community_result
                ?.totalVotes
            }{' '}
            votes )
          </Text>
        </View>
      )}
      <View
        style={[
          styles.appraisalWrapper,
          {justifyContent: 'flex-start', alignItems: 'center'},
        ]}>
        <Icon
          name="expertCertified"
          size={20}
          tint={colors.grey}
          style={{marginRight: 10}}
        />
        <Text style={styles.appraisalLabel}>{locale.ExpertsNotVoting}</Text>
        <Tooltip
          size={20}
          style={{marginLeft: 10}}
          enableSheetContent
          title={locale.ExpertsNotVoting}
          description={`Experts don’t weigh in on community submissions due to the less stringent nature of the requirements. However, a community submission can be escalated to our "expert feedback" tier where you'll get an evaluation based on the information available. Should you be able to provide the full requirements for certification, you can escalate the item to our certification tiers.`}
        />
      </View>
      {canUpgradeToExpertVerification ? (
        <TouchableOpacity
          style={[
            styles.appraisalWrapper,
            {justifyContent: 'flex-start', alignItems: 'center'},
          ]}
          onPress={handleUpgradeToExpertAuthentication}
          disabled={isAuthenticationPending}>
          <Icon
            name="expertCertified"
            size={20}
            tint={isAuthenticationPending ? colors.grey : colors.secondary}
            style={{marginRight: 10}}
          />
          <Text
            style={[
              styles.appraisalLabel,
              {
                fontFamily: 'Gugi-Regular',
                color: isAuthenticationPending ? colors.grey : colors.primary,
              },
            ]}>
            {locale.UpgradeToExpertAnalysis}
          </Text>
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

export default StatsCard;
