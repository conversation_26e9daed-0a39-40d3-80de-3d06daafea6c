import React, {useCallback, useState} from 'react';
import {StyleSheet, TouchableOpacity, Text, View, Image} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {
  addFrontImage,
  addBackImage,
  addFrontTagImage,
  addBackTagImage,
  addCloseUpFrontImage,
  addCloseUpBackImage,
  addCopyrightImage,
  addLowerHemStitchingImage,
  addArmHemStitching,
  addExtraImage,
} from '../../redux/actions/uploadMedia';
import {authenticationTypeScreen} from '../../assets/strings';
import ImagePickerModal from '../../components/ImagePicker';
import useImageUpload from '../../tools/hooks/useImageUpload';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Icon from '../../components/Icon';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ActivityIndicator from '../../components/ActivityIndicator';
import {useWindowDimensions} from '../../tools/hooks';

const IMAGE_INITIAL_STATE = {
  local: null,
  remote: null,
};

const Uploader = ({item, required, uploadOptions, defaultImageUrls = null}) => {
  const dispatch = useDispatch();

  const {
    window: {width: windowWidth},
  } = useWindowDimensions();
  const isMounted = React.useRef(false);

  const [imagePath, setImagePath] = useState(() => ({
    [item?.value]: IMAGE_INITIAL_STATE,
  }));

  const [loading, setLoading] = useState(false);
  const [pickDialogVisible, setPickDialogVisible] = useState(false);
  const [errorDialogVisible, setErrorDialogVisible] = useState(false);

  const {upload} = useImageUpload();

  const imageUploadMethod = useSelector(
    state => state?.uploadMedia?.imageUploadMethod,
  );

  const handleImageUploadClick = () => {
    setPickDialogVisible(true);
  };

  const deleteImage = useCallback(
    value => {
      setImagePath(IMAGE_INITIAL_STATE);
      switch (value) {
        case 'front_image':
          return dispatch(
            addFrontImage({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'closeup_front_image':
          return dispatch(
            addCloseUpFrontImage({
              value: null,
              image: null,
              method: null,
            }),
          );

        case 'back_image':
          return dispatch(
            addBackImage({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'front_tag_image':
          return dispatch(
            addFrontTagImage({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'back_tag_image':
          return dispatch(
            addBackTagImage({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'copyright_image':
          return dispatch(
            addCopyrightImage({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'closeup_back_image':
          return dispatch(
            addCloseUpBackImage({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'lower_hem_stitching_image':
          return dispatch(
            addLowerHemStitchingImage({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'arm_hem_stitching_image':
          return dispatch(
            addArmHemStitching({
              value: null,
              image: null,
              method: null,
            }),
          );
        case 'extra':
          return dispatch(
            addExtraImage({
              value: null,
              image: null,
              method: null,
            }),
          );
      }
    },
    [dispatch],
  );

  const chooseImageUploadTypes = ({value, image, method}) => {
    switch (value) {
      case 'front_image':
        return dispatch(
          addFrontImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'closeup_front_image':
        return dispatch(
          addCloseUpFrontImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'back_image':
        return dispatch(
          addBackImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'front_tag_image':
        return dispatch(
          addFrontTagImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'back_tag_image':
        return dispatch(
          addBackTagImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'copyright_image':
        return dispatch(
          addCopyrightImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'closeup_back_image':
        return dispatch(
          addCloseUpBackImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'lower_hem_stitching_image':
        return dispatch(
          addLowerHemStitchingImage({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'arm_hem_stitching_image':
        return dispatch(
          addArmHemStitching({
            value: value,
            image: image,
            method: method,
          }),
        );
      case 'extra':
        return dispatch(
          addExtraImage({
            value: value,
            image: image,
            method: method,
          }),
        );
    }
  };

  const handleImageUpload = async img => {
    setLoading(true);
    console.log("Start Image Upload")
    try {
      const {path} = await upload(img);
      chooseImageUploadTypes({
        value: item.value,
        image: path,
        method: imageUploadMethod,
      });
      setImagePath({
        [item?.value]: {
          local: img?.path,
          remote: path,
        },
      });
    } catch (error) {
      console.log("Image Upload error==>", error)
      setErrorDialogVisible(true);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (!isMounted.current) {
      const isValid =
        !(defaultImageUrls?.local == null) &&
        !(defaultImageUrls?.remote == null);

      setImagePath(previousState =>
        isValid ? {[item?.value]: defaultImageUrls} : previousState,
      );
      if (isValid && item?.value) {
        chooseImageUploadTypes({
          value: item?.value,
          image: defaultImageUrls?.remote,
          method: imageUploadMethod,
        });
      }
      isMounted.current = true;
    }
  }, [
    defaultImageUrls,
    item?.value,
    imageUploadMethod,
    chooseImageUploadTypes,
  ]);

  const _image = React.useMemo(() => imagePath?.[item?.value], [
    imagePath,
    item?.value,
  ]);

  return (
    <View style={[styles.container, {width: windowWidth / 2.4}]}>
      <View style={{overflow: 'hidden', borderRadius: 15}}>
        <TouchAwareButton
          style={[
            styles.button,
            {width: '100%', height: windowWidth / (2.4 * 1.5)},
          ]}
          onPress={handleImageUploadClick}>
          {loading ? (
            <ActivityIndicator />
          ) : _image?.local?.length > 0 || _image?.remote?.length > 0 ? (
            <Image
              style={[
                styles.imagePlaceHolder,
                {width: '100%', height: windowWidth / (2.4 * 1.5)},
              ]}
              source={{uri: _image?.local || _image?.remote}}
            />
          ) : (
            <>
              <Image
                style={{
                  width: '55%',
                  resizeMode: 'contain',
                }}
                source={item?.placeholder}
              />
            </>
          )}
        </TouchAwareButton>
      </View>

      {_image?.remote?.length ? (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteImage(item.value)}>
          <Icon name="cancel_x" size={12} tint={colors.white} />
        </TouchableOpacity>
      ) : null}

      <Text style={[styles.imageLabels, {maxWidth: '100%'}]} numberOfLines={2}>
        {required ? (
          <Text style={styles.required}>
            {authenticationTypeScreen.ASTERISk}
          </Text>
        ) : null}
        {item.name}
      </Text>
      <ImagePickerModal
        visible={pickDialogVisible}
        setVisible={setPickDialogVisible}
        onImageSelected={handleImageUpload}
        name={item.value}
        uploadOptions={uploadOptions}
        canSelectFromGallery={!(uploadOptions == 2 || uploadOptions == 3)}
      />

      <InfoModal
        setVisible={setErrorDialogVisible}
        popUp={{
          state: errorDialogVisible,
          isError: true,
          data: {
            title: locale.UploadFailed,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacings.lg,
  },
  button: {
    paddingVertical: spacings.xl,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: colors.black,
    overflow: 'hidden',
    borderRadius: 15,
  },
  buttonText: {
    color: 'white',
    fontSize: 15,
  },
  imagePlaceHolder: {
    width: '100%',
    resizeMode: 'cover',
    borderRadius: 2,
  },
  iconStyles: {
    padding: spacings.lg,
  },
  imageLabels: {
    marginTop: spacings.sm,
    fontSize: fontSize.md,
    color: colors.black,
  },
  required: {
    color: colors.warning,
  },
  deleteButton: {
    position: 'absolute',
    right: 5,
    top: 5,
    padding: 8,
    backgroundColor: colors.black,
    borderRadius: 30,
  },
  iconLabel: {
    color: colors.black,
    fontWeight: 'bold',
    fontSize: fontSize.lg,
  },
});

export default React.memo(Uploader);
