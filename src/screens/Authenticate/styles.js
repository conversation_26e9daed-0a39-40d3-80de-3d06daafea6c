import {StyleSheet, Dimensions, Platform} from 'react-native';

import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const isIOS = Platform.OS == 'ios';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    ...(isIOS ? {paddingHorizontal: spacings.lg} : {}),
    paddingHorizontal: spacings.lg,
  },
  authenticateContainer: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  visibleSection: {
    width: '100%',
    padding: 20,
    paddingBottom: 30,
  },
  headerWrapper: {
    marginVertical: spacings.xl,
  },
  headerStyle: {
    marginBottom: spacings.sm,
    color: colors.black,
    fontSize: 18,
    fontFamily: 'Gugi-Regular',
    textTransform: 'capitalize',
  },
  headerText: {
    color: colors.darkGrey,
    fontFamily: 'Inter-Medium',
  },
  textStyle: {
    color: colors.black,
    fontSize: fontSize.md,
    fontFamily: 'Inter-SemiBold',
  },
  cardFieldStyle: {
    width: '100%',
    height: 50,
    marginBottom: 30,
    borderRadius: 4,
    color: colors.black,
  },
  cardStyle: {
    borderWidth: 1,
    color: colors.black,
    borderColor: colors.grey,
  },
  card: {
    borderWidth: 1,
    borderColor: colors.darkGrey,
    marginBottom: spacings.lg,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    paddingVertical: 18,
    backgroundColor: 'transparent',
    flex: 1,
  },
  cardText: {
    color: colors.black,
    fontSize: 15,
    fontFamily: 'Inter-Medium',
  },
  sectionOne: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    maxWidth: '70%',
  },
  sectionTwo: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    flex: 1,
  },
  cardHeader: {
    color: colors.black,
    fontSize: 16,
    fontFamily: 'Gugi-Regular',
    marginVertical: 10,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  loginContainer: {
    width: WIDTH * 0.9,
    backgroundColor: colors.white,
    flex: 1,
    alignItems: 'center',
    marginVertical: spacings.lg,
  },
  inputCard: {
    marginVertical: spacings.md,
    alignContent: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  errorLabel: {
    color: colors.black,
  },
  inputLabel: {
    color: colors.black,
    marginBottom: 5,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Medium',
  },
  inputField: {
    width: '100%',
    paddingLeft: spacings.md,
    color: colors.black,
    backgroundColor: colors.white,
    borderRadius: 2,
    fontSize: fontSize.md,
    justifyContent: 'flex-end',
    minHeight: 40,
    borderWidth: 1,
    borderColor: colors.grey,
  },
  addDescHeaderWrapper: {
    width: '100%',
    marginBottom: spacings.sm,
  },
  header: {
    fontSize: fontSize.xl,
    color: colors.black,
    fontFamily: 'Gugi-Regular',
  },
  submitButton: {
    height: 50,
    marginTop: spacings.lg,
    width: '100%',
    borderRadius: spacings.sm,
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSize.md,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  conditionField: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  conditionFieldLabel: {
    ...(isIOS ? {} : {maxWidth: '65%'}),
  },
  conditionDesc: {
    textAlignVertical: 'top',
  },
  pickerContainer: {
    width: '100%',
    borderWidth: 1,
    backgroundColor: colors.lightGrey,
    color: 'black',
    marginBottom: spacings.lg,
  },
  radio: {
    height: 15,
    width: 15,
    borderRadius: 15,
    borderWidth: 3,
    padding: spacings.sm,
  },
  radioLabel: {
    color: colors.black,
    marginLeft: spacings.sm,
    fontSize: fontSize.lg,
    paddingVertical: spacings.md,
  },
  radioWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacings.lg,
    paddingHorizontal: spacings.md,
  },
  radioContainer: {
    flexDirection: 'row',
    width: '100%',
    flexWrap: 'wrap',
  },
  modal: {
    color: colors.black,
  },
  modalTitle: {
    color: colors.black,
    fontWeight: 'bold',
    fontSize: fontSize.xl,
    marginBottom: spacings.md,
  },
  errorWrapper: {
    width: '100%',
    alignItems: 'flex-start',
  },
  errorText: {
    color: colors.warning,
    fontSize: fontSize.sm,
  },
  pickerText: {
    color: colors.black,
    fontSize: fontSize.lg,
  },
  modalStyles: {
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    backgroundColor: colors.white,
  },
  buttonStyles: {
    paddingHorizontal: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacings.md,
    marginBottom: spacings.md,
  },
  pickerButtonStyles: {
    height: 50,
    paddingHorizontal: spacings.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacings.md,
    ...(isIOS ? {marginTop: spacings.md} : {}),
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 2,
  },
});
