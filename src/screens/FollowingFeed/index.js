import React, {useState, useCallback} from 'react';
import {StyleSheet, Text, View, FlatList, Dimensions} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';

import {GET_FOLLOWING_USER_ACTIVITY} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import FeedCard from '../../components/FeedCard';
import {screens, icons} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import FollowingFeedLoader from '../../components/Loaders/FollowingFeedLoader';
import Icon from '../../components/Icon';
import locale from '../../assets/locale.json';
import {DefaultErrorComponent} from '../../components/QueryManager';
import RefreshControl from '../../components/RefreshControl';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import BottomSheet from '../../components/BottomSheet';
import {UNFOLLOW_USER} from '../../apollo/mutations';
import InfoModal from '../../components/Modal/InfoModal';
import ListActionRow from '../../components/ListActionRow';

const {height: SCREEN_HEIGHT} = Dimensions.get('screen');
const LIMIT = 100; // TODO: Pagination;

const SHEET_INITIAL_STATE = {
  visible: false,
  userId: null,
  username: '',
};

const Index = ({navigation}) => {
  const [refreshing, setRefreshing] = useState(false);
  const [sheetState, setSheetState] = React.useState(SHEET_INITIAL_STATE);

  const scrollRef = React.useRef();

  const {data, refetch, loading, error} = useQuery(
    GET_FOLLOWING_USER_ACTIVITY,
    {
      variables: {
        limit: LIMIT,
      },
    },
  );

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('tabPress', e => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (error) {}
    });

    return unsubscribe;
  }, [navigation, refetch]);

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  const resetSheet = () => {
    setSheetState(SHEET_INITIAL_STATE);
  };

  const handleMenuPressed = item => {
    setSheetState({
      visible: true,
      userId: item?.userId,
      username: item?.username,
    });
  };

  const renderItem = useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          onPress={() =>
            navigation.navigate(screens.SINGLE_LISTING, {
              params: item?.post?.id,
            })
          }>
          <FeedCard item={item} onMenuPressed={handleMenuPressed} />
        </TouchAwareButton>
      );
    },
    [navigation, refetch],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  return (
    <>
      <View style={styles.container}>
        <FlatList
          contentContainerStyle={
            error ||
            data?.getFollowingUserFeedActivity?.data?.edges?.length === 0
              ? {flex: 1}
              : {}
          }
          data={data?.getFollowingUserFeedActivity?.data?.edges}
          renderItem={renderItem}
          keyExtractor={extractKey}
          ListHeaderComponent={<View style={{paddingTop: spacings.lg}} />}
          ListEmptyComponent={
            loading ? (
              <FollowingFeedLoader />
            ) : error ? (
              <DefaultErrorComponent
                onRetryIconPress={onRefresh.bind(null, false)}
                style={{flex: 0.9}}
                customErrorText={locale.CouldNotFetchFollowingActivity}
              />
            ) : (
              <View style={styles.emptyView}>
                <Icon
                  name={icons.UPLOAD_PHOTO}
                  tint={colors.darkGrey}
                  size={70}
                />
                <Text style={styles.emptyViewText}>
                  {locale['YouHaveNotFollowedAny...']}
                </Text>
              </View>
            )
          }
          initialNumToRender={10}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onScroll={e => {
            scrollRef.current = e?.nativeEvent?.contentOffset?.y;
          }}
        />
      </View>
      <BottomSheet
        visible={sheetState.visible}
        setVisible={resetSheet}
        automaticallyAdjustHeight
        key={sheetState.authenticationId}>
        <View style={{minHeight: 50, padding: 20}}>
          <UnFollowUser
            userId={sheetState?.userId}
            resetSheet={resetSheet}
            refetch={refetch}
            username={sheetState?.username ?? ''}
          />
        </View>
      </BottomSheet>
    </>
  );
};

function UnFollowUser({
  userId = null,
  username = null,
  resetSheet = () => null,
  refetch = () => null,
}) {
  const [unfollowUser] = useMutation(UNFOLLOW_USER);
  const [popup, setPopup] = React.useState(false);

  const [loading, setLoading] = React.useState(false);

  const handleUnFollowUser = React.useCallback(async () => {
    setLoading(true);
    try {
      const {data} = await unfollowUser({variables: {userId}});
      const isSuccessful = data?.unfollowUser?.success;
      if (isSuccessful) {
        await refetch?.();
        resetSheet();
      } else {
        throw new Error();
      }
    } catch (error) {
      setPopup(true);
    } finally {
      setLoading(false);
    }
  }, [userId, unfollowUser]);

  return (
    <>
      <ListActionRow
        iconName="minus"
        iconContainerStyle={{backgroundColor: 'transparent'}}
        actionText={`${locale.UnFollow} ${username ?? 'User'}`}
        isLoading={loading}
        disabled={loading}
        onPress={handleUnFollowUser}
        iconTint={colors.red}
        iconSize={30}
        actionTextStyle={{color: colors.warning}}
      />
      <InfoModal
        setVisible={setPopup}
        popUp={{
          isError: true,
          state: popup,
          data: {
            title: locale.Error,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },

  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.sm,
    textAlign: 'center',
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Index;
