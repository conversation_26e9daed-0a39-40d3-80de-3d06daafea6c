import {StyleSheet} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacings.lg,
    backgroundColor: colors.white,
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacings.md,
  },
  labelStyle: {
    color: colors.black,
    fontSize: fontSize.md,
  },
  title: {
    color: colors.black,
    fontSize: fontSize.lg,
    fontFamily: 'Gugi-Regular',
    marginBottom: spacings.md,
  },
  section: {
    marginBottom: spacings.xl,
    paddingBottom: spacings.lg,
  },
  leftIconWrapper: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
  },
});
