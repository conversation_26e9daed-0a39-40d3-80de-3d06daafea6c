import React from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import { 
  useBlockBackNavigationForAndroid, 
  usePostShareURLConstructor 
} from '../../tools/hooks';
import Layout from '../../layout';
import { authenticationTypeScreen } from '../../assets/strings';
import QRCode from '../../components/QRCode';
import colors from '../../assets/colors';
import { spacings } from '../../assets/spacing';
import { fontSize } from '../../assets/font';

const QRCodeScreen = ({route, navigation}) => {
  const confirmation_code = route?.params?.params;
  //const confirmation_code = "";

  useBlockBackNavigationForAndroid();

  const {url} = usePostShareURLConstructor({
    authenticationKey: confirmation_code ?? '',
  });


  React.useLayoutEffect(() => {
    navigation?.setOptions?.({
      title: 'QR Code'
    });
  }, [navigation]);


  return (    
    <Layout contentContainerStyle={styles.container}>
      <View style={styles.labelWrapper}>      
        
        <Text style={styles.confirmation}>
          {authenticationTypeScreen.CONFIRMATION}
          <Text style={{fontFamily: 'Inter-Bold', textTransform: 'uppercase'}}>
            {`#${confirmation_code ?? ''}`}
          </Text>
        </Text>
      </View>
      <View style={styles.textWrapper}>
        
        <View
          style={{
            justifyContent: 'center',
            flexDirection: 'row',
            marginVertical: 30,
          }}>
          <QRCode url={url} />
        </View>
      </View>
      
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: spacings.lg,
    alignItems: 'center',
    paddingBottom: 100,
  },
  labelWrapper: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 3,
    borderBottomColor: colors.lightGrey,
    paddingVertical: spacings.lg,
    marginBottom: spacings.lg,
  },
  textWrapper: {
    width: '100%',
  },


  confirmation: {
    fontSize: fontSize.md,
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
});

export default QRCodeScreen;