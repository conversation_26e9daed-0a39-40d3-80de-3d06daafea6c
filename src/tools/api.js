/* eslint-disable prettier/prettier */
import axios from './axiosConfig';
import {apiEndPoint} from '../assets/strings';

//Sign in
export const LOGIN = async data => {
  return axios({
    url: apiEndPoint.LOGIN,
    method: 'POST',
    data,
  });
};

//Validate 2FA code request
export const VALIDATE_2FA_LOGIN = async data => {
  return axios({
    url: apiEndPoint.VERIFY_2FA_LOGIN,
    method: 'POST',
    data,
  });
};

//Send 2FA Enable Request
export function SEND_2FA_VALIDATE_CODE() {
  return axios({
    url: apiEndPoint.SEND_2FA_VALIDATE_CODE,
    method: 'GET',
  });
};

//Send 2FA Enable Request
export function SEND_2FA_VERIFICATION_CODE(code) {
  return axios({
    url: apiEndPoint.SEND_2FA_ENABLED+code,
    method: 'GET',
  });
};

//Send 2FA Disable Request
export function SEND_2FA_DISABLED() {
  return axios({
    url: apiEndPoint.SEND_2FA_DISABLED,
    method: 'GET',
  });
};

//Send 2FA Disable Request
export function SEND_DELETE_ACCOUNT() {
  return axios({
    url: apiEndPoint.DELETE_ACCOUNT,
    method: 'GET',
  });
};

//Send registration code
export const SEND_REGISTRATION_CODE = async data => {
  return axios({
    url: apiEndPoint.REGISTER_SEND_CODE,
    method: 'POST',
    data,
  });
};

//Verify registration code
export const VERIFY_REGISTRATION_CODE = async data => {
  return axios({
    url: apiEndPoint.REGISTER_VERIFY_CODE,
    method: 'POST',
    data,
  });
};

//Account registration request
export const SIGNUP = async data => {
  return axios({
    url: apiEndPoint.SIGNUP,
    method: 'POST',
    data,
  });
};

//Initiate password change request
export const FORGOT_PASSWORD = async data => {
  return axios({
    url: apiEndPoint.FORGOT_PASSWORD,
    method: 'POST',
    data,
  });
};

//Validate code request
export const VALIDATE_CODE = async data => {
  return axios({
    url: apiEndPoint.VALIDATE_CODE,
    method: 'POST',
    data,
  });
};


//Reset password request
export const RESET_PASSWORD = async data => {
  return axios({
    url: apiEndPoint.RESET_PASSWORD,
    method: 'POST',
    data,
  });
};

//Verify account request
export const VERIFY_ACCOUNT = async data => {
  return axios({
    url: apiEndPoint.VERIFY_ACCOUNT,
    method: 'POST',
    data,
  });
};


export const REFRESH_TOKEN = async data => {
  return axios({
    url: apiEndPoint.REFRESH_TOKEN,
    method: 'POST',
    data: {
      refresh_token: data,
    },
  });
};

export function GET_APP_DETAILS() {
  return axios({
    url: apiEndPoint.MOBILE_APP_DETAILS,
    method: 'GET',
  });
}

export function GET_EXPERT_QUEUE() {
  return axios({
    url: apiEndPoint.EXPERT_QUEUE,
    method: 'GET',
  });
}



