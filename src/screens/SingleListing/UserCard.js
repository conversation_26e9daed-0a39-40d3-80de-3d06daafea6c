import React from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import dayjs from 'dayjs';
import ClipBoard from '@react-native-community/clipboard';

import {styles} from './styles';
import {spacings} from '../../assets/spacing';
import {singleListingScreen, screens} from '../../assets/strings';
import {useUser} from '../../tools/hooks';
import ProfileImage from '../../components/ProfileImage';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {showToast} from '../../components/Toast';
import locale from '../../assets/locale.json';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import Favorite from './Favorite';
import Subscribe from './Subscribe';

var relativeTime = require('dayjs/plugin/relativeTime');
dayjs.extend(relativeTime);

const UserCard = ({data, refetchPost = () => null, renderShare = null}) => {
  const navigation = useNavigation();

  const {user: userData} = useUser();

  const authenticationData = data?.getPost?.data;

  const isPostByMe = userData?.id == data?.getPost?.data?.user?.id;

  const isAuthenticationPending = React.useMemo(
    () => data?.getPost?.data?.authentication_status === 'PENDING',
    [data?.getPost?.data?.authentication_status],
  );

  const handleNavigation = () => {
    if (isPostByMe) {
      navigation.push(screens.USER_PROFILE);
    } else {
      navigation.push(
        data?.getPost?.data?.user?.role === 'EXPERT'
          ? screens.EXPERTS_PROFILE
          : screens.PUBLIC_PROFILES,
        {
          params: data?.getPost?.data?.user?.id,
          expertUserId: data?.getPost?.data?.user?.id,
        },
      );
    }
  };

  const handleCopyToClipboard = React.useCallback(() => {
    ClipBoard.setString(
      `${data?.getPost?.data?.authentication_key?.toUpperCase?.() ?? ''}`,
    );
    showToast({type: 'success', message: locale.IDCopiedToClipboard});
  }, [data?.getPost?.data?.authentication_key]);

  return (
    <View style={styles.userContainer}>
      <View style={styles.userContainerTopSection}>
        <TouchableOpacity style={styles.userId} onPress={handleNavigation}>
          <Text style={[styles.sm, {marginRight: spacings.sm}]}>
            {singleListingScreen.BY}
          </Text>
          <ProfileImage url={data?.getPost?.data?.user?.image} size={25} />
          <Text style={[styles.sm, {marginLeft: spacings.sm}]}>
            {data?.getPost?.data?.user?.username}
          </Text>
        </TouchableOpacity>
        <Text style={styles.sm}>
          {singleListingScreen.UPLOADED}
          {dayjs(data?.getPost?.data?.created_at).fromNow()}
        </Text>
      </View>
      <View style={styles.userContainerBottomSection}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text numberOfLines={3} style={[styles.userCardTitleText]}>
            {data?.getPost?.data?.title}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TouchAwareButton
              onPress={handleCopyToClipboard}
              style={styles.authKey}>
              <Text style={styles.postId}>
                {singleListingScreen.ID}{' '}
                {data?.getPost?.data?.authentication_key ?? ''}
              </Text>
              <Icon name="copyToClipboard" tint={colors.primary} size={15} />
            </TouchAwareButton>
          </View>
        </View>
        {!isAuthenticationPending ? (
          <View
            style={[
              styles.authKey,
              {
                flexWrap: 'wrap',
                alignItems: 'center',
                flexDirection: 'row',
              },
            ]}>
            <Favorite
              postId={data?.getPost?.data?.id}
              isLiked={data?.getPost?.data?.is_liked_by_me}
              numberOfLikes={data?.getPost?.data?.number_of_likes}
            />
            <Subscribe
              postId={authenticationData?.id}
              isPostByMe={isPostByMe}
              postUpdateSubscription={
                authenticationData?.post_updates_subscription
              }
              refetchPost={refetchPost}
            />
            {renderShare}
          </View>
        ) : null}
      </View>
    </View>
  );
};

export default UserCard;
