import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';

import {usePaginatedBrands} from '../../tools/hooks';
import BottomSheet from '../../components/BottomSheet';
import ActivityIndicator from '../../components/ActivityIndicator';
import Button from '../../components/Button';
import locale from '../../assets/locale.json';
import styles from './styles';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import TextInput from '../../components/TextInput';

function BrandPicker({
  children = null,
  onClear = () => null,
  onApply = () => null,
  selectedItems = [],
}) {
  const [sheetVisible, setSheetVisible] = React.useState(false);

  const handlePress = () => {
    setSheetVisible(true);
  };

  const closeSheet = () => {
    setSheetVisible(false);
  };
  return (
    <>
      {/* Render the caller UI yourself, the picker will be handled automatically */}
      {children({openPicker: handlePress})}
      <BottomSheet
        visible={sheetVisible}
        setVisible={setSheetVisible}
        snapPoints={['40%', '80%']}
        index={1}
        showKeyboardDismissButton>
        <PickerUI
          onClear={onClear}
          onApply={onApply}
          closeSheet={closeSheet}
          selectedItems={selectedItems}
        />
      </BottomSheet>
    </>
  );
}

function PickerUI({
  onClear = () => null,
  onApply = () => null,
  closeSheet = () => null,
  selectedItems = [],
}) {
  const [selected, setSelected] = React.useState(() => selectedItems ?? []);
  const [brandTerm, setBrandTerm] = React.useState('');

  const {
    brands,
    handleFetchMore,
    isFetchingMore,
    isSearchLoading,
  } = usePaginatedBrands({
    searchTerm: brandTerm,
    notifyOnNetworkStatusChange: false,
    limit: 30,
    isFocused: selected?.length == 0,
  });

  const handleRowPressed = item => {
    setSelected(previousState =>
      selected?.length == 0
        ? [item]
        : previousState?.find(i => i?.id == item?.id)
        ? previousState?.filter(i => i?.id !== item?.id)
        : [...previousState, item],
    );
  };

  const handleClear = () => {
    setSelected([]);
    setBrandTerm('');
    onClear();
    closeSheet();
  };

  const handleApply = () => {
    onApply(selected);
    closeSheet();
  };

  const renderItem = React.useCallback(
    ({item}) => (
      <ListRow
        item={item}
        onPress={handleRowPressed}
        isSelected={selected?.find(i => item?.id == i?.id)}
      />
    ),
    [selected, handleRowPressed],
  );

  const renderInput = React.useMemo(
    () => (
      <TextInput
        placeholder="Search"
        value={brandTerm}
        onChange={setBrandTerm}
        style={styles.searchInput}
        containerStyle={{flex: 1}}
      />
    ),
    [setBrandTerm, brandTerm],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);
  return (
    <>
      {selected?.length > 0 ? (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            marginBottom: 10,
          }}>
          <Button
            text={locale.Clear}
            style={{
              ...styles.filterUtilityButton,
              backgroundColor: colors.redBadge,
            }}
            scaleAnimationThreshold={0.96}
            onPress={handleClear}
            textStyle={{fontSize: 14}}
          />
          <Button
            text={locale.Apply}
            style={{
              ...styles.filterUtilityButton,
              backgroundColor: colors.primary,
            }}
            scaleAnimationThreshold={0.96}
            onPress={handleApply}
            textStyle={{fontSize: 14}}
          />
        </View>
      ) : null}
      <View style={[styles.inputWrapper]}>
        <Icon name="search_icon" size={18} tint={colors.grey} />
        {renderInput}
        {isSearchLoading ? (
          <ActivityIndicator style={{position: 'absolute', right: 0}} />
        ) : null}
      </View>
      <BottomSheetFlatList
        keyExtractor={extractKey}
        data={brands}
        renderItem={renderItem}
        ListFooterComponent={
          <>
            {isFetchingMore ? (
              <View
                style={{
                  marginBottom: 100,
                  flex: 1,
                }}>
                <ActivityIndicator size={30} />
              </View>
            ) : null}
          </>
        }
        onEndReachedThreshold={0.2}
        onEndReached={handleFetchMore}
        scrollToOverflowEnabled={true}
        scrollEventThrottle={1900}
        initialNumToRender={20}
        contentContainerStyle={{paddingBottom: 50}}
      />
    </>
  );
}

function ListRow({item = null, onPress = () => null, isSelected = false}) {
  return (
    <TouchableOpacity style={styles.listRow} onPress={onPress.bind(null, item)}>
      <Text style={styles.listRowText}>{item?.name}</Text>
      <Icon
        style={{
          ...styles.radioButton,
          backgroundColor: isSelected ? colors.primary : colors.grey,
        }}
        name={isSelected ? 'checked_radio' : 'unchecked_radio'}
        tint={isSelected ? colors.primary : colors.grey}
        size={20}
      />
    </TouchableOpacity>
  );
}

export default BrandPicker;
