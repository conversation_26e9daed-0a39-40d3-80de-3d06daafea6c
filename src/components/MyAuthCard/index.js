import React, {useCallback} from 'react';
import {StyleSheet, Text, View, Dimensions} from 'react-native';
import dayjs from 'dayjs';
import {useNavigation} from '@react-navigation/native';

import Icon from '../Icon';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {icons, screens, images} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import TouchAwareButton from '../Button/TouchAwareButton';
import FastImage from '../FastImage';
import locale from '../../assets/locale.json';

const {width} = Dimensions.get('window');
const WIDTH = width;
const CARD_WIDTH = WIDTH - spacings.xxl;

function titleMapping(authenticationType) {
  switch (authenticationType) {
    case 2:
      return 'Expert Authentication';
    case 3:
      return 'Expert Authentication + NFT';
    case 4:
      return 'Tag Only';
    case 5:
      return 'Expert Feedback';
    default:
      return 'Community Vote';
  }
}

const MyAuthCard = ({
  item,
  onMenuPressed = () => null,
  showAuthenticationPendingStatus = false,
}) => {
  const navigation = useNavigation();

  const handleDecisionLayout = useCallback(type => {
    switch (type) {
      case 'REAL':
        return {
          backgroundColor: colors.greenBadge,
        };
      case 'FAKE':
        return {
          backgroundColor: colors.redBadge,
        };
      case 'UNDECIDED':
        return {
          backgroundColor: colors.darkBadge,
        };
      case 'UNDETERMINED':
        return {
          backgroundColor: colors.amber,
        };
      default:
        return {backgroundColor: colors.darkBadge};
    }
  }, []);

  const handleIconColor = type => {
    switch (type) {
      case 'REAL':
        return colors.green;
      case 'FAKE':
        return colors.warning;
      case 'UNDECIDED':
        return colors.darkGrey;
      case 'UNDETERMINED':
        return colors.amber;
      default:
        return colors.grey;
    }
  };
  const handleDecision = useCallback(decision => {
    switch (decision) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
          return 'UNDETERMINED';
      default:
        return 'PENDING';
    }
  }, []);

  const handleUpgradeToExpertCertification = () => {
    navigation.navigate(screens.AUTHENTICATE, {
      isUpgradeToExpertAuthentication: true,
      upgradeData: {
        authenticationId: item?.queue_id,
        postId: item?.id,
      },
    });
  };

  return (
    <View style={styles.cardsGroup}>
      <View style={styles.topBar}>
        <View style={styles.userBar}>
          <Text style={styles.userStyle}>
            {titleMapping(item?.authentication_type?.id)}
          </Text>
        </View>
        <TouchAwareButton
          onPress={() => onMenuPressed(item)}
          style={{paddingLeft: 10, paddingVertical: 5}}>
          <Icon
            name={icons.ELIPSIS_HORIZONTAL}
            size={25}
            tint={colors.darkGrey}
            clickable={false}
          />
        </TouchAwareButton>
      </View>
      <View style={{position: 'relative'}}>
        {item?.post_status_type === 'PRIVATE' ? (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              backgroundColor: 'rgba(1,1,1,0.5)',
              zIndex: 10,
              padding: 5,
              borderRadius: 2,
            }}>
            <Icon name="lock" size={20} tint={colors.white} />
          </View>
        ) : null}
        <FastImage
          source={
            item?.images?.featured || item?.images?.closeup_front
              ? {uri: item?.images?.featured || item?.images?.closeup_front}
              : images.PLACEHOLDER
          }
          style={styles.imageStyle}
        />
        {item?.authentication_type?.id == 1 ? (
          <>
            <View
              style={[
                styles.badgeStyle,
                handleDecisionLayout(item?.authentication_result?.decision),
              ]}>
              <Icon
                name={icons.PEOPLE_OUTLINE}
                size={fontSize.md}
                tint={colors.white}
              />

              <Text style={styles.badgeText}>
                {handleDecision(item?.authentication_result.decision)}
              </Text>
              {item?.authentication_result?.community_result?.decision ===
              'UNDECIDED' ? null : (
                <Text style={[styles.badgeText, {marginRight: spacings.sm}]}>
                  ({item?.authentication_result?.community_result?.score}
                  )%
                </Text>
              )}
            </View>
            {!(item?.appraisal_value == null) ||
            !(
              item?.authentication_result?.expert_result?.appraisal_value ==
              null
            ) ? (
              <View
                style={[
                  styles.appraisalWrapper,
                  {backgroundColor: colors.darkBadge},
                ]}>
                <Icon
                  name={icons.PEOPLE_OUTLINE}
                  size={fontSize.md}
                  tint={colors.white}
                />
                <Text style={styles.appraisalText}>
                  $
                  {item?.authentication_type?.id == 1
                    ? item?.appraisal_value
                    : item?.authentication_result?.expert_result
                        ?.appraisal_value}
                </Text>
              </View>
            ) : null}
          </>
        ) : null}
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginTop: 10,
          flexWrap: 'wrap',
        }}>
        <Text
          numberOfLines={1}
          style={{
            fontFamily: 'Inter-Bold',
            color: colors.black,
            maxWidth: '85%',
            textTransform: 'capitalize',
            fontSize: 16,
          }}>
          {item?.title}
        </Text>
        <Text style={styles.timeStyle}>
          {dayjs(item?.approved_at ?? item?.created_at).fromNow()}
        </Text>
      </View>
      <View style={styles.bottomBar}>
        {item?.authentication_status === 'PENDING' &&
        showAuthenticationPendingStatus ? (
          <View style={styles.expertWrapper}>
            <Icon
              name={
                item?.authentication_type?.id == 1
                  ? 'community_icon'
                  : 'expertCertified'
              }
              size={15}
              tint={colors.warning}
            />
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              style={{
                color: colors.warning,
                marginLeft: spacings.sm,
                fontFamily: 'Gugi-Regular',
              }}>
              {locale.AuthenticationPending}
            </Text>
          </View>
        ) : (
          <View style={styles.expertWrapper}>
            {item?.authentication_type?.id == 1 ? (
              <>
                <Icon
                  name="expertCertified"
                  size={15}
                  tint={colors.secondary}
                />
                <TouchAwareButton
                  onPress={handleUpgradeToExpertCertification}
                  style={{
                    paddingRight: 20,
                    paddingVertical: 5,
                  }}>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    style={{
                      color: colors.primary,
                      fontFamily: 'Gugi-Regular',
                      marginLeft: spacings.sm,
                    }}>
                    {locale.UpgradeToExpertAnalysis}
                  </Text>
                </TouchAwareButton>
              </>
            ) : (
              <>
                <Icon
                  name="expertCertified"
                  size={15}
                  tint={colors.secondary}
                />
                <Text style={[styles.titleStyle, {marginLeft: spacings.sm}]}>
                  Experts{' '}
                  {item?.authentication_type?.id == 5 ? 'Feedback' : 'Voted'}:
                </Text>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={[
                    styles.titleStyle,
                    {
                      fontFamily: 'Inter-Medium',
                      color: handleIconColor(
                        item?.authentication_result?.expert_result?.decision,
                      ),
                      paddingHorizontal: spacings.sm,
                    },
                  ]}>
                  {handleDecision(
                    item?.authentication_result?.expert_result?.decision,
                  )}
                </Text>
              </>
            )}
            <Text style={styles.expertAppraisal}>
              {item?.authentication_type?.id == 1 ||
              item?.authentication_result?.expert_result?.decision === 'FAKE' ||
              item?.authentication_result?.expert_result?.appraisal_value ==
                null
                ? null
                : `|  $${item?.authentication_result?.expert_result?.appraisal_value}`}

                {
                  item?.is_private ? ' (Private)' : ''
                }
            </Text>
          </View>
        )}
      </View>
      {!(item?.nft_detail == null) ? (
        <View
          style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
          <Icon name="nftEmblem" size={15} tint={colors.yellow} />
          <Text style={[styles.titleStyle, {marginLeft: spacings.sm}]}>
            {locale.NFTCertified}
          </Text>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  cardsGroup: {
    width: CARD_WIDTH,
    marginBottom: spacings.lg,
    borderRadius: 2,
    borderBottomColor: colors.lightGrey,
    paddingBottom: spacings.xl,
    borderBottomWidth: 3,
  },
  imageStyle: {
    width: '100%',
    height: CARD_WIDTH / 2,
    borderRadius: 2,
  },
  userBar: {
    flexDirection: 'row',
    paddingVertical: spacings.sm,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flex: 6.5 / 10,
  },
  textBar: {
    width: '100%',
    paddingBottom: spacings.md,
  },
  titleStyle: {
    fontSize: 13,
    color: colors.black,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Medium',
  },
  vote: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badgeStyle: {
    position: 'absolute',
    right: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  badgeText: {
    fontSize: fontSize.sm,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: spacings.sm,
    textTransform: 'capitalize',
  },
  appraisalWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 0,
    left: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  appraisalText: {
    fontSize: fontSize.sm,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: spacings.sm,
  },
  expertAppraisal: {
    fontSize: fontSize.md,
    color: colors.black,
    fontWeight: 'bold',
  },
  profileImageStyle: {
    height: 20,
    width: 20,
    borderRadius: 20,
  },
  userStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    fontWeight: 'bold',
  },
  activityNotification: {
    fontSize: fontSize.md,
    color: colors.black,
    marginLeft: spacings.sm,
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  timeStyle: {
    fontSize: 10,
    color: colors.darkGrey,
  },
  bottomBar: {
    marginTop: spacings.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  expertWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemSeparator: {
    paddingRight: spacings.sm,
    color: colors.black,
  },
});

export default React.memo(MyAuthCard);
