import React from 'react';
import {StyleSheet} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';

import AllBrands from './AllBrands';
import AllCategories from './AllCategories';
import colors from '../../assets/colors';
import {screens, navLabels} from '../../assets/strings';
import {fontSize} from '../../assets/font';

const TopTabs = createMaterialTopTabNavigator();

const Index = () => {
  return (
    <TopTabs.Navigator
      initialRouteName={screens.ALL_BRANDS}
      screenOptions={{
        swipeEnabled: false,
        tabBarActiveTintColor: colors.black,
        tabBarIndicatorStyle: styles.indicator,
        tabBarStyle: styles.barStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
      }}
      sceneContainerStyle={{backgroundColor: colors.white}}>
      <TopTabs.Screen
        name={screens.ALL_BRANDS}
        component={AllBrands}
        options={{
          tabBarLabel: navLabels.ALL_BRANDS,
        }}
      />
      <TopTabs.Screen
        name={screens.ALL_CATEGORIES}
        component={AllCategories}
        options={{tabBarLabel: navLabels.ALL_CATEGORIES}}
      />
    </TopTabs.Navigator>
  );
};

const styles = StyleSheet.create({
  indicator: {
    backgroundColor: colors.primary,
    borderRadius: 25,
  },
  barStyle: {
    backgroundColor: colors.white,
    elevation: 1,
    borderTopWidth: 3,
    borderTopColor: colors.lightGrey,
  },
  tabBarLabelStyle: {
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
    fontWeight: '700',
  },
});

export default Index;
