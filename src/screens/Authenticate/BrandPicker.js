import React, {useState, useCallback} from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  Keyboard,
  Platform,
  FlatList,
} from 'react-native';
import {useIsFocused} from '@react-navigation/native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {icons} from '../../assets/strings';
import Icon from '../../components/Icon';
import {usePaginatedBrands} from '../../tools/hooks';
import QueryManager from '../../components/QueryManager';
import RefreshControl from '../../components/RefreshControl';
import ActivityIndicator from '../../components/ActivityIndicator';
import locale from '../../assets/locale.json';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import AddCustomBrand from './AddCustomBrand';
import InfoModal from '../../components/Modal/InfoModal';

const BrandPicker = ({navigation, route}) => {
  const [brandTerm, setBrandTerm] = useState('');
  const [refreshing, setRefreshing] = React.useState(false);
  const isFocused = useIsFocused();

  const checkNFTStatus = route?.params?.checkNFTStatus ?? false;
  const onBrandSelected = route?.params?.onBrandSelected ?? (() => null);

  const firstRef = React.useRef(true);

  const {
    data,
    handleFetchMore,
    loading,
    error,
    refetch,
    isFetchingMore,
    resetCursorRef,
    isSearchLoading,
  } = usePaginatedBrands({
    searchTerm: brandTerm,
    notifyOnNetworkStatusChange: false,
    limit: 30,
  });

  const allBrands = React.useMemo(() => data?.getBrands?.data?.edges, [
    data?.getBrands?.data?.edges,
  ]);

  const selectBrand = useCallback(
    (item, isCustomBrand = false) => {
      onBrandSelected(item, isCustomBrand);
      navigation.goBack();
    },
    [navigation, onBrandSelected],
  );

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
        resetCursorRef();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch, resetCursorRef],
  );

  const renderInput = React.useMemo(
    () => (
      <TextInput
        style={styles.inputStyles}
        selectionColor={colors.grey}
        placeholder="Search"
        placeholderTextColor={colors.darkGrey}
        value={brandTerm}
        onChangeText={setBrandTerm}
      />
    ),
    [setBrandTerm, brandTerm],
  );

  const dismissKeyboard = Keyboard.dismiss;

  React.useEffect(() => {
    if (!isFocused) {
      dismissKeyboard();
    }
  }, [isFocused, dismissKeyboard]);

  React.useEffect(() => {
    if (firstRef.current) {
      firstRef.current = false;
    }
  }, []);

  return (
    <View style={styles.container}>
      <QueryManager {...{data, error, refetch, loading}}>
        <QueryManager.Data>
          <View style={[styles.inputWrapper]}>
            <Icon name={icons.SEARCH_ICON} size={18} tint={colors.grey} />
            {renderInput}
            {isSearchLoading ? (
              <ActivityIndicator style={{position: 'absolute', right: 0}} />
            ) : null}
          </View>
          <View style={{marginTop: 20}}>
            <AddCustomBrand
              handleSelection={item => selectBrand(item, true)}
              defaultValueForBrand={brandTerm}
            />
          </View>
          <FlatList
            data={allBrands}
            renderItem={({item}) => (
              <ItemRow
                item={item}
                handleSelection={selectBrand}
                checkNFTStatus={checkNFTStatus}
              />
            )}
            keyExtractor={item => item?.id?.toString?.()}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            onEndReachedThreshold={0.2}
            onEndReached={handleFetchMore}
            scrollToOverflowEnabled={true}
            scrollEventThrottle={1900}
            initialNumToRender={10}
            contentContainerStyle={[
              {paddingBottom: 50, paddingTop: 20},
              allBrands?.length == 0 ? {flex: 1} : {},
            ]}
            onScroll={dismissKeyboard}
            ListFooterComponent={
              <>
                {isFetchingMore ? (
                  <View style={{marginVertical: 10}}>
                    <ActivityIndicator />
                  </View>
                ) : null}
                <View style={{margin: spacings.lg}} />
              </>
            }
            ListEmptyComponent={
              !loading ? (
                <DefaultEmptyComponent
                  containerStyle={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  lottieStyle={{width: 80, height: 80}}
                />
              ) : null
            }
            showsVerticalScrollIndicator={false}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <ActivityIndicator />
          </View>
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale['SomethingWentWrong.']}
        />
      </QueryManager>
    </View>
  );
};

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

const ItemRow = React.memo(function ({
  item = null,
  handleSelection = () => null,
  checkNFTStatus = false,
}) {
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const handleItemSelection = React.useCallback(
    item => {
      if (checkNFTStatus && !item?.is_nft_enabled) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: true,
          data: {
            title: locale.Error,
            description:
              locale.NFTAuthenticationIsNotAvailableForSelectedBrandAtTheMoment,
          },
        }));
      } else {
        handleSelection(item);
      }
    },
    [checkNFTStatus, handleSelection],
  );

  return (
    <TouchAwareButton
      containerStyle={{width: '100%'}}
      style={{...styles.brandItemStyle}}
      onPress={() => handleItemSelection(item)}
      scaleAnimationThreshold={0.98}>
      <Text style={{color: colors.black, fontFamily: 'Inter-Regular'}}>
        {item?.name}
      </Text>
      <InfoModal
        setVisible={() => {
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
    </TouchAwareButton>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
    paddingTop: 20,
  },
  inputStyles: {
    borderColor: colors.lightGrey,
    backgroundColor: colors.lightGrey,
    borderWidth: StyleSheet.hairlineWidth,
    flex: 1,
    color: colors.black,
    borderRadius: 10,
    ...(Platform.OS === 'ios' ? {padding: 15} : {paddingVertical: 7}),
  },
  inputWrapper: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    backgroundColor: colors.lightGrey,
    borderRadius: 4,
    paddingLeft: spacings.md,
  },
  topSection: {
    paddingTop: spacings.lg,
  },
  brandItemStyle: {
    paddingBottom: 20,
    justifyContent: 'center',
  },
  addBrandButton: {
    borderBottomWidth: 3,
    borderBottomColor: colors.lightGrey,
    paddingVertical: spacings.lg,
    justifyContent: 'center',
  },
  addBrandButtonText: {
    color: colors.primary,
  },
  footer: {
    height: spacings.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default BrandPicker;
