import React, {useState, useCallback} from 'react';
import {
  StyleSheet,
  FlatList,
  Text,
  View,
  RefreshControl,
  TouchableOpacity,
  Platform,
} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import CreditCardsLoader from '../../components/Loaders/CreditCardsLoader';
import {GET_ALL_CARDS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {creditCardScreen, screens} from '../../assets/strings';
import QueryManager from '../../components/QueryManager';

const CreditCards = ({navigation}) => {
  const {data, loading, error, refetch} = useQuery(GET_ALL_CARDS);
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch?.();
    } catch (_) {
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  useFocusEffect(
    React.useCallback(() => {
      try {
        refetch?.();
      } catch (_) {}
    }, [refetch]),
  );

  const renderItem = ({item}) => {
    return (
      <View style={styles.card}>
        <View>
          <View style={styles.cardTitleWrapper}>
            <Text style={styles.cardTitle}>{item?.brand}</Text>
            {item.is_default ? (
              <Text style={styles.defaultLabel}>
                {creditCardScreen.DEFAULT}
              </Text>
            ) : null}
          </View>
          <Text style={[styles.date, {marginTop: 5}]}>{item?.name}</Text>
        </View>
        <View style={styles.rightSection}>
          <Text style={[styles.cardTitle]}>{item?.number}</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={() =>
              navigation.navigate(screens.EDIT_CREDIT_CARD, {params: item?.id})
            }>
            <Text style={styles.buttonText}>
              {creditCardScreen.BUTTON_LABEL}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <QueryManager
        data={data}
        error={error}
        loading={loading}
        refetch={refetch}>
        <QueryManager.Data>
          <FlatList
            data={data?.getAllCards?.data}
            showsVerticalScrollIndicator={false}
            renderItem={renderItem}
            keyExtractor={item => item?.id?.toString?.()}
            contentContainerStyle={
              Array.isArray(data?.getAllCards?.data) &&
              data?.getAllCards?.data?.length == 0
                ? {flex: 1}
                : {}
            }
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                progressBackgroundColor={colors.lightGrey}
                colors={[colors.primary, colors.black]}
                tintColor={colors.primary}
                onRefresh={onRefresh}
              />
            }
            ListFooterComponent={
              Platform.OS === 'android' ? (
                <TouchableOpacity
                  style={styles.addCardButton}
                  onPress={() => navigation.navigate(screens.ADD_CARD)}>
                  <Text style={styles.addCardButtonText}>
                    {creditCardScreen.ADD_CARD}
                  </Text>
                </TouchableOpacity>
              ) : null
            }
            ListEmptyComponent={
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 100,
                }}>
                <Text
                  style={[
                    styles.emptyContainerText,
                    {
                      maxWidth: '70%',
                      textAlign: 'center',
                    },
                  ]}>
                  {creditCardScreen.NO_CARDS_SAVED}
                </Text>
              </View>
            }
            ListHeaderComponent={
              <Text style={styles.headerTitleStyle}>Saved credit cards</Text>
            }
          />
        </QueryManager.Data>
      </QueryManager>
      <QueryManager.Loading>
        <CreditCardsLoader />
      </QueryManager.Loading>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  card: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: spacings.lg,
    borderWidth: 2,
    borderColor: colors.lightGrey,
    marginTop: spacings.lg,
    flexWrap: 'wrap',
  },
  cardTitleWrapper: {
    flexDirection: 'row',
  },
  cardTitle: {
    fontSize: 16,
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
  headerTitleStyle: {
    color: colors.black,
    fontSize: fontSize.lg,
    marginTop: spacings.lg,
    fontFamily: 'Gugi-Regular',
  },
  defaultLabel: {
    color: colors.white,
    backgroundColor: colors.black,
    paddingHorizontal: spacings.sm,
    paddingVertical: 2,
    fontSize: 12,
    borderRadius: spacings.sm,
    marginLeft: spacings.md,
    alignSelf: 'center',
  },
  rightSection: {
    alignItems: 'flex-end',
    marginLeft: 'auto',
  },
  leftSection: {
    alignItems: 'flex-start',
  },
  button: {
    marginTop: spacings.md,
    paddingVertical: spacings.sm,
  },
  buttonText: {
    fontSize: fontSize.md,
    color: colors.primary,
  },
  date: {
    fontSize: fontSize.md,
  },
  addCardButton: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary,
    marginBottom: spacings.xxl,
    borderRadius: 4,
  },
  addCardButtonText: {
    fontSize: fontSize.lg,
    color: colors.white,
    fontWeight: 'bold',
  },
  label: {
    fontSize: fontSize.lg,
    fontWeight: 'bold',
    color: colors.black,
    marginBottom: spacings.sm,
  },
});
export default CreditCards;
