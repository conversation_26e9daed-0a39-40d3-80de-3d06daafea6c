import {StyleSheet, Platform} from 'react-native';

import colors from '../../assets/colors';
import scale from '../../tools/scale';

const isIOS = Platform.OS === 'ios';

export default StyleSheet.create({
  containerWrapper: {
    paddingHorizontal: isIOS ? 10 : 18,
    borderBottomWidth: 0.5,
    paddingTop: isIOS ? 0 : 16, // Added
  },
  container: {
    height: 50,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
  titleContainer: {
    textAlign: 'center',
    maxWidth: `${scale.moderateScale(60)}%`,
  },
  title: {
    fontSize: 20,
    color: colors.white,
    fontFamily: 'Gugi-Regular',
    textAlign: 'center',
  },
  headerButton: {flexDirection: 'row', alignItems: 'center'},
  headerLeft: {position: 'absolute', left: 0},
  headerRight: {position: 'absolute', right: 0},
});
