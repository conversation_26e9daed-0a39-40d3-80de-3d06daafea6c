import React, {useLayoutEffect, useState, useRef, useEffect, useCallback} from 'react';
import {
  StyleSheet,
  Text,
  Pressable,
  TouchableOpacity,
  View,
  Switch
} from 'react-native';
import {DrawerActions, useFocusEffect} from '@react-navigation/native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {icons, persistent, screens} from '../../assets/strings';
import {profileSettingsScreen} from '../../assets/strings';
import Icon from '../../components/Icon';
import locale from '../../assets/locale.json';
import { useDispatch } from 'react-redux';
import { LOGIN, SEND_2FA_DISABLED, SEND_2FA_VALIDATE_CODE } from '../../tools/api';
import { showToast } from '../../components/Toast';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ActivityIndicator } from 'react-native';

const Index = ({navigation}) => {

  const [isLoading, setIsLoading] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [error, setError] = useState(null);
  const [userEmail, setUserEmail] = useState(null);
  const isMounted = useRef(true);
  const [passwordHidden, setPasswordHidden] = useState(true);
  const dispatch = useDispatch()  
  
  useFocusEffect(
    React.useCallback(function () {
      (async () => {
        try {
          const is2FA = await AsyncStorage.getItem(persistent.IS_2FA);
          const email = await AsyncStorage.getItem(persistent.EMAIL_ID);          
          setIsEnabled(is2FA == "1" ? true : false)          
          setUserEmail(email)
        } catch (_) {}
      })();
    }, []),
  );

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setError(null);
      isMounted.current = false;
    };
  }, []);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: 'Profile',
      headerTintColor: '#fff',
      headerRight: null,
      headerLeft: () => (
        <Pressable
          style={styles.leftIconWrapper}
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}>
          <Icon
            name={icons.ARROW_LEFT}
            tint={colors.white}
            size={fontSize.xxxl}
          />
        </Pressable>
      ),
    });
  }, []);

  const toggleSwitch = useCallback(() => {
    setIsEnabled(previousState => !previousState);
    enableDisable2FA()
  });

  function enableDisable2FA(previousState) {
    if(isEnabled == true) {
      if (isMounted.current === true) {
        let credentials = {
          
        };
        send2FADisable(credentials);
      }

    } else {
      if (isMounted.current === true) {
        let credentials = {
          
        };
       send2FAVerificationCode(credentials);        
      }
    }
  }
    
  const send2FAVerificationCode = useCallback(
    async credentials => {
      setError('');
      setIsLoading(true);
      try {
        const response = await SEND_2FA_VALIDATE_CODE();
        const res = response?.data;

        setIsLoading(false);
        if (res?.success) {
          res.email = userEmail
          res.isFromProfile = true
          navigation.navigate(screens.FA2_VALIDATE_CODE_FROM_PROFILE, {params: res});                 
        } 

        if (res?.message?.length > 0) {
          showToast({message});
        } else {
          throw new Error();
        }
      } catch (e) {
        setError(
          e?.response?.data?.message ?? locale.SomethingWentWrongPleaseTryAgain,
        );
        setIsLoading(false);
      }
    },
    [dispatch],
  );

  const send2FADisable = useCallback(
    async credentials => {
      setError('');
      setIsLoading(true);
      try {        
        const response = await SEND_2FA_DISABLED();
        const res = response?.data;
        setIsLoading(false);
        if (res?.success) {
          await AsyncStorage.setItem(persistent.IS_2FA, "0");
        }
        if (res?.message?.length > 0) {
          showToast({message: res?.message});
        } else {
          throw new Error();
        }
      } catch (e) {
        setError(
          e?.response?.data?.message ?? locale.SomethingWentWrongPleaseTryAgain,
        );
        setIsLoading(false);
      }
    },
    [dispatch],
  );


  return (
    <View style={styles.container}>
      <Text style={styles.header}>{profileSettingsScreen.HEADER}</Text>
      <TouchableOpacity
        style={[
          styles.card,
          {borderBottomWidth: 2, borderBottomColor: colors.lightGrey},
        ]}
        onPress={() => navigation.navigate(screens.PERSONAL_INFORMATION)}>
        <Text style={styles.title}>{locale.EditProfile}</Text>
        <Icon name={icons.CHEVRON} size={fontSize.xxl} tint={colors.grey} />
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.card}
        onPress={() => navigation.navigate(screens.SECURITY)}>
        <Text style={styles.title}>{profileSettingsScreen.SECURITY}</Text>
        <Icon name={icons.CHEVRON} size={fontSize.xxl} tint={colors.grey} />
      </TouchableOpacity>

      <View
        style={styles.card}
        onPress={() => navigation.navigate(screens.SECURITY)}>
        <Text style={styles.title}>{profileSettingsScreen.ENABLE_2FA}</Text>
        
        <Switch
          trackColor={{false: '#767577', true: 'green'}}
          thumbColor={isEnabled ? '#FFFFFF' : '#FFFFFF'}
          ios_backgroundColor="lightgray"
          onValueChange={toggleSwitch}
          value={isEnabled}
      />
      </View>
      {isLoading ? (
              <View
                style={{
                }}>
                <ActivityIndicator size = 'large' />
              </View>
            ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  card: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacings.lg,
  },
  title: {
    fontSize: fontSize.lg,
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
  header: {
    fontSize: fontSize.xl,
    color: colors.black,
    marginTop: spacings.lg,
    fontFamily: 'Gugi-Regular',
  },
  leftIconWrapper: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
  },
});

export default Index;
