import React from 'react';
import {StatusBar} from 'react-native';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import AuthNavigator from './AuthNavigator';
import UserNavigator from './UserNavigator';
import colors from '../assets/colors';
import {persistent} from '../assets/strings';

const AppNavigator = () => {
  const navigation = useNavigation();
  const {isLoggedIn} = useSelector(state => state?.auth?.loggedIn);

  const renderNavigator = React.useMemo(
    () => (isLoggedIn ? <UserNavigator /> : <AuthNavigator />),
    [isLoggedIn],
  );

  React.useEffect(() => {
    (async function () {
      try {
        const redirectNavigationEntry = await AsyncStorage.getItem(
          persistent.REDIRECT_NAVIGATION,
        );
        if (!(redirectNavigationEntry == null)) {
          const redirectNavigation = JSON.parse(redirectNavigationEntry);
          if (!(redirectNavigation?.path == null)) {
            await AsyncStorage.removeItem(persistent.REDIRECT_NAVIGATION);
            navigation.navigate(redirectNavigation?.path, {
              ...(redirectNavigation?.params ?? {}),
            });
          }
        }
      } catch (_) {}
    })();
  }, [isLoggedIn, navigation]);

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={colors.black} />
      {renderNavigator}
    </>
  );
};

export default AppNavigator;
