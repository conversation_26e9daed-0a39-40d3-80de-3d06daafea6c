/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const PersonalInformationLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item alignItems="center" justifyContent="center">
          <SkeletonPlaceholder.Item
            width={90}
            height={90}
            borderRadius={90}
            marginBottom={20}
          />
          <SkeletonPlaceholder.Item
            flexDirection="row"
            justifyContent="space-around"
            marginBottom={10}>
            <SkeletonPlaceholder.Item
              width={WIDTH / 5}
              height={20}
              marginRight={10}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              width={WIDTH / 5}
              height={20}
              marginLeft={10}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={WIDTH}
          height={3}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          width={50}
          alignSelf="flex-end"
          height={20}
          borderRadius={4}
          marginBottom={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.5}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH / 3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.5}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH / 3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.5}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH / 3}
            height={20}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          width={WIDTH}
          height={3}
          borderRadius={4}
          marginVertical={10}
        />
        <SkeletonPlaceholder.Item marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={20}
            borderRadius={4}
            marginVertical={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.9}
            height={80}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default PersonalInformationLoader;
