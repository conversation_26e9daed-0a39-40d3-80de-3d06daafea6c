import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import HomeListingsLoader from '../../components/Loaders/HomeListingsLoader';
import Icon from '../../components/Icon';
import {GET_CATEGORY, GET_ALL_POSTS} from '../../apollo/queries';
import {icons, images, screens} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import ActivityCard from '../../components/ActivityCard';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import {BrandPicker, AuthenticationDecisionPicker} from '../../ui';
import locale from '../../assets/locale.json';
import RefreshControl from '../../components/RefreshControl';
import FastImage from '../../components/FastImage';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {useInteractionManager} from '../../tools/hooks';

const {height: SCREEN_HEIGHT} = Dimensions.get('screen');

const LIMIT = 100;

const SingleCategoryScreen = ({route, navigation}) => {
  const categoryFromRoute = route?.params?.params;

  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const [brandIds, setBrandIds] = useState([]);
  const [selectedDecision, setSelectedDecision] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  const {data: categoryData, refetch: refetchCategory} = useQuery(
    GET_CATEGORY,
    {
      variables: {
        id: categoryFromRoute?.id,
      },
    },
  );

  const category = categoryData?.getCategory?.data ?? categoryFromRoute;

  const {
    data: categoryPosts,
    loading: categoryPostLoading,
    refetch: refetchPosts,
  } = useQuery(GET_ALL_POSTS, {
    variables: {
      limit: LIMIT, // TODO PAGINATION
      filters: {
        category_ids: [categoryFromRoute?.id],
        ...(brandIds?.length > 0
          ? {brand_ids: brandIds?.map(brand => brand?.id)}
          : {}),
        ...(selectedDecision == null ? {} : {decision: selectedDecision?.id}),
      },
      cacheId: APOLLO_CACHE_IDS.getPosts_POSTS_BY_CATEGORY,
    },
  });

  const handleBrandChange = _brandIds => {
    setBrandIds(_brandIds);
  };

  const onRefresh = React.useCallback(async () => {
    try {
      setRefreshing(true);
      await Promise.all([refetchCategory(), refetchPosts()]);
    } catch (_) {
    } finally {
      setRefreshing(false);
    }
  }, [refetchCategory, refetchPosts]);

  useFocusEffect(
    React.useCallback(() => {
      (async function () {
        try {
          await Promise.all([refetchCategory?.(), refetchPosts?.()]);
        } catch (_) {}
      })();
    }, [refetchCategory, refetchPosts]),
  );

  const renderHeader = React.useMemo(() => {
    return (
      <>
        <View style={styles.topBar}>
          <Text style={styles.titleStyle}>{category?.name}</Text>
          <View style={styles.brandLogoStyle}>
            <FastImage
              style={styles.logo}
              source={
                category?.image_url
                  ? {uri: category?.image_url}
                  : images.PLACEHOLDER
              }
            />
          </View>
        </View>
        <View style={styles.filters}>
          <BrandPicker
            onApply={handleBrandChange}
            onClear={() => handleBrandChange([])}
            selectedItems={brandIds}>
            {({openPicker}) => (
              <TouchableOpacity
                style={[
                  styles.pickerContainer,
                  {
                    backgroundColor:
                      brandIds?.length > 0 ? colors.primary : colors.lightGrey,
                  },
                ]}
                onPress={openPicker}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={[
                    styles.pickerText,
                    {
                      color: brandIds?.length > 0 ? colors.white : colors.black,
                    },
                  ]}>
                  {brandIds?.length
                    ? brandIds?.map(brand => brand?.name)?.join(', ')
                    : 'All Brands'}
                </Text>
                <Icon
                  name={icons.CHEVRON_DOWN}
                  tint={brandIds?.length ? colors.white : colors.black}
                  size={spacings.md}
                />
              </TouchableOpacity>
            )}
          </BrandPicker>
          <AuthenticationDecisionPicker onSelected={setSelectedDecision}>
            {({openPicker}) => (
              <TouchableOpacity
                style={[
                  styles.pickerContainer,
                  {
                    backgroundColor:
                      selectedDecision == null
                        ? colors.lightGrey
                        : colors.primary,
                  },
                ]}
                onPress={openPicker}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  style={[
                    styles.pickerText,
                    {
                      color:
                        selectedDecision == null ? colors.black : colors.white,
                    },
                  ]}>
                  {!(selectedDecision == null)
                    ? `Status: ${selectedDecision?.name}`
                    : 'Status: All'}
                </Text>
                <Icon
                  name={icons.CHEVRON_DOWN}
                  tint={
                    !(selectedDecision == null) ? colors.white : colors.black
                  }
                  size={spacings.md}
                />
              </TouchableOpacity>
            )}
          </AuthenticationDecisionPicker>
        </View>
      </>
    );
  }, [category, handleBrandChange, brandIds, selectedDecision]);

  const renderEmpty = React.useMemo(() => {
    return isInteractionsComplete ? (
      <View style={styles.emptyView}>
        <DefaultEmptyComponent
          lottieStyle={{width: 100, height: 100}}
          text={locale.NoPostsFound}
        />
      </View>
    ) : null;
  }, [isInteractionsComplete]);

  const renderItem = React.useCallback(
    ({item}) =>
      isInteractionsComplete ? (
        <TouchAwareButton
          onPress={() =>
            navigation.push(screens.SINGLE_LISTING, {params: item.id})
          }>
          <ActivityCard item={item} />
        </TouchAwareButton>
      ) : null,
    [navigation.push, isInteractionsComplete],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  const isLoading = categoryPostLoading && categoryPosts == null;

  return (
    <View style={styles.container}>
      <FlatList
        data={categoryPosts?.getPosts?.data?.edges}
        renderItem={renderItem}
        keyExtractor={extractKey}
        numColumns={2}
        columnWrapperStyle={styles.contentContainer}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={
          isLoading || !isInteractionsComplete ? <HomeListingsLoader /> : null
        }
        ListEmptyComponent={isLoading ? <HomeListingsLoader /> : renderEmpty}
        initialNumToRender={10}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  titleStyle: {
    color: colors.black,
    fontSize: fontSize.md,
    textTransform: 'capitalize',
    fontFamily: 'Gugi-Regular',
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  filters: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    marginVertical: spacings.md,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.grey,
  },
  pickerContainer: {
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 150,
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
    backgroundColor: colors.lightGrey,
    marginBottom: spacings.lg,
  },
  pickerText: {
    color: colors.black,
    fontSize: fontSize.md,
    textTransform: 'capitalize',
  },
  brandLogoStyle: {
    height: 30,
    width: 50,
    resizeMode: 'contain',
  },
  logo: {
    height: '90%',
    width: '90%',
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    marginTop: spacings.lg,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  modalSheetContainer: {
    paddingVertical: spacings.lg,
    marginHorizontal: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  checkedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 25,
    width: 25,
    borderRadius: 25,
    backgroundColor: colors.primary,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  uncheckedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 25,
    width: 25,
    borderRadius: 25,
    backgroundColor: colors.lightGrey,
    borderWidth: 2,
    borderColor: colors.grey,
  },
});

export default SingleCategoryScreen;
