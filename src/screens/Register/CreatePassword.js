import React, {useEffect, useCallback, useState, useRef} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
  Linking,
} from 'react-native';
import {Formik, Field} from 'formik';
import * as yup from 'yup';
import {useDispatch} from 'react-redux';
import {DOMAIN} from '@env';

import CustomInput from '../../components/AuthInput';
import {registerScreen, passwordErrors, icons} from '../../assets/strings';
import {styles} from './styles';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {SIGNUP} from '../../tools/api';
import {registerSuccess} from '../../redux/actions/auth';
import Icon from '../../components/Icon';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Layout from '../../layout';

const PRIVACY_POLICY_LINK = `${DOMAIN}/privacy-policy`;
const TERMS_AND_CONDITIONS_LINK = `${DOMAIN}/terms-and-conditions`;

const RegisterSchema = yup.object().shape({
  username: yup.string().required(passwordErrors.REQUIRED),
  password: yup
    .string()
    .matches(/(?=.*\d)/, passwordErrors.INCLUDE_NUMBER)
    .matches(
      /((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})/,
      passwordErrors.INCLUDE_SPECIAL_CHARACTER,
    )
    .matches(/^(\S+$)/, passwordErrors.NO_SPACES)
    .min(8, passwordErrors.MINIMUM_CHARACTERS)
    .required(passwordErrors.REQUIRED),
});

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: locale.Error,
    description: '',
  },
};

const CreatePassword = ({route}) => {
  const isMounted = useRef(true);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();

  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const item = route?.params?.params;

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  const handleSignUp = useCallback(
    async data => {
      setIsLoading(true);
      let credentials = {
        username: data.username,
        email: item?.email,
        code: item?.code,
        password: data.password,
      };

      try {
        const response = await SIGNUP(credentials);

        const res = response.data;

        if (res?.success) {
          const access_token = res.data?.access_token;
          const refresh_token = res.data?.refresh_token;
          const userId = res.data?.user_id || '';
          const roleId = res.data?.role_id ?? 2;
          const userEmail = item?.email;
          dispatch(
            registerSuccess({
              access_token,
              refresh_token,
              userId,
              roleId,
              userEmail,
            }),
          );
        } else {
          throw new Error();
        }
      } catch (e) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          data: {
            ...previousState.data,
            description:
              e?.response?.data?.message ??
              locale.SomethingWentWrongPleaseTryAgain,
          },
        }));
      } finally {
        setIsLoading(false);
      }
    },
    [dispatch, item?.code, item?.email],
  );

  const handleTermsAndConditions = async () => {
    try {
      await Linking.openURL(TERMS_AND_CONDITIONS_LINK);
    } catch (_) {}
  };
  const handlePrivacyPolicy = async () => {
    try {
      await Linking.openURL(PRIVACY_POLICY_LINK);
    } catch (_) {}
  };

  return (
    <Layout contentContainerStyle={styles.container}>
      <Formik
        validationSchema={RegisterSchema}
        initialValues={{
          password: '',
        }}
        onSubmit={async values => {
          if (isMounted.current === true) {
            handleSignUp(values);
          }
        }}>
        {({handleSubmit}) => (
          <>
            <View style={styles.loginContainer}>
              <View style={styles.titleWrapper}>
                <Text style={styles.titleStyle}>
                  {registerScreen.SCREEN3_TITLE}
                </Text>
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>{registerScreen.USERNAME}</Text>
                <View style={styles.passwordWrapper}>
                  <Field
                    style={styles.inputField}
                    component={CustomInput}
                    name="username"
                    keyboardType="default"
                  />
                </View>
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {registerScreen.SCREEN3_SUBTITLE}
                </Text>
                <Field
                  style={styles.inputField}
                  component={CustomPasswordComponent}
                  name="password"
                  keyboardType="default"
                />
              </View>
              <View
                style={{width: '100%', marginVertical: 20, marginBottom: 10}}>
                <Text style={styles.infoText}>
                  By continuing you agree to{' '}
                  <Text
                    style={styles.infoSubtext}
                    onPress={handleTermsAndConditions}>
                    Terms and Conditions
                  </Text>{' '}
                  and{' '}
                  <Text
                    style={styles.infoSubtext}
                    onPress={handlePrivacyPolicy}>
                    Privacy Policy
                  </Text>
                  .
                </Text>
              </View>
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}>
                {isLoading ? (
                  <ActivityIndicator color={colors.white} />
                ) : (
                  <Text style={styles.buttonText}>
                    {registerScreen.SCREEN3_BUTTON_LABEL}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </>
        )}
      </Formik>
      <InfoModal
        setVisible={() =>
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }))
        }
        popUp={popup}
      />
    </Layout>
  );
};

function CustomPasswordComponent({...props}) {
  const [passwordHidden, setPasswordHidden] = useState(true);

  const togglePassword = () => {
    setPasswordHidden(!passwordHidden);
  };

  return (
    <View style={styles.passwordWrapper}>
      <CustomInput {...props} secureTextEntry={passwordHidden}>
        <TouchableOpacity
          style={styles.togglePassword}
          onPress={togglePassword}>
          {
            <Icon
              name={passwordHidden ? icons.EYE_OPEN : icons.EYE_CLOSED}
              size={fontSize.xxxxl}
              tint={colors.black}
            />
          }
        </TouchableOpacity>
      </CustomInput>
    </View>
  );
}

export default CreatePassword;
