import React from 'react';
import {Text, View} from 'react-native';

import {singleListingScreen} from '../../assets/strings';
import {styles} from './styles';
import locale from '../../assets/locale.json';
import {fixedNumberTo} from '../../tools/utils';

const DescriptionCard = ({data, provenanceTaggingComponent = null}) => {
  return (
    <View style={styles.descriptionContainer}>
      <View style={styles.descTitleWrapper}>
        <Text style={styles.descTitle}>
          {singleListingScreen.DESCRIPTION_HEADER}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.BRAND}</Text>
        <Text style={styles.descValue}>
          {data?.getPost?.data?.brand?.name ?? 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.PIT_TO_PIT}</Text>
        <Text style={styles.descValue}>
          {data?.getPost?.data?.pit_to_pit_measurement?.toString?.()?.length
            ? `${fixedNumberTo(data?.getPost?.data?.pit_to_pit_measurement)}''`
            : 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.REAR_COLLAR}</Text>
        <Text style={styles.descValue}>
          {data?.getPost?.data?.collar_to_bottom_measurement?.toString?.()
            ?.length
            ? `${fixedNumberTo(
                data?.getPost?.data?.collar_to_bottom_measurement,
              )}''`
            : 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.LABEL_SIZE}</Text>
        <Text style={[styles.descValue]}>
          {data?.getPost?.data?.size?.name ?? 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.MATERIAL}</Text>

        <Text style={styles.descValue}>
          {data?.getPost?.data?.material?.name ?? 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.STITCHING}</Text>
        {data?.getPost?.data?.stitching ? (
          <Text style={styles.descValue}>
            {data?.getPost?.data?.stitching?.name ?? 'N/A'}
          </Text>
        ) : null}
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.CATEGORY}</Text>
        <Text style={styles.descValue}>
          {data?.getPost?.data?.category?.name ?? 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.DECADE}</Text>
        <Text style={[styles.descValue, {textTransform: 'none'}]}>
          {data?.getPost?.data?.from_decade?.name ?? 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <Text style={styles.descLabel}>{singleListingScreen.CONDITION}</Text>
        <Text style={styles.descValue}>
          {data?.getPost?.data?.condition?.name ?? 'N/A'}
        </Text>
      </View>
      <View style={styles.altDescCardWrapper}>
        <Text style={styles.altDescLabel}>{locale.RNNumber}</Text>
        <Text style={styles.descLabel}>
          {!(data?.getPost?.data?.rn_number == null)
            ? data?.getPost?.data?.rn_number
            : 'N/A'}
        </Text>
      </View>

      <View style={styles.altDescCardWrapper}>
        <Text style={styles.altDescLabel}>
          {singleListingScreen.CONDITION_DESCRIPTION}
        </Text>
        <Text style={styles.descLabel}>
          {data?.getPost?.data?.condition_description?.length
            ? data?.getPost?.data?.condition_description
            : 'N/A'}
        </Text>
      </View>
      <View style={[styles.altDescCardWrapper, {marginBottom: 60}]}>
        <Text style={styles.altDescLabel}>
          {singleListingScreen.PROVENANCE}
        </Text>
        <Text style={styles.descLabel}>
          {data?.getPost?.data?.provenance?.length
            ? data?.getPost?.data?.provenance
            : 'N/A'}
        </Text>
        {provenanceTaggingComponent}
      </View>
    </View>
  );
};

export default DescriptionCard;
