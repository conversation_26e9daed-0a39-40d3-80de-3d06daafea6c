import actionTypes from '../actionTypes';

//Add photo actions
export const addFrontImage = data => ({
  type: actionTypes.FRONT_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});
export const addBackImage = data => ({
  type: actionTypes.BACK_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});
export const addFrontTagImage = data => ({
  type: actionTypes.FRONT_TAG_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});
export const addBackTagImage = data => ({
  type: actionTypes.BACK_TAG_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});

export const addCloseUpFrontImage = data => ({
  type: actionTypes.CLOSEUP_FRONT_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});

export const addCloseUpBackImage = data => ({
  type: actionTypes.CLOSEUP_BACK_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});

export const addCopyrightImage = data => ({
  type: actionTypes.COPYRIGHT_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});

export const addLowerHemStitchingImage = data => ({
  type: actionTypes.LOWER_HEM_STITCHING_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});

export const addArmHemStitching = data => ({
  type: actionTypes.ARM_HEM_STITCHING_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});

export const addExtraImage = data => ({
  type: actionTypes.EXTRA_IMAGE,
  payload: {
    image: data.image,
    method: data.method,
  },
});

//Remove photo actions
export const removePhotoFromState = payload => ({
  type: actionTypes.REMOVE_PHOTO,
  payload,
});

//Clear photos from state
export const clearAllPhotosFromState = () => ({
  type: actionTypes.CLEAR_UPLOADS,
});

export const changeDp = payload => ({
  type: actionTypes.CHANGE_DP,
  payload,
});

export const uploadMethod = payload => ({
  type: actionTypes.UPLOAD_METHOD,
  payload,
});

export const showAuthenticationScore = payload => ({
  type: actionTypes.SHOW_SCORE,
  payload,
});
