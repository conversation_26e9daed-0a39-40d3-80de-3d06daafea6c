/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH / 2 - spacings.xl;

const ProfileLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            flexDirection="row"
            marginVertical={20}
            alignItems="center">
            <SkeletonPlaceholder.Item
              marginBottom={10}
              marginRight={10}
              width={60}
              height={60}
              borderRadius={60}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={10}
                width={60}
                height={20}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item flexDirection="row">
                <SkeletonPlaceholder.Item
                  marginBottom={10}
                  marginRight={10}
                  width={80}
                  height={20}
                  borderRadius={4}
                />
                <SkeletonPlaceholder.Item
                  marginBottom={10}
                  marginRight={10}
                  width={60}
                  height={20}
                  borderRadius={4}
                />
                <SkeletonPlaceholder.Item
                  marginBottom={10}
                  width={20}
                  height={20}
                  borderRadius={4}
                />
              </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>

        <SkeletonPlaceholder.Item
          marginBottom={10}
          width={WIDTH * 0.9}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={10}
          width={WIDTH * 0.9}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={10}
          width={WIDTH * 0.4}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={10}
          width={WIDTH * 0.2}
          height={10}
          borderRadius={4}
        />

        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-around"
          marginTop={20}>
          <SkeletonPlaceholder.Item alignItems="center">
            <SkeletonPlaceholder.Item
              marginBottom={10}
              width={25}
              height={15}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={10}
              width={50}
              height={15}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item alignItems="center">
            <SkeletonPlaceholder.Item
              marginBottom={10}
              width={25}
              height={15}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={10}
              width={50}
              height={15}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item alignItems="center">
            <SkeletonPlaceholder.Item
              marginBottom={10}
              width={25}
              height={15}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={10}
              width={50}
              height={15}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginTop={20}>
          <SkeletonPlaceholder.Item alignItems="center" justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={6}
                width={CARD_WIDTH}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                alignItems="center"
                flexDirection="row"
                marginBottom={6}>
                <SkeletonPlaceholder.Item
                  marginRight={10}
                  width={20}
                  height={20}
                  borderRadius={50}
                />
                <SkeletonPlaceholder.Item
                  width={CARD_WIDTH / 3}
                  height={15}
                  borderRadius={4}
                />
              </SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item alignItems="center" justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={6}
                width={CARD_WIDTH}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                alignItems="center"
                flexDirection="row"
                marginBottom={6}>
                <SkeletonPlaceholder.Item
                  marginRight={10}
                  width={20}
                  height={20}
                  borderRadius={50}
                />
                <SkeletonPlaceholder.Item
                  width={CARD_WIDTH / 3}
                  height={15}
                  borderRadius={4}
                />
              </SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item alignItems="center" justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={6}
                width={CARD_WIDTH}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                alignItems="center"
                flexDirection="row"
                marginBottom={6}>
                <SkeletonPlaceholder.Item
                  marginRight={10}
                  width={20}
                  height={20}
                  borderRadius={50}
                />
                <SkeletonPlaceholder.Item
                  width={CARD_WIDTH / 3}
                  height={15}
                  borderRadius={4}
                />
              </SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item alignItems="center" justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={6}
                width={CARD_WIDTH}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                alignItems="center"
                flexDirection="row"
                marginBottom={6}>
                <SkeletonPlaceholder.Item
                  marginRight={10}
                  width={20}
                  height={20}
                  borderRadius={50}
                />
                <SkeletonPlaceholder.Item
                  width={CARD_WIDTH / 3}
                  height={15}
                  borderRadius={4}
                />
              </SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default ProfileLoader;
