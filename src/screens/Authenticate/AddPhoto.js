import React, {useEffect, useCallback, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {useSelector} from 'react-redux';

import {
  communityVerificationData,
  expertVerificationData,
  tagVerificationData,
  ExpertFeedbackVerificationData,
  expertVerificationWithNFTData,
} from '../../assets/authenticationTypeData';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Uploader from './Uploader';
import {screens, authenticationTypeScreen} from '../../assets/strings';
import ActivityIndicator from '../../components/ActivityIndicator';
import withAuthenticationResubmission from './withAuthenticationResubmission';
import withUpgradeToExpertCertification from './withUpgradeToExpertCertification';

const AddPhoto = ({
  route,
  navigation,
  resubmissionData = null,
  isAuthenticationResubmission = false,
  authenticationId = null,
  isUpgradeToExpertAuthentication = false,
  upgradeData = null,
}) => {
  const [authenticationType, setAuthenticationType] = useState(null);
  const [error, setError] = useState(null);
  const [showError, setShowError] = useState(false);
  const [verificationData, setVerificationData] = useState('');

  const uploadsState = useSelector(state => state?.uploadMedia?.uploads);

  const isResubmission =
    isAuthenticationResubmission && authenticationId && resubmissionData;

  const isUpgradeToExpertAuthenticationValid =
    isUpgradeToExpertAuthentication && !(upgradeData == null);

  const _authenticationType = React.useMemo(
    () =>
      isResubmission
        ? resubmissionData?.authentication_type?.id
        : route?.params?.authenticationType,
    [
      isResubmission,
      resubmissionData?.authentication_type?.id,
      route?.params?.authenticationType,
    ],
  );

  useEffect(() => {
    if (_authenticationType) {
      setAuthenticationType(_authenticationType);
    }
  }, [_authenticationType]);

  const initializeData = useCallback(() => {
    switch (authenticationType) {
      case 1:
        return setVerificationData(communityVerificationData);
      case 2:
        return setVerificationData(expertVerificationData);
      case 3:
        return setVerificationData(expertVerificationWithNFTData);
      case 4:
        return setVerificationData(tagVerificationData);
      case 5:
        return setVerificationData(ExpertFeedbackVerificationData);
      default:
        return setVerificationData(communityVerificationData);
    }
  }, [authenticationType]);

  useEffect(() => {
    if (!(authenticationType == null)) {
      initializeData();
    }
  }, [authenticationType, initializeData]);

  const handleNavigation = React.useCallback(() => {
    navigation.navigate(screens.ADD_DESCRIPTION, {
      authType: authenticationType,
      resubmissionData,
      isAuthenticationResubmission,
      authenticationId,
      isUpgradeToExpertAuthentication,
      upgradeData,
    });
  }, [
    authenticationType,
    resubmissionData,
    isAuthenticationResubmission,
    authenticationId,
    isUpgradeToExpertAuthentication,
    upgradeData,
  ]);

  const navigatetoNextScreen = useCallback(() => {
    if (authenticationType === 1 || authenticationType === 5) {
      if (
        uploadsState.front_image === null ||
        uploadsState.closeup_front_image === null ||
        uploadsState.front_tag_image === null
      ) {
        setShowError(true);
        setError('Please upload all the required photos first');
      } else {
        setShowError(false);

        handleNavigation();
      }
      return;
    } else if (authenticationType === 2 || authenticationType === 3) {
      if (
        uploadsState.front_image === null ||
        uploadsState.closeup_front_image === null ||
        uploadsState.front_tag_image === null ||
        uploadsState.lower_hem_stitching_image === null ||
        uploadsState.arm_hem_stitching_image === null
      ) {
        setShowError(true);
        setError('Please upload all the required photos first');
      } else {
        setShowError(false);

        handleNavigation();
      }
      return;
    } else if (authenticationType === 4) {
      if (
        uploadsState.front_tag_image === null ||
        uploadsState.back_tag_image === null
      ) {
        setShowError(true);
        setError('Please upload all the required photos first');
      } else {
        setShowError(false);

        handleNavigation();
      }
      return;
    }
  }, [
    authenticationType,
    navigation,
    uploadsState.arm_hem_stitching_image,
    uploadsState.back_image,
    uploadsState.back_tag_image,
    uploadsState.closeup_back_image,
    uploadsState.closeup_front_image,
    uploadsState.front_image,
    uploadsState.front_tag_image,
    uploadsState.lower_hem_stitching_image,
    resubmissionData,
    isAuthenticationResubmission,
    authenticationId,
    handleNavigation,
  ]);

  const mapRemoteImageUrls = React.useCallback(
    item => {
      const value = item?.value?.endsWith('_image')
        ? item?.value?.slice(0, -6)
        : item?.value;

      return {
        defaultImageUrls: {
          remote:
            (isUpgradeToExpertAuthenticationValid
              ? upgradeData?.postData
              : resubmissionData
            )?.images_stripped?.[value] || null,
          local:
            (isUpgradeToExpertAuthenticationValid
              ? upgradeData?.postData
              : resubmissionData
            )?.images?.[value] || null,
        },
      };
    },
    [
      resubmissionData,
      isUpgradeToExpertAuthenticationValid,
      upgradeData?.postData,
    ],
  );

  return !verificationData || verificationData.length < 1 ? (
    <View style={styles.loadingContainer}>
      <ActivityIndicator color={colors.primary} />
    </View>
  ) : (
    <ScrollView
      style={styles.container}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}>
      <View style={styles.headerWrapper}>
        <Text style={styles.headerText}>{verificationData.header}</Text>
        <TouchableOpacity
          style={styles.examplesButton}
          onPress={() =>
            navigation.navigate(screens.EXAMPLES, {
              authType: authenticationType,
            })
          }>
          <Text style={styles.examplesButtonText}>
            {authenticationTypeScreen.VIEW_EXAMPLE}
          </Text>
        </TouchableOpacity>
      </View>
      <View style={[styles.headerWrapper, {marginBottom: spacings.lg}]}>
        <Text style={[styles.headerText, {fontSize: 15}]}>
          <Text style={styles.required}>
            {authenticationTypeScreen.ASTERISk}
          </Text>
          {authenticationTypeScreen.REQUIRED_PHOTOS}
        </Text>
      </View>
      <View style={styles.cardGroup}>
        {verificationData?.required?.map(item => (
          <Uploader
            uploadOptions={authenticationType}
            key={item?.id}
            item={item}
            required={true}
            {...(isResubmission || isUpgradeToExpertAuthenticationValid
              ? mapRemoteImageUrls(item)
              : {})}
          />
        ))}
      </View>
      {verificationData.optional ? (
        <>
          <View style={[styles.headerWrapper, {marginBottom: spacings.lg}]}>
            <Text style={[styles.headerText, {fontSize: 15}]}>
              {authenticationTypeScreen.OPTIONAL_PHOTOS}
            </Text>
          </View>
          <View style={styles.cardGroup}>
            {verificationData?.optional?.map(item => (
              <Uploader
                uploadOptions={authenticationType}
                key={item?.id}
                item={item}
                required={false}
                {...(isResubmission || isUpgradeToExpertAuthenticationValid
                  ? mapRemoteImageUrls(item)
                  : {})}
              />
            ))}
          </View>
        </>
      ) : null}
      <View style={styles.nextButtonWrapper}>
        {showError ? <Text style={styles.errorText}>{error}</Text> : null}
        <TouchableOpacity
          style={styles.nextButton}
          onPress={navigatetoNextScreen}>
          <Text style={styles.nextButtonText}>
            {authenticationTypeScreen.NEXT_BUTTON}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: spacings.lg,
    paddingHorizontal: spacings.lg,
    backgroundColor: colors.white,
  },
  cardGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: spacings.lg,
  },
  requiredContainer: {
    justifyContent: 'space-between',
    marginBottom: spacings.lg,
  },
  headerWrapper: {
    width: '100%',
    paddingTop: spacings.md,
  },
  headerText: {
    fontSize: fontSize.lg,
    lineHeight: 20,
    color: colors.black,
    fontFamily: 'Gugi-Regular',
  },
  examplesButton: {
    paddingVertical: spacings.md,
  },
  examplesButtonText: {
    textDecorationLine: 'underline',
    color: colors.primary,
  },
  nextButton: {
    height: 50,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
  },
  nextButtonText: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
  nextButtonWrapper: {
    marginBottom: spacings.xxxl,
    width: '100%',
  },
  required: {
    color: colors.warning,
  },
  errorText: {
    fontSize: fontSize.sm,
    color: colors.warning,
    margin: spacings.sm,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: colors.white,
    alignItems: 'center',
  },
});
export default withUpgradeToExpertCertification(
  withAuthenticationResubmission(AddPhoto),
);
