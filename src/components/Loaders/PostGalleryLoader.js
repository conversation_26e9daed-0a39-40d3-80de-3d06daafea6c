/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width} = Dimensions.get('window');

const WIDTH = width / 2;
const CARD_WIDTH = WIDTH - spacings.xl;

const PostGalleryLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          marginBottom={spacings.lg}>
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          marginBottom={spacings.lg}>
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          marginBottom={spacings.lg}>
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          marginBottom={spacings.lg}>
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={CARD_WIDTH}
            height={CARD_WIDTH / 1.5}
            marginBottom={10}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default PostGalleryLoader;
