import {from} from '@apollo/client';

import useToken from './useToken';
import refreshToken from './refreshToken';
import {customFetch} from '../../tools/axiosConfig';
// import http from './http'; // If we use uploadLink, no need to use httplink.

import {createUploadLink} from 'apollo-upload-client';
import {GRAPHQL_URL, GRAPHQL_URL_DEV} from '@env';

const uri =
  process.env.NODE_ENV === 'development' ? GRAPHQL_URL_DEV : GRAPHQL_URL;

const uploadLink = createUploadLink({
  uri,
  fetch: customFetch,
});

export default from([useToken, refreshToken, uploadLink]);
