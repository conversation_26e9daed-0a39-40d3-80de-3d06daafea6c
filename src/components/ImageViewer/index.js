import React from 'react';
import {Modal, Image} from 'react-native';
import Zoom<PERSON><PERSON>Viewer from 'react-native-image-zoom-viewer';
import Animated, {
  FadeInDown,
  Layout,
  ZoomInEasyUp,
} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import ActivityIndicator from '../ActivityIndicator';
import PLACEHOLDER from '../../assets/Images/imageplaceholder.jpg';
import {useWindowDimensions} from '../../tools/hooks';
import colors from '../../assets/colors';
import Icon from '../Icon';
import FastImage from '../FastImage';

export default function ImageViewer({
  images = [],
  visible = false,
  setVisible = () => null,
  index = 0,
}) {
  const {top} = useSafeAreaInsets();
  const {
    window: {width},
  } = useWindowDimensions();

  const dismissModal = React.useCallback(() => {
    setVisible(false);
  }, [setVisible]);

  return (
    <Modal visible={visible} transparent={true} onRequestClose={dismissModal}>
      <Animated.View style={[{flex: 1}]} entering={FadeInDown} layout={Layout}>
        <ZoomImageViewer
          index={index}
          imageUrls={images}
          enableSwipeDown
          onSwipeDown={dismissModal}
          enableImageZoom
          useNativeDriver
          loadingRender={() => <ActivityIndicator />}
          failImageSource={{
            url: Image.resolveAssetSource(PLACEHOLDER).uri,
            width,
            height: width,
          }}
          renderIndicator={() => null}
          saveToLocalByLongPress={false}
          renderImage={props => (
            <FastImage
              {...props}
              enableBlurHash={false}
              enableMountOpacityAnimation={false}
            />
          )}
        />

        <Animated.View
          entering={ZoomInEasyUp}
          style={[{position: 'absolute', top: 20 + top, right: 20}]}>
          <Icon
            name="cancel_x"
            size={15}
            tint={colors.black}
            containerStyle={{
              backgroundColor: 'white',
              padding: 10,
              borderRadius: 20,
            }}
            clickable
            onPress={dismissModal}
          />
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}
