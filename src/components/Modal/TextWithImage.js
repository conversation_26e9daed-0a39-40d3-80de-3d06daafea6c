import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import Icon from '../Icon';
import { icons } from '../../assets/strings';
import colors from '../../assets/colors';

const TextWithImage = ({ textBefore, imageUri, textAfter }) => {
  return (

    <View style={styles.textWithImageContainer}>
        <Text style={styles.subtitle}>{textBefore}</Text>
        
        <Icon  style={styles.image}
                name={
                  textBefore === 'Pass' ? icons.PASS_ICON : icons.CANCEL_X
                }
                tint={
                  textBefore === 'Pass' ? colors.green : colors.warning
                }
              />
        <Text style={styles.subtitle2}>{textAfter}</Text>
  </View>

 
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
  },
  textWithImageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: 15,
  },  
  image: {
    width: 20,
    height: 20,
    marginHorizontal: 10,
  },

  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Inter-Medium',
  },

  subtitle2: {
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Inter-Medium',
    marginHorizontal: 10,
  }
});

export default TextWithImage;
