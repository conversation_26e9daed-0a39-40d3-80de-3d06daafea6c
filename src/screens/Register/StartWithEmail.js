/* eslint-disable prettier/prettier */
import React, {useEffect, useState, useCallback, useRef} from 'react';
import {Text, View, TouchableOpacity, ActivityIndicator} from 'react-native';
import {Formik, Field} from 'formik';
import * as yup from 'yup';

import CustomInput from '../../components/AuthInput';
import {screens, registerScreen} from '../../assets/strings';
import {styles} from './styles';
import colors from '../../assets/colors';
import {SEND_REGISTRATION_CODE} from '../../tools/api';
import Layout from '../../layout';

const RegisterSchema = yup.object().shape({
  email: yup
    .string()
    .email('Invalid email address')
    .required('Please enter an email address'),
});

const StartWithEmail = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setError(null);
      isMounted.current = false;
    };
  }, []);

  const handleEmailConfirmation = useCallback(
    async data => {
      setIsLoading(true);
      try {
        const response = await SEND_REGISTRATION_CODE(data);
        const res = response.data;
        if (res.success === true) {
          setIsLoading(false);
          navigation.navigate(screens.EMAIL_VERIFICATION, {params: data});
        } else {
          setIsLoading(false);
        }
      } catch (e) {
        setError(e.response.data.message);
        setIsLoading(false);
      }
    },
    [navigation],
  );

  return (
    <Layout contentContainerStyle={styles.container}>
      <Formik
        validationSchema={RegisterSchema}
        initialValues={{
          email: '',
        }}
        onSubmit={async values => {
          if (isMounted.current === true) {
            handleEmailConfirmation(values);
          }
        }}>
        {({handleSubmit, isValid}) => (
          <>
            <View style={styles.loginContainer}>
              <View style={styles.titleWrapper}>
                <Text style={styles.titleStyle}>
                  {registerScreen.SCREEN1_TITLE}
                </Text>
              </View>
              <View style={styles.inputCard}>
                <Text style={[styles.inputLabel, {color: '#4B5563'}]}>
                  {registerScreen.SCREEN1_SUBTITLE}
                </Text>
                <Field
                  style={styles.inputField}
                  component={CustomInput}
                  name="email"
                  keyboardType="email-address"
                />
              </View>
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}>
                {isLoading ? (
                  <ActivityIndicator color={colors.white} />
                ) : (
                  <Text style={styles.buttonText}>
                    {registerScreen.SCREEN1_BUTTON_LABEL}
                  </Text>
                )}
              </TouchableOpacity>
              {error ? (
                <View style={styles.errorWrapper}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : null}
            </View>
          </>
        )}
      </Formik>
    </Layout>
  );
};

export default StartWithEmail;
