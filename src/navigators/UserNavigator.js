import React from 'react';
import { Platform } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { TransitionPresets } from '@react-navigation/stack';

import ExpertFeedbackOptions from '../screens/Authenticate/ExpertFeedbackOptions';
import Votes from '../screens/SingleListing/Votes';
import AuthenticationAddCard from '../screens/Authenticate/AuthenticationAddCard';
import BrandPicker from '../screens/Authenticate/BrandPicker';
import PostGallery from '../screens/SingleListing/PostGallery';
import EditInformation from '../screens/Profile/EditInformation';
import SearchScreen from '../screens/SearchScreen';
import FollowersListing from '../screens/UserProfile/FollowersLIsting';
import FollowingListing from '../screens/UserProfile/FollowingListing';
import AllListings from '../screens/AllActivity/AllListings';
import ForceLogout from '../screens/ForceLogout';
import Examples from '../screens/Authenticate/Examples';
import Comments from '../screens/SingleListing/Comments';
import PublicProfiles from '../screens/UserProfile/PublicProfiles';
import ExpertProfile from '../screens/OurExperts/ExpertProfile';
import Messenger from '../screens/Messenger';
import ChatScreen from '../screens/UserProfile/ChatScreen';
import AuthSuccessful from '../screens/Authenticate/AuthSuccessful';
import CheckOut from '../screens/Authenticate/CheckOut';
import EditCreditCard from '../screens/Billing/EditCreditCard';
import PaymentMethod from '../screens/Authenticate/PaymentMethod';
import AddDescription from '../screens/Authenticate/AddDescription';
import PersonalInformation from '../screens/Profile/PersonalInformation';
import Security from '../screens/Profile/Security';
import SingleListing from '../screens/SingleListing';
import UserProfile from '../screens/UserProfile';
import Authenticate from '../screens/Authenticate/Authenticate';
import AllCategories from '../screens/AllBrandsAndCategories/AllCategories';
import DrawNavigator from './DrawNavigator';
import SingleBrandScreen from '../screens/AllBrandsAndCategories/SingleBrandScreen';
import SingleCategoryScreen from '../screens/AllBrandsAndCategories/SingleCategoryScreen';
import AllBrandAndCategories from '../screens/AllBrandsAndCategories';
import AddPhoto from '../screens/Authenticate/AddPhoto';
import PostLikes from '../screens/PostLikes';
import { fa2ValidateCodeScreen, icons, navLabels, screens } from '../assets/strings';
import colors from '../assets/colors';
import { fontSize } from '../assets/font';
import NotificationMessages from '../screens/Notifications/NotificationMessages';
import AddCard from '../screens/Billing/AddCard';
import PostImageComment from '../screens/SingleListing/PostImageComment';
import Icon from '../components/Icon';
import withDeviceSync from '../components/HOC/withDeviceSync';
import locale from '../assets/locale.json';
import FA2ValidateCode from '../screens/FA2ValidateCode';
import AuthenticationGrid from '../screens/MyAuthentications/AiResult2';
import QRCodeScreen from '../screens/QRCode';
import AuthenticationGrid_old from '../screens/MyAuthentications/AiResult2_old';
import CameraScan from '../screens/AIScan/CameraScan';
//import QRCodeScreen from '../screens/MyAuthentications/QRCodeScreen';

const Stack = createStackNavigator();
const isIOS = Platform.OS == 'ios';

const UserNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.black,
        },
        ...(isIOS ? { headerBackTitle: 'Back' } : ''),
        headerTintColor: colors.white,
        headerTitleAlign: 'center',
        gestureEnabled: true,
        ...(isIOS
          ? TransitionPresets.SlideFromRightIOS
          : TransitionPresets.RevealFromBottomAndroid),
      }}>
      <Stack.Screen
        name={screens.DRAW_NAVIGATOR}
        component={DrawNavigator}
        options={{
          headerShown: false,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.ALLBRAND_CATEGORIES}
        component={AllBrandAndCategories}
        options={{
          headerTitle: navLabels.ALLBRAND_CATEGORIES,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name="test"
        component={AllCategories}
        options={{
          headerTitle: navLabels.ALLBRAND_CATEGORIES,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.SINGLE_BRAND_SCREEN}
        component={SingleBrandScreen}
        options={{
          headerTitle: 'Brands',
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.ADD_CARD}
        component={AddCard}
        options={{
          headerTitle: navLabels.ADD_CARD,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.SINGLE_CATEGORY_SCREEN}
        component={SingleCategoryScreen}
        options={{
          headerTitle: 'Categories',
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.AUTHENTICATE}
        component={Authenticate}
        options={{
          animationEnabled: true,
          presentation: 'modal',
          headerTitle: navLabels.AUTHENTICATE,
          headerBackImage: () => (
            <Icon
              style={{
                marginLeft: 20,
              }}
              containerStyle={{ padding: 10 }}
              name={icons.CANCEL_X}
              tint={colors.white}
              size={fontSize.lg}
            />
          ),
          ...(isIOS ? { headerBackTitleVisible: false } : {}),
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
          ...TransitionPresets.RevealFromBottomAndroid,
        }}
      />

      <Stack.Screen
        name={screens.AI_SCAN_SCREEN}
        component={CameraScan}
        options={{
          animationEnabled: true,
          presentation: 'modal',
          headerTitle: navLabels.SCAN_AI,
          headerBackImage: () => (
            <Icon
              style={{
                marginLeft: 20,
              }}
              containerStyle={{ padding: 10 }}
              name={icons.CANCEL_X}
              tint={colors.white}
              size={fontSize.lg}
            />
          ),
          ...(isIOS ? { headerBackTitleVisible: false } : {}),
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
          ...TransitionPresets.RevealFromBottomAndroid,
        }}
      />

      <Stack.Screen
        name={screens.ADD_PHOTOS}
        component={AddPhoto}
        options={{
          animationEnabled: true,
          gestureEnabled: true,
          ...TransitionPresets.SlideFromRightIOS,
          presentation: 'modal',
          headerTitle: navLabels.ADD_PHOTOS,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.USER_PROFILE}
        component={UserProfile}
        options={{
          headerTitle: navLabels.USER_PROFILE,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.EDIT_INFORMATION}
        component={EditInformation}
        options={{
          headerTitle: navLabels.EDIT_INFORMATION,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.SINGLE_LISTING}
        component={SingleListing}
        options={{
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />

      <Stack.Screen
        name={screens.AIRESULT}
        component={AuthenticationGrid_old}
        options={{
          headerTitle: navLabels.AI_LENS_BETA,
          headerTitleStyle: {
            //textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />

<Stack.Screen
        name={screens.AITAGRESULT}
        component={AuthenticationGrid_old}
        options={{
          headerTitle: navLabels.SCAN_AI,
          headerTitleStyle: {
            //textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      

      <Stack.Screen
        name={screens.QR_CODE_SCREEN}
        component={QRCodeScreen}
        options={{
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />

      <Stack.Screen
        name={screens.PERSONAL_INFORMATION}
        component={PersonalInformation}
        options={{
          headerTitle: locale.EditProfile,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.SECURITY}
        component={Security}
        options={{
          headerTitle: navLabels.SECURITY,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />

      <Stack.Screen
        name={screens.FA2_VALIDATE_CODE_FROM_PROFILE}
        component={FA2ValidateCode}
        options={{
          headerTitle: fa2ValidateCodeScreen.HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.ADD_DESCRIPTION}
        component={AddDescription}
        options={{
          headerTitle: navLabels.ADD_DESCRIPTION,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.EXPERT_FEEDBACK_OPTIONS}
        component={ExpertFeedbackOptions}
        options={{
          headerTitle: navLabels.EXPERT_FEEDBACK_OPTIONS,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.POST_GALLERY}
        component={PostGallery}
        options={{
          headerTitle: navLabels.POST_GALLERY,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.POST_IMAGE_COMMENT}
        component={PostImageComment}
        options={{
          headerTitle: navLabels.POST_IMAGE_COMMENT,
          animationEnabled: true,
          gestureEnabled: true,
          ...TransitionPresets.SlideFromRightIOS,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.PAYMENT_METHOD}
        component={PaymentMethod}
        options={{
          headerTitle: navLabels.PAYMENT_METHOD,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.BRAND_PICKER}
        component={BrandPicker}
        options={{
          headerTitle: navLabels.BRAND_PICKER,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
          headerBackTitle: ' ',
          headerBackImage: ({ tintColor }) => (
            <Icon
              name="cancel_x"
              tint={tintColor}
              size={15}
              style={{ marginLeft: 10 }}
              containerStyle={{ padding: 10 }}
            />
          ),
        }}
      />
      <Stack.Screen
        name={screens.AUTHENTICATION_ADD_CARD}
        component={AuthenticationAddCard}
        options={{
          headerTitle: navLabels.AUTHENTICATION_ADD_CARD,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.EDIT_CREDIT_CARD}
        component={EditCreditCard}
        options={{
          headerTitle: navLabels.EDIT_CREDIT_CARD,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.CHECKOUT}
        component={CheckOut}
        options={{
          headerTitle: navLabels.CHECKOUT,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.AUTH_SUCCESSFUL}
        component={AuthSuccessful}
        options={{
          headerTitle: navLabels.AUTH_SUCCESSFUL,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
          headerLeft: null,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name={screens.FOLLOWING_LISTING}
        component={FollowingListing}
        options={{
          headerTitle: navLabels.FOLLOWING_LISTING,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.FOLLOWERS_LISTING}
        component={FollowersListing}
        options={{
          headerTitle: navLabels.FOLLOWERS_LISTING,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.CHAT_SCREEN}
        component={ChatScreen}
        options={{
          headerTitle: navLabels.CHAT_SCREEN,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.MESSENGER}
        component={Messenger}
        options={{
          headerTitle: locale.Messages,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
          ...TransitionPresets.SlideFromRightIOS,
        }}
      />
      <Stack.Screen
        name={screens.EXPERTS_PROFILE}
        component={ExpertProfile}
        options={{
          headerTitle: ' ',
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.VOTES}
        component={Votes}
        options={{
          headerTitle: navLabels.VOTES,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.PUBLIC_PROFILES}
        component={PublicProfiles}
        options={{
          headerTitle: navLabels.PUBLIC_PROFILES,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />

      <Stack.Screen
        name={screens.EXAMPLES}
        component={Examples}
        options={{
          headerTitle: navLabels.EXAMPLES,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.ALL_LISTINGS}
        component={AllListings}
        options={{
          headerTitle: locale.AllLegitChecks,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.SEARCH_SCREEN}
        component={SearchScreen}
        options={{
          headerShown: false,
          gestureEnabled: false,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
          ...(isIOS
            ? TransitionPresets.RevealFromBottomAndroid
            : TransitionPresets.BottomSheetAndroid),
        }}
      />
      <Stack.Screen
        name={screens.NOTIFICATION_MESSAGES}
        component={NotificationMessages}
        options={{
          headerTitle: navLabels.NOTIFICATION_MESSAGES,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
          ...TransitionPresets.SlideFromRightIOS,
        }}
      />
      <Stack.Screen
        name={screens.FORCE_LOGOUT}
        component={ForceLogout}
        options={{
          gestureEnabled: false,
          unmountOnBlur: true,
          ...TransitionPresets.ModalSlideFromBottomIOS,
          headerTitle: '',
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.COMMENTS}
        component={Comments}
        options={{
          animationEnabled: true,
          gestureEnabled: true,
          ...TransitionPresets.SlideFromRightIOS,
          presentation: 'modal',
          headerTitle: navLabels.COMMENTS,
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
        }}
      />
      <Stack.Screen
        name={screens.POST_LIKES}
        component={PostLikes}
        options={{
          headerTitleStyle: {
            textTransform: 'capitalize',
            fontFamily: 'Gugi-Regular',
          },
          ...TransitionPresets.SlideFromRightIOS,
        }}
      />
    </Stack.Navigator>
  );
};

export default withDeviceSync(UserNavigator);
