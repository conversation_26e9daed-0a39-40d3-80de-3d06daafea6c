/* eslint-disable prettier/prettier */
import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
} from 'react-native';

import styles from './styles';
import colors from '../../assets/colors';
import BaseLayout from '../../layout';
import { screens, fa2ValidateCodeScreen, apiEndPoint, persistent } from '../../assets/strings';
import { SEND_2FA_VERIFICATION_CODE, VALIDATE_2FA_LOGIN, VALIDATE_CODE } from '../../tools/api';
import { spacings } from '../../assets/spacing';
import { useDispatch } from 'react-redux';
import { loginSuccess } from '../../redux/actions/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

export default function FA2ValidateCode({ route, navigation }) {
  const [isLoading, setIsLoading] = useState(false);
  const [code, setCode] = useState(null);
  const [error, setError] = useState(null);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
  const isMounted = useRef(true);
  const item = route.params.params;
  const dispatch = useDispatch();

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setError(null);
      isMounted.current = false;
    };
  }, []);

  const handle2FAVerification = useCallback(async () => {
    setIsLoading(true);
    let credentials = {
      email: item.email,
      code: code,
    };

    try {
      const response = await VALIDATE_2FA_LOGIN(credentials);
      const res = response.data;
      if (res?.success === true) {
        setIsLoading(false);

        const access_token = res?.data?.access_token;
        const refresh_token = res?.data?.refresh_token;
        const userId = res?.data?.user_id || '';
        const roleId = res?.data?.role_id ?? 2;
        const is2FA = true;
        const userEmail = item.email;

        dispatch(
          loginSuccess({
            access_token,
            refresh_token,
            userId,
            roleId,
            is2FA,
            userEmail,
          }),
        );

      } else {
        setError(res?.error?.code);
        setIsLoading(false);
      }
    } catch (e) {
      setIsLoading(false);
      setError(e?.response?.data?.message ?? 'Something went wrong...');
    }
  }, [code, item.email, navigation]);

  const enable2FACode = useCallback(async () => {
    setIsLoading(true);    
    try {
      const response = await SEND_2FA_VERIFICATION_CODE(code);
      const res = response.data;
      if (res?.success === true) {
        setIsLoading(false);        
        await AsyncStorage.setItem(persistent.IS_2FA, "1");
        
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: false,
          data: {
            title: locale.Success,
            description: res?.message ?? "Enabled",
          },
        }));

      } else {
        setError(res?.error?.code);
        setIsLoading(false);
      }
    } catch (e) {
      setIsLoading(false);
      setError(e?.response?.data?.message ?? 'Something went wrong...');
    }
  }, [code, item.email, navigation]);
  return (
    <BaseLayout contentContainerStyle={styles.container}>
      <View style={styles.titleWrapper}>
        <Text style={styles.titleStyle}>{fa2ValidateCodeScreen.TITLE}</Text>
      </View>
      <View style={styles.titleWrapper}>
        <Text style={styles.inputLabel}>{fa2ValidateCodeScreen.SUBTITLE}</Text>
      </View>
      <View style={styles.inputWrapper}>
        <Text style={styles.label}>{fa2ValidateCodeScreen.CODE}</Text>
        <TextInput
          value={code}
          onChangeText={setCode}
          placeholder={fa2ValidateCodeScreen.PLACEHOLDER}
          selectionColor={colors.grey}
          placeholderTextColor={colors.grey}
          style={styles.inputStyles}
          keyboardType="numeric"
          onSubmitEditing={item.isFromProfile ? enable2FACode : handle2FAVerification}
        />
      </View>
      <TouchableOpacity style={styles.button} onPress={item.isFromProfile ? enable2FACode : handle2FAVerification}>
        {isLoading ? (
          <ActivityIndicator color={colors.white} />
        ) : (
          <Text style={styles.buttonText}>
            {item.isFromProfile ? fa2ValidateCodeScreen.ENABLE2FA_BUTTON : fa2ValidateCodeScreen.NEXT_BUTTON }
          </Text>
        )}
      </TouchableOpacity>
      <View style={{ marginTop: spacings.sm }} />
      {error ? (
        <View style={styles.errorWrapper}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : null}

    <InfoModal
        setVisible={() => {
          if (popup.state && !popup.isError) {
            navigation.goBack();
          }
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
    </BaseLayout>
  );
}

