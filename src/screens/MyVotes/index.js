import React, {useState, useCallback} from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  Dimensions,
  RefreshControl,
} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';

import Icon from '../../components/Icon';
import MyVotesLoader from '../../components/Loaders/MyVotesLoader';
import {GET_MY_VOTED_POSTS} from '../../apollo/queries';
import {icons, screens} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import MyStuffsCard from '../../components/MyStuffsCard';
import {fontSize} from '../../assets/font';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import InfoModal from '../../components/Modal/InfoModal';
import ListActionRow from '../../components/ListActionRow';
import BottomSheet from '../../components/BottomSheet';
import {DELETE_POST_VOTE} from '../../apollo/mutations';
import locale from '../../assets/locale.json';

const {height: SCREEN_HEIGHT} = Dimensions.get('screen');
const SHEET_INITIAL_STATE = {
  visible: false,
  voteId: null,
};

const Index = ({navigation}) => {
  const scrollRef = React.useRef();

  const [sheetState, setSheetState] = React.useState(SHEET_INITIAL_STATE);

  const [refreshing, setRefreshing] = useState(false);
  const {data, refetch, loading, error} = useQuery(GET_MY_VOTED_POSTS, {
    variables: {limit: 100},
  });

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch?.();
    } catch (_) {
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('tabPress', e => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (error) {}
    });

    return unsubscribe;
  }, [navigation, refetch]);

  const resetSheet = () => {
    setSheetState(SHEET_INITIAL_STATE);
  };

  const handleMenuPressed = item => {
    setSheetState({
      visible: true,
      voteId: item?.voteId,
    });
  };

  const renderItem = React.useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          onPress={() =>
            navigation.push(screens.SINGLE_LISTING, {params: item.id})
          }
          disableLongPressAnimation={item?.is_voting_closed}
          {...(!item?.is_voting_closed && item?.can_delete_vote
            ? {
                onLongPress: handleMenuPressed.bind(null, {
                  voteId: item?.my_vote?.id,
                }),
              }
            : {})}>
          <MyStuffsCard item={item} onMenuPressed={handleMenuPressed} />
        </TouchAwareButton>
      );
    },
    [navigation.push, handleMenuPressed],
  );

  const renderSheet = React.useMemo(
    () => (
      <BottomSheet
        visible={sheetState.visible}
        setVisible={resetSheet}
        automaticallyAdjustHeight
        key={sheetState.authenticationId}>
        <View style={{minHeight: 50, padding: 20}}>
          <DeleteButton
            voteId={sheetState.voteId}
            resetSheet={resetSheet}
            refetch={refetch}
          />
        </View>
      </BottomSheet>
    ),
    [resetSheet, sheetState, refetch],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  if (loading || error) {
    return <MyVotesLoader />;
  }
  return (
    <View style={styles.container}>
      <FlatList
        data={data?.getMyVotedPosts?.data?.edges}
        style={styles.contentContainer}
        renderItem={renderItem}
        keyExtractor={extractKey}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyView}>
            <Icon name={icons.UPLOAD_PHOTO} size={70} tint={colors.darkGrey} />
            <Text style={styles.emptyViewText}>
              {locale.YouHaveNotVotedOnAnyPosts}
            </Text>
          </View>
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            progressBackgroundColor={colors.lightGrey}
            colors={[colors.primary, colors.black]}
            tintColor={colors.primary}
            onRefresh={onRefresh}
          />
        }
        initialNumToRender={10}
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
      />
      {renderSheet}
    </View>
  );
};

const POPUP_INITIAL_STATE = {
  visible: false,
  message: '',
};

function DeleteButton({
  voteId = null,
  resetSheet = () => null,
  refetch = () => null,
}) {
  const [deleteVoteOfPost] = useMutation(DELETE_POST_VOTE);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [loading, setLoading] = React.useState(false);

  const handleDeleteVote = React.useCallback(async () => {
    setLoading(true);
    try {
      const {data} = await deleteVoteOfPost({
        variables: {id: voteId},
      });
      const isSuccessful = data?.deleteVoteOfPost?.success;
      const message = data?.deleteVoteOfPost?.message;
      if (isSuccessful) {
        await refetch?.();
        resetSheet();
      } else {
        if (message?.length > 0) {
          setPopup({
            visible: true,
            message: message,
          });
        } else {
          throw new Error();
        }
      }
    } catch (error) {
      setPopup({
        visible: true,
        message: locale['SomethingWentWrong.'],
      });
    } finally {
      setLoading(false);
    }
  }, [voteId, deleteVoteOfPost, resetSheet]);

  return (
    <>
      <ListActionRow
        iconName="delete"
        iconContainerStyle={{backgroundColor: colors.red}}
        actionText={locale.DeleteVote}
        isLoading={loading}
        disabled={loading}
        onPress={handleDeleteVote}
      />
      <InfoModal
        setVisible={() => {
          setPopup(POPUP_INITIAL_STATE);
        }}
        popUp={{
          isError: true,
          state: popup.visible,
          data: {
            title: locale.Error,
            description: popup.message,
          },
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  contentContainer: {
    paddingTop: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});

export default Index;
