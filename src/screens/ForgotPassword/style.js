/* eslint-disable prettier/prettier */
import {StyleSheet, Dimensions, Platform} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

const {width: WIDTH} = Dimensions.get('window');

const isIOS = Platform.OS == 'ios';

export const styles = StyleSheet.create({
  srollViewWrapper: {
    flexGrow: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
  },
  loginContainer: {
    width: WIDTH * 0.9,
    backgroundColor: colors.white,
    flex: 1,
    marginVertical: spacings.lg,
  },
  submitButton: {
    backgroundColor: colors.primary,
    height: spacings.xxxl,
    marginTop: spacings.lg,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacings.sm,
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSize.md,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  titleWrapper: {
    width: '100%',
    marginBottom: spacings.lg,
  },
  titleStyle: {
    fontSize: fontSize.lg,
    fontFamily: 'Gugi-Regular',
    color: colors.black,
  },
  inputCard: {
    marginVertical: spacings.md,
  },
  inputLabel: {
    width: '100%',
    fontSize: fontSize.md,
    color: colors.black,
    marginBottom: 5,
  },
  inputField: {
    width: '100%',
    paddingLeft: spacings.md,
    color: colors.black,
    backgroundColor: colors.white,
    borderRadius: spacings.sm,
    fontSize: fontSize.md,
    justifyContent: 'flex-end',
    // ADDED
    ...(isIOS ? {padding: 15} : {}),
  },
  passwordWrapper: {
    flexDirection: 'row',
    width: '100%',
  },
  forgotPasswordText: {
    paddingVertical: spacings.md,
    fontSize: fontSize.sm,
    color: colors.black,
    textDecorationLine: 'underline',
  },
  forgotPasswordSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacings.sm,
    width: '100%',
  },
  errorText: {
    fontSize: 9,
    color: colors.warning,
    width: '100%',
    position: 'absolute',
  },
  errorWrapper: {
    width: '100%',
    marginTop: 5,
  },
});
