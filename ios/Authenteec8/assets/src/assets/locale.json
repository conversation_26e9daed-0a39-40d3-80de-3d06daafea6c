{"calendarPermissionIsRequired": "Calendar access is required.", "SomethingWentWrong.": "Something went wrong.", "SomethingWentWrongPleaseTryAgain": "Something went wrong. Please try again.", "calendarPermissionSteps": "Please allow calendar access to continue.", "changeYourPassword": "Change your password", "profile": "Profile", "dashboard": "Dashboard", "notification": "Notification", "cancel": "Cancel", "allowCameraPermissionToContinue": "Camera Permission is required.", "cameraPermissionSteps": "Go to Camera permissions in the settings and turn it on.", "storagePermissionIsRequired": "Storage Permission is required", "storagePermissionSteps": "Go to Storage permissions and turn it on.", "storagePermissionStepsIOS": "Go to settings and allow Photos permission", "allow": "Allow", "deny": "<PERSON><PERSON>", "locationServiceRequired": "Location service is required.", "appWantsToEnableLocation": "This app wants to enable your device location.", "locationPermissionIsRequired": "Location Permission is required.", "locationPermissionSteps": "Go to location Permissions and turn it on.", "takeMeThere": "Take me there", "selectFrom": "Selection option", "SelectFrom": "Select From", "camera": "Camera", "gallery": "Gallery", "CouldNotFetchRecentBrands": "Could not fetch recent brands...", "CouldNotFetchRecentCategories": "Could not fetch recent categories...", "CouldNotFetchLatestListing": "Could not fetch recent listings...", "TypeToSearch": "Type to search", "LooksLikeItsEmpty": "Looks like its empty...", "CouldNotFetchFollowingActivity": "Could not fetch following activity...", "VerificationCodeSentToYourInbox": "Verification code sent to your inbox.", "OK": "OK", "SessionExpired": "Session Expired", "LoggingYouOut": "Logging you out.", "Clear": "Clear", "Apply": "Apply", "NoPostsFound": "No posts found.", "TypeSomething": "Type Something...", "Search": "Search", "NoNetworkConnectionAvailable": "No network available", "CommunityVoted": "Community voted", "ExpertCertified": "Expert certified", "ExpertCertifiedNFT": "Expert certified NFT", "TagOnly": "Tag only", "ExpertVoted": "Expert Voted", "Logout": "Logout", "DeleteAccount": "Delete Account", "AreYouSureYouWantToLogout": "Are you sure you want to logout?", "AreYouSureYouWantToDelete": "Are you sure you want to delete your account?", "Yes": "Yes", "No": "No", "ExpertFeedback": "Expert Fe<PERSON>back", "UploadFailed": "Upload failed!", "NoConnectionAvailable": "No Connection Available", "Max": "Max", "fileSizeAllowed": "file size allowed.", "Error": "Error", "MakeSureToFillAllTheRequiredFields": "Make sure to fill all the required fields.", "BrandIsRequired": "Brand is required.", "PitToPitMeasurementIsRequired": "Pit to Pit Measurement is required.", "RealCollarToBottomMeasurementIsRequired": "Rear Collar to Bottom Measurement is required.", "DecadeIsRequired": "Decade is required.", "CategoryIsRequired": "Category is required.", "StitchingIsRequired": "Stitching is required.", "NameIsRequired": "Name is required", "ConditionIsRequired": "Condition is required.", "LabelSizeIsRequired": "Label Size is required.", "MaterialIsRequired": "Material is required.", "ConditionDescriptionIsRequired": "Condition description is required.", "AddWallet": "Add Wallet", "PleaseAddYourCryptoWallet...": "Please add your crypto address before proceeding with NFT authentications.", "ConnectCryptoWallet": "Connect Crypto Wallet", "Skip": "<PERSON><PERSON>", "AddPaymentMethod": "Add payment method", "SelectPaymentMethod": "Select Payment Method", "Delete": "Delete", "DeleteAuthentication": "Delete Authentication", "Success": "Success", "YourRequestToTransferNFTHasBeenReceived": "Your request to transfer NFT has been received.", "ClaimNFT": "Claim NFT", "Featured": "Featured", "Profile": "Profile", "Notifications": "Notifications", "Billing": "Billing", "ConnectWallet": "Connect Wallet", "OurExperts": "Our Experts", "HowItWorks": "How it Works", "CouldNotFetchChatList": "Could not fetch chat list.", "DeleteChat": "Delete Chat", "YouHaveNoChat": "You have no chat.", "Messages": "Messages", "Prev": "Prev", "Add": "Add", "Change": "Change", "AddYourPersonalInformation": "Add Your Profile Information", "InvalidEmailFormat": "Invalid Email Format", "EmailIsRequired": "Email is required", "UsernameIsRequired": "Username is required.", "NoActivityYet": "No activity yet.", "PasswordChangedSuccessfully": "Password Changed Successfully.", "AreYouSureYouWantToDeleteThisCard": "Are you sure you want to delete this card?", "DeleteCard": "Delete Card", "CardUpdatedSuccessfully": "Card updated successfully.", "CardDeclined": "Card Declined.", "AllExpertise": "All Expertise", "NoExpertsFound": "No Experts found", "PostNotFound": "Post not found.", "ThePostYouAreTryingToFindMayNotBeActiveOrIsDeleted": "The post you are trying to view may not be active or deleted by author.", "CommunityVotes": "Community Votes", "AverageAppraisal": "Average Appraisal", "ExpertsNotVoting": "Experts not Voting", "ExpertsVote": "Expert's Vote", "UserHasNoActivityYet": "User has no activity yet.", "Follow": "Follow", "Following": "Following", "CouldNotFetchNotifications": "Could not fetch notifications.", "YouHaveNoNotificationsForNow": "You have no notifications for now.", "RejectionReason": "Rejection Reason", "TapToResubmit": "Tap to resubmit", "hasVoted": "has voted", "likedYourPost": "liked your post", "onYourPost": "on your post", "YourAuthentication": "Your authentication", "hasBeenAccepted": "has been accepted", "hasBeenRejected": "has been rejected", "hasCommentedOnYourPost": "has commented on your post", "hasMentionedYouOnAPost": "has mentioned you on a post", "image": "image", "hasStartedFollowingYou": "has started following you", "UpgradeToExpertCertification": "Upgrade to Expert Certification", "ChooseTheCertificationYouWant": "Choose the certification you want.", "RNNumber": "RN Number", "Pending": "Pending", "PersonalInformation": "Personal Information", "EditProfile": "Edit Profile", "Website": "Website", "Expert": "Expert", "ExpertsProfile": "Expert's Profile", "MyProfile": "My Profile", "YouHaveNotChattedWithThisUserYet": "You haven't chatted with this user yet.", "Didn'tFindTheBrandYouAreLookingFor?": "Didn't find the brand you are looking for?", "ThereAreNoCommentsAtTheMoment": "There are no comments at the moment.", "AddCustomBrand": "Add Custom Brand", "CustomBrandSubmittedSuccessfully": "Custom brand submitted successfully.", "NFTAuthenticationIsNotAvailableForSelectedBrandAtTheMoment": "NFT authentication is not enabled for selected brand at the moment.", "BlockUser": "Block User", "ViewProfile": "View Profile", "UserNotFound": "User not found.", "AccountDoesNotExists": "Account does not exists.", "ContactUs": "Contact Us", "Title": "Title", "TitleIsRequired": "Title is required.", "Description": "Description", "DescriptionIsRequired": "Description is required.", "Submit": "Submit", "YourQueryWasSubmittedSuccessfully": "Your query was submitted successfully.", "Discount": "Discount", "YourNFTForAuthentication": "Your NFT for authentication", "hasBeenTransferred": "has been transferred", "CheckOut": "CheckOut", "TapToSeeFullImage": "Tap to see full image", "ShareThisCertificate": "Share this certificate", "DoYouWantTheAuthenticationToBePrivate": "Do you want the authentication to be private?", "UnFollow": "Unfollow", "AnalysisResults": "Analysis Results", "AuthenticatedBy": "Authenticated By", "AuthenticationBy": "Authentication By", "Appraisal": "Appraisal", "NFTCertified": "NFT Certified", "DeleteVote": "Delete Vote", "Subject": "Subject", "SubjectIsRequired": "Subject is required.", "Message": "Message", "MessageIsRequired": "Message is required.", "YourMessageHasBeenSent...": "Your message has been sent - a Legiteem8 representative will get back to you shortly.", "ThankYou": "Thank you!", "SeeAllListings": "See All Listings", "IfTheBrandOfYourTShirt...": "If the brand of your T-shirt is not listed, you can add it manually. Before doing it though, please make sure the brand is actually missing in our system.", "AddABrand": "Add a brand", "CustomBrand": "Custom Brand", "ConnectWalletToClaimNFT": "Connect Wallet to Claim NFT", "UpgradeToExpertAnalysis": "Upgrade to Expert Analysis", "AuthenticationPending": "Authentication Pending", "ExpertFeedBack": "Expert Fe<PERSON>back", "MyLegiteem8Wallet": "My Legiteem8 Wallet", "IDCopiedToClipboard": "ID copied to clipboard.", "RNNumberMustBeWither5Or6DigitsLong": "RN Number must be either 5 or 6 digits long.", "TheseAreCommunityResultsAndHaveNotBeen...": "These are Community Results and have not been reviewed or validated by a Legiteem8 expert", "DownloadTheAppToSeeFullResults": "Download the App to See Full Results", "GetNFT": "Get NFT", "UpgradeWithNFT": "Upgrade with NFT", "Legiteem8IsTheWorldsLargestVintage...": "Legiteem8 is the world’s first vintage t-shirt authentication app. But we’re also the first authentication app to harness the power of community. If you’ve got a t-shirt you’d like to authenticate, simply upload the required photos and input the essential data and let the social element of Legiteem8 work its magic.\n\nOther vintage t-shirt aficionados will be able weigh in and vote on whether your item is genuine and our system will calculate a score based on community votes. Concurrently, users will also input their appraisals of your t-shirt to help attach an accurate value to it.\n\nWe also offer tiers of service beyond our free community listings. You can also have your item assessed by an expert who can issue a certificate of authenticity, and even a NFT based version that will reside on the blockchain.", "ContractAddress": "Contract Address", "NFTCredentials": "NFT Credentials", "Etherscan": "Etherscan", "YouHaveNotVotedOnAnyPosts": "You have not voted on any posts.", "All": "All", "YouHaveNotFollowedAny...": "You haven't followed any other Legiteem8 members yet.", "LatestLegitChecks": "Latest Legit Checks", "AllLegitChecks": "All Legit Checks", "PleaseUpdateTheAppToContinue": "Please update the app to continue.", "Update": "Update", "NoListingsFound": "No listings found.", "ExpertCertNFT": "Expert Cert. NFT", "Undecided": "Undecided", "PolygonScanLink": "Polygonscan Link", "YouHaveNotMessagesYet": "You have no messages, yet.", "TheResultsForYourExpertServicesForA": "The results of your expert services for a", "TheResultsForAPostYouAreSubscribed...": "The results of a post you are subscribed to are ready.", "areReady": "are ready.", "PleaseWait": "Please wait...", "ErrorContactingAppleServer": "Error contacting Apple server. Please try again.", "Retry": "Retry", "ProductNotFound": "Product not found.", "ProceedToCheckout": "Proceed to checkout.", "Verified": "Verified.", "PleaseFinishAllTheInAppPurchaseStepsToCompleteThePurchase": "Please finish all the in app purchase steps to complete the purchase.", "ProvenanceIsRequired": "Provenance is required.", "NoUsersFound": "No users found.", "UserHasNotPostedYet": "User has not posted yet.", "Votes": "Votes", "SuccessfullySubscribedToPostUpdates": "Successfully subscribed to post updates.", "UnsubscribedFromPostUpdates": "Unsubscribed from post updates.", "Subscribe": "Subscribe", "Unsubscribe": "Unsubscribe", "FAQs": "FAQs", "FrequentlyAskedQuestions": "Frequently Asked Questions", "Like": "Like", "Users": "Users", "Shirts": "Shirts", "QrCodeSaved": "QR code saved to gallery.", "TagPreviousOwner": "Tag Previous Owner", "RequestToBeAdded": "Request To Be Added", "ProvenanceTagging": "Provenance Tagging", "PreviousOwner": "Previous Owner", "EnterUsername": "<PERSON><PERSON> Username", "DateOfOwnershipChange": "Date of Ownership Change", "ChooseDate": "<PERSON>ose <PERSON>", "hasTaggedYouAsThePreviousOwnerOf": "has tagged you as the previous owner of", "hasRequestedToBeTaggedAsAPreviousOwnerOf": "has requested to be tagged as a previous owner of", "Share": "Share", "FirstName": "First Name", "FirstNameIsRequired": "First name is required", "PleaseClearlyExplainYourReasons...": "Please clearly explain your reason(s) for giving this t-shirt a failing grade so the community can understand your decision.", "PleaseFullOutAllTheRequiredFields": "Please fill out all the required fields.", "ApplyCouponError": "Apply Coupon Error"}