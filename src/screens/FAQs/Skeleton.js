import React from 'react';
import {View, Dimensions} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH;

export default function UsersSkeleton() {
  return (
    <View
      style={{
        backgroundColor: colors.white,
        marginTop: 20,
      }}>
      <SkeletonPlaceholder speed={1000}>
        {new Array(20).fill(100).map((_, index) => (
          <SkeletonPlaceholder.Item key={index.toString()} flexDirection="row">
            <SkeletonPlaceholder.Item marginBottom={20}>
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH}
                marginBottom={10}
                height={20}
              />
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH / 1.5}
                height={8}
                borderRadius={10}
                marginBottom={10}
              />
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH / 1.5}
                height={8}
                borderRadius={10}
                marginBottom={10}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        ))}
      </SkeletonPlaceholder>
    </View>
  );
}
