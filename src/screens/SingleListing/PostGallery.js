import React, {useMemo, useCallback} from 'react';
import {StyleSheet, FlatList, Text, View, Dimensions} from 'react-native';
import {useQuery} from '@apollo/react-hooks';
import {useFocusEffect} from '@react-navigation/native';

import PostGalleryLoader from '../../components/Loaders/PostGalleryLoader';
import Icon from '../../components/Icon';
import {GET_SINGLE_POST} from '../../apollo/queries';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import {icons, screens, IMAGE_TYPES} from '../../assets/strings';
import ImageViewer from '../../components/ImageViewer';
import FastImage from '../../components/FastImage';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import RefreshControl from '../../components/RefreshControl';

const {width} = Dimensions.get('screen');
const WIDTH = width / 2;
const CARD_WIDTH = WIDTH - spacings.xl;

const IMAGE_VIEWER_INITIAL_STATE = {
  index: 0,
  visible: false,
};

const PostGallery = ({route, navigation}) => {
  const {postId} = route.params;
  const [imageViewerVisible, setImageViewerVisible] = React.useState(
    IMAGE_VIEWER_INITIAL_STATE,
  );
  const [refreshing, setRefreshing] = React.useState(false);

  const {data, refetch, loading, error} = useQuery(GET_SINGLE_POST, {
    variables: {id: postId},
  });

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  useFocusEffect(
    React.useCallback(() => {
      try {
        refetch?.();
      } catch (_) {}
    }, [refetch]),
  );

  const handleImageClick = useCallback(
    value => {
      navigation.navigate(screens.POST_IMAGE_COMMENT, {
        image_name: value?.key,
        image_url: value?.url,
        postId: postId,
      });
    },
    [navigation, postId],
  );

  const galleryImages = useMemo(() => {
    const objs = [];
    const finalImages = [];

    if (
      !(data?.getPost?.data?.images == null) &&
      typeof data?.getPost?.data?.images == 'object'
    ) {
      data?.getPost?.data?.number_of_post_image_comments?.forEach?.(item => {
        const itemImage = item?.authentication_image?.toLowerCase?.();
        if (itemImage in IMAGE_TYPES) {
          objs.push({
            url: null,
            key: itemImage,
            id: itemImage,
            count: item?.count,
          });
        }
      });

      for (const [_key, _value] of Object.entries(
        data?.getPost?.data?.images,
      )) {
        if (_key in IMAGE_TYPES && _value?.length > 0) {
          const findFromFirst = objs.find(obj => obj?.key == _key);
          finalImages.push({
            id: _key,
            key: _key,
            count: findFromFirst ? findFromFirst?.count : 0,
            url: _value,
          });
        }
      }
    }
    return finalImages;
  }, [
    data?.getPost?.data?.images,
    data?.getPost?.data?.number_of_post_image_comments,
  ]);

  const Empty = () => {
    return (
      <View style={styles.emptyContainer}>
        <Text style={{color: colors.black}}>
          There are no images under this post yet.
        </Text>
      </View>
    );
  };

  const handleImageViewing = React.useCallback((index = 0) => {
    setImageViewerVisible({visible: true, index});
  }, []);

  if (loading || error) {
    return <PostGalleryLoader />;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={galleryImages}
        columnWrapperStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        bounces={true}
        renderItem={({item, index}) => (
          <TouchAwareButton onPress={() => handleImageClick(item)}>
            <FastImage source={{uri: item.url}} style={styles.imageStyles} />
            <View style={styles.magnifyingButton}>
              <TouchAwareButton
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.3)',
                  padding: 7,
                  borderRadius: 3,
                }}
                onPress={handleImageViewing.bind(null, index)}>
                <Icon
                  name={icons.MAGNIFYING_GLASS}
                  size={20}
                  tint={colors.black}
                />
              </TouchAwareButton>
            </View>
            {item?.count ? (
              <View style={styles.commentCount}>
                <Text style={styles.commentStyles}>
                  {item?.count > 1
                    ? `${item?.count} comments`
                    : `${item?.count} comment`}
                </Text>
              </View>
            ) : null}
          </TouchAwareButton>
        )}
        keyExtractor={(item, index) => index.toString()}
        numColumns={2}
        ListEmptyComponent={<Empty />}
        ListHeaderComponent={() =>
          galleryImages.length === 0 ? null : (
            <View style={styles.titleWrapper}>
              <Text numberOfLines={3} ellipsizeMode="tail" style={styles.title}>
                {data?.getPost?.data?.title} ({galleryImages.length})
              </Text>
            </View>
          )
        }
        ListFooterComponent={<View style={{height: spacings.lg}} />}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      />
      <ImageViewer
        visible={imageViewerVisible.visible}
        setVisible={() => setImageViewerVisible(IMAGE_VIEWER_INITIAL_STATE)}
        images={galleryImages?.map(img => ({url: img?.url, id: img?.key}))}
        index={imageViewerVisible.index}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyContainer: {
    flex: 1,
    backgroundColor: colors.white,
    marginTop: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    paddingTop: spacings.lg,
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  imageStyles: {
    height: CARD_WIDTH / 1.5,
    width: CARD_WIDTH,
    borderRadius: 2,
    backgroundColor: colors.lightGrey,
  },
  imageTitleWrapper: {
    width: width,
    padding: spacings.lg,
    backgroundColor: colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },
  magnifyingButton: {
    position: 'absolute',
    right: 0,
    top: 0,
  },
  commentCount: {
    backgroundColor: colors.darkBadge,
    position: 'absolute',
    left: 0,
    bottom: 0,
    padding: spacings.sm,
    borderRadius: 2,
  },
  commentStyles: {
    fontSize: fontSize.sm,
    color: colors.white,
  },
  titleWrapper: {
    paddingHorizontal: spacings.lg,
    paddingTop: spacings.lg,
  },
  title: {
    color: colors.black,
    fontSize: fontSize.xxl,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  imageTitleStyle: {
    fontWeight: 'bold',
    fontSize: fontSize.lg,
    color: colors.white,
    textTransform: 'capitalize',
  },
});

export default PostGallery;
