import React, {useEffect, useState, useRef, useCallback} from 'react';
import {Text, View, TouchableOpacity, ActivityIndicator} from 'react-native';
import {Formik, Field} from 'formik';
import * as yup from 'yup';

import CustomInput from '../../components/AuthInput';
import {screens, registerScreen} from '../../assets/strings';
import {styles} from './styles';
import colors from '../../assets/colors';
import {
  SEND_REGISTRATION_CODE,
  VERIFY_REGISTRATION_CODE,
} from '../../tools/api';
import {showToast} from '../../components/Toast';
import locale from '../../assets/locale.json';
import Layout from '../../layout';

const RegisterSchema = yup.object().shape({
  code: yup.string().required('Please enter the code'),
});

const EmailVerification = ({route, navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [resendCode, setResendCode] = useState(false);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);

  const item = route.params.params;

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setError(null);
      isMounted.current = false;
    };
  }, []);

  const handleCodeVerification = useCallback(
    async data => {
      setIsLoading(true);
      let credentials = {
        email: item.email,
        code: data.code,
      };
      try {
        const response = await VERIFY_REGISTRATION_CODE(credentials);
        const res = response.data;
        if (res.success === true) {
          setIsLoading(false);
          navigation.navigate(screens.CREATE_PASSWORD, {params: credentials});
        } else {
          setError('Please enter the correct code.');
          setIsLoading(false);
        }
      } catch (e) {
        setError('Please enter the correct code.');
        setIsLoading(false);
      }
    },
    [item.email, navigation],
  );

  const handleResendCode = useCallback(async () => {
    setResendCode(true);
    try {
      const response = await SEND_REGISTRATION_CODE(item);
      const res = response?.data;
      if (res?.success) {
        showToast({
          type: 'success',
          message: locale.VerificationCodeSentToYourInbox,
          position: 'bottom',
        });
      } else {
        throw new Error();
      }
    } catch (e) {
      showToast({
        type: 'error',
        message: locale['SomethingWentWrong.'],
        position: 'bottom',
      });
    } finally {
      setResendCode(false);
    }
  }, [item]);

  return (
    <Layout contentContainerStyle={styles.container}>
      <Formik
        validationSchema={RegisterSchema}
        initialValues={{
          code: '',
        }}
        onSubmit={async values => {
          if (isMounted.current === true) {
            handleCodeVerification(values);
          }
        }}>
        {({handleSubmit}) => (
          <>
            <View style={styles.loginContainer}>
              <View style={styles.titleWrapper}>
                <Text style={styles.titleStyle}>
                  {registerScreen.SCREEN2_TITLE}
                </Text>
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {registerScreen.SCREEN2_SUBTITLE}
                </Text>
                <View style={styles.codeExpiresWrapper}>
                  <Text style={styles.codeExpires}>
                    {registerScreen.CODE_EXPIRES}
                  </Text>
                </View>
                <Text style={styles.inputLabel}>
                  {registerScreen.SCREEN2_EMAIL_LABEL}
                </Text>
                <Field
                  style={styles.inputField}
                  component={CustomInput}
                  name="code"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.forgotPasswordSection}>
                <Text style={styles.codeNotReceived}>
                  {registerScreen.NOT_RECEIVED_CODE}
                </Text>
                <TouchableOpacity
                  style={styles.resendCodeWrapper}
                  onPress={handleResendCode}>
                  <Text style={styles.resendCode}>
                    {resendCode ? (
                      <ActivityIndicator
                        color={colors.primary}
                        size={'small'}
                      />
                    ) : (
                      registerScreen.RESEND_CODE
                    )}
                  </Text>
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}>
                {isLoading ? (
                  <ActivityIndicator color={colors.white} />
                ) : (
                  <Text style={styles.buttonText}>
                    {registerScreen.SCREEN2_BUTTON_LABEL}
                  </Text>
                )}
              </TouchableOpacity>
              {error ? (
                <View style={styles.errorWrapper}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : null}
            </View>
          </>
        )}
      </Formik>
    </Layout>
  );
};

export default EmailVerification;
