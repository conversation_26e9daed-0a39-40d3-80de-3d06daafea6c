/* eslint-disable prettier/prettier */
import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  Pressable,
  Alert,
  Modal,
  View,
  TextInput,
  Dimensions,
  Platform,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import DropDownPicker from 'react-native-dropdown-picker';
import {SafeAreaView} from 'react-native-safe-area-context';

import {fontSize} from '../../assets/font';
import {icons, searchScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('screen');

const isIOS = Platform.OS == 'ios';

const SearchButton = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [typeOpen, setTypeOpen] = useState(false);
  const [stitchingOpen, setStitchingOpen] = useState(false);
  const [materialOpen, setMaterialpen] = useState(false);
  const [verdictOpen, setVerdictOpen] = useState(false);

  const [typeValue, setTypeValue] = useState(null);
  const [stitchingValue, setStitchingValue] = useState(null);
  const [materialValue, setMaterialValue] = useState(null);
  const [verdictValue, setVerdictValue] = useState(null);

  const [type, setType] = useState([
    {id: 1, label: 'All', value: 'all'},
    {id: 2, label: 'Shirts', value: 'shirts'},
    {id: 3, label: 'Tags', value: 'tags'},
    {id: 4, label: 'Code', value: 'code'},
  ]);

  const [material, setMaterial] = useState([
    {id: 1, label: 'All', value: 'all'},
    {id: 2, label: 'Cotton', value: 'cotton'},
    {id: 3, label: 'Polyester', value: 'polyester'},
    {id: 4, label: 'Rayon-Blend', value: 'rayon-blend'},
    {id: 5, label: 'Tri-Blend', value: 'tri-blend'},
    {id: 6, label: 'Poly-Cotton', value: 'poly-cotton'},
    {id: 7, label: 'Blend', value: 'blend'},
    {id: 8, label: 'Other', value: 'other'},
  ]);

  const [stitching, setStitching] = useState([
    {id: 1, label: 'All', value: 'all'},
    {id: 2, label: 'Single', value: 'single'},
    {id: 3, label: 'Double', value: 'Double'},
    {id: 4, label: 'Dual', value: 'dual'},
  ]);

  const [verdict, setVerdict] = useState([
    {id: 1, label: 'All', value: 'all'},
    {id: 2, label: 'Fake', value: 'fake'},
    {id: 3, label: 'Pending', value: 'pending'},
    {id: 4, label: 'Code', value: 'code'},
  ]);

  const handleSearchToggle = () => {
    setModalVisible(!modalVisible);
  };

  return (
    <View style={styles.container}>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          Alert.alert('Modal has been closed.');
          setModalVisible(!modalVisible);
        }}>
        <SafeAreaView style={styles.modal}>
          {isIOS ? (
            <StatusBar backgroundColor="black" barStyle="dark-content" />
          ) : null}
          <View style={styles.modalHeader}>
            <View style={styles.searchBarWrapper}>
              <Icon
                style={{padding: spacings.sm}}
                name={icons.SEARCH_OUTLINE}
                size={fontSize.xxl}
                color={colors.darkGrey}
              />
              <TextInput
                selectionColor={colors.white}
                placeholder={searchScreen.PLACEHOLDER}
                autoCapitalize="sentences"
                autoCorrect={false}
                placeholderTextColor={colors.darkGrey}
                autoFocus={true}
                style={[styles.searchBar, {color: colors.almostWhite}]}
              />
            </View>
            <Pressable onPress={handleSearchToggle}>
              <Text style={styles.closeButtonText}>{searchScreen.CANCEL}</Text>
            </Pressable>
          </View>
          <View style={styles.filters}>
            <View style={styles.filterChip}>
              <DropDownPicker
                style={styles.picker}
                containerStyle={styles.pickerContainer}
                categorySelectable={true}
                listMode="FLATLIST"
                placeholder="Type"
                flatListProps={{
                  initialNumToRender: 10,
                }}
                open={typeOpen}
                value={typeValue}
                items={type}
                setOpen={setTypeOpen}
                setValue={setTypeValue}
                setItems={setType}
              />
            </View>
            <View style={styles.filterChip}>
              <DropDownPicker
                style={styles.picker}
                containerStyle={styles.pickerContainer}
                categorySelectable={true}
                listMode="FLATLIST"
                placeholder="Materials"
                flatListProps={{
                  initialNumToRender: 10,
                }}
                open={materialOpen}
                value={materialValue}
                items={material}
                setOpen={setMaterialpen}
                setValue={setMaterialValue}
                setItems={setMaterial}
              />
            </View>
            <View style={styles.filterChip}>
              <DropDownPicker
                style={styles.picker}
                containerStyle={styles.pickerContainer}
                categorySelectable={true}
                listMode="FLATLIST"
                placeholder="Stitching"
                flatListProps={{
                  initialNumToRender: 10,
                }}
                open={stitchingOpen}
                value={stitchingValue}
                items={stitching}
                setOpen={setStitchingOpen}
                setValue={setStitchingValue}
                setItems={setStitching}
              />
            </View>
            <View style={styles.filterChip}>
              <DropDownPicker
                style={styles.picker}
                containerStyle={styles.pickerContainer}
                categorySelectable={true}
                listMode="FLATLIST"
                placeholder="Verdict"
                flatListProps={{
                  initialNumToRender: 10,
                }}
                open={verdictOpen}
                value={verdictValue}
                items={verdict}
                setOpen={setVerdictOpen}
                setValue={setVerdictValue}
                setItems={setVerdict}
              />
            </View>
          </View>
        </SafeAreaView>
      </Modal>

      <Pressable onPress={handleSearchToggle} style={styles.searchBarButton}>
        <Icon
          style={{padding: spacings.sm}}
          name={icons.SEARCH_OUTLINE}
          size={fontSize.xxl}
          color={colors.darkGrey}
        />
        <Text style={styles.searchButtonText}>{searchScreen.PLACEHOLDER}</Text>
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: WIDTH * 0.5,
  },
  modal: {
    backgroundColor: colors.white,
    flex: 1,
  },
  modalHeader: {
    backgroundColor: colors.black,
    borderBottomWidth: StyleSheet.hairlineWidth,
    height: 60,
    alignItems: 'center',
    justifyContent: 'space-evenly',
    flexDirection: 'row',
    paddingHorizontal: spacings.lg,
  },
  titleStyle: {
    fontSize: fontSize.xxl,
    color: colors.black,
    fontWeight: 'bold',
    marginLeft: spacings.xl,
  },
  searchBarWrapper: {
    width: '80%',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.darkGrey,
    borderRadius: 50,
    paddingLeft: 10,
  },
  searchBarButton: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.darkGrey,
    color: colors.white,
    borderRadius: spacings.lg,
    paddingHorizontal: spacings.sm,
  },
  searchBar: {
    width: '90%',
    height: spacings.xxl,
    paddingHorizontal: spacings.sm,
    fontSize: fontSize.md,
    color: colors.white,
  },
  searchButtonText: {
    color: colors.darkGrey,
  },
  closeButtonText: {
    fontSize: fontSize.md,
    color: colors.primary,
    padding: spacings.md,
    fontWeight: 'bold',
  },
  filters: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.grey,
  },
  filterChip: {
    marginHorizontal: 5,
    marginVertical: 10,
  },
  picker: {
    width: 115,
    height: 40,
  },
  pickerContainer: {
    width: 115,
  },
});

export default SearchButton;
