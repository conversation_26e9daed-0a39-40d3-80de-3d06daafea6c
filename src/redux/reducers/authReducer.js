import AsyncStorage from '@react-native-async-storage/async-storage';

import actionTypes from '../actionTypes';
import {persistent} from '../../assets/strings';

const ROLE_MAPPING = {
  2: 'MEMBER',
  3: 'EXPERT',
};

const initialState = {
  access_token: '',
  refresh_token: '',
  credential: {
    role_id: 2,
    user_id: null,
    role: ROLE_MAPPING[2],
  },
  user: {
    id: null,
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    image: '',
    bio: '',
    accuracy: '',
    allow_push_notifications: '',
    allow_message_from_non_follower: '',
    account_verified: '',
    two_factor_authentication: '',
    badge: {
      id: '',
      name: '',
      image_url: '',
      level: '',
    },
    social_media: {
      id: '',
      name: '',
      url: '',
    },
    role: '',
    authentication_count: '',
    followersCount: '',
    followingsCount: '',
    number_of_votes: '',
  },
  doesUserChangedPassword: false,
  currentLocation: null,
  profile: {},
  cryptoAddress: null,
  cryptoUserData: null,
  isCryptoAddressManual: false,
  showModal: false,
  isFirstTimeUsingApp: true,
  isExpert: false,
  loggedIn: {
    isLoggedIn: false,
  },
  registerAccount: {
    isRegistered: false,
  },
  verifyAccount: {
    isAccountVerified: false,
  },
  initiateForgotPassword: {
    emailExists: false,
  },
  validateCode: {
    isValidated: false,
  },
  passwordReset: {
    isResetSuccess: false,
  },
  notification: {
    allow_post_reaction: true,
    allow_follow_status: true,
    allow_direct_message: true,
    allow_authentication_submission_status: true,
    allow_in_app_post_reaction: true,
    allow_in_app_follow_status: true,
    allow_in_app_direct_message: true,
    allow_in_app_authentication_submission_status: true,
  },
};

const setToken = async (accessToken, refreshToken, userID, is2FA, userEmail) => {
  try {
    await AsyncStorage.setItem(persistent.ACCESS_TOKEN, accessToken);
    await AsyncStorage.setItem(persistent.REFRESH_TOKEN, refreshToken);
    await AsyncStorage.setItem(persistent.USER_ID, JSON.stringify(userID));
    await AsyncStorage.setItem(persistent.IS_2FA, is2FA ? "1" : "0");
    await AsyncStorage.setItem(persistent.EMAIL_ID, userEmail);
  } catch (_) {}
};

const authReducer = (state = initialState, {type, payload}) => {
  switch (type) {
    case actionTypes.LOGIN_SUCCESS:
      setToken(payload.access_token, payload.refresh_token, payload.userId, payload.is2FA, payload.userEmail);
      return {
        ...state,
        access_token: payload.access_token,
        refresh_token: payload.refresh_token,
        loggedIn: {
          ...state.loggedIn,
          isLoggedIn: true,
        },
        credential: {
          role_id: payload?.roleId,
          user_id: payload?.userId,
          role: ROLE_MAPPING[payload?.roleId] ?? ROLE_MAPPING[2],
        },
      };
    case actionTypes.REGISTER_SUCCESS:
      setToken(payload.access_token, payload.refresh_token, payload.userId, false, payload.userEmail);
      return {
        ...state,
        access_token: payload.access_token,
        refresh_token: payload.refresh_token,
        showModal: true,
        registerAccount: {
          ...state.registerAccount,
          isRegistered: true,
        },
        loggedIn: {
          ...state.loggedIn,
          isLoggedIn: true,
        },
        credential: {
          role_id: payload?.roleId,
          user_id: payload?.userId,
          role: ROLE_MAPPING[payload?.roleId] ?? ROLE_MAPPING[2],
        },
      };
    case actionTypes.CODE_VALIDATION_SUCCESS:
      return {
        ...state,
        validateCode: {
          ...state.validateCode,
          isValidated: true,
        },
      };

    case actionTypes.PASSWORD_RESET_SUCCESS:
      return {
        ...state,
        passwordReset: {
          ...state.passwordReset,
          isResetSuccess: true,
        },
      };
    case actionTypes.ACCOUNT_VERIFICATION_SUCCESS:
      return {
        ...state,
        verifyAccount: {
          ...state.verifyAccount,
          isAccountVerified: true,
        },
        loggedIn: {
          ...state.loggedIn,
          isLoggedIn: true,
        },
      };
    case actionTypes.FORGOT_PASSWORD_SUCCESS:
      return {
        ...state,
        initiateForgotPassword: {
          ...state.initiateForgotPassword,
          emailExists: true,
        },
      };
    case actionTypes.REFRESH_USER:
      return {
        ...state,
        user: {
          ...state.user,
          ...(payload?.user ?? {}),
        },
      };
    case actionTypes.SIGN_OUT:
      return {
        ...state,
        access_token: '',
        refresh_token: '',
        user: null,
        loggedIn: {
          ...state.loggedIn,
          isLoggedIn: false,
        },
        isFirstTimeUsingApp: false,
        isExpert: false,
      };
    case actionTypes.ONBOARDING:
      return {
        ...state,
        isFirstTimeUsingApp: false,
      };

    case actionTypes.IS_FIRST_TIME_USING_APP:
      return {
        ...state,
        showModal: false,
      };
    case actionTypes.IS_EXPERT:
      return {
        ...state,
        isExpert: true,
      };
    case actionTypes.CRYPTO_ADDRESS:
      return {
        ...state,
        cryptoAddress: payload?.walletAddress,
        cryptoUserData: payload?.userData,
        isCryptoAddressManual:
          payload?.isCryptoAddressManual ?? state.isCryptoAddressManual,
      };
    case actionTypes.REFRESH_USER_PUSH_NOTIFICATION_SETTINGS:
      return {
        ...state,
        notification: {
          ...state.notification,
          ...payload,
        },
      };
    default:
      return state;
  }
};

export default authReducer;
