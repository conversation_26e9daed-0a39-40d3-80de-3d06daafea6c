import React from 'react';
import {StyleSheet, Dimensions} from 'react-native';

import {fontSize} from '../../assets/font';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('screen');

export default styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  modalSheetContainer: {
    padding: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tagImageStyles: {
    width: WIDTH,
    height: WIDTH,
    resizeMode: 'contain',
  },
  modalHeader: {
    backgroundColor: colors.black,
    borderBottomWidth: StyleSheet.hairlineWidth,
    height: 60,
    alignItems: 'center',
    justifyContent: 'space-evenly',
    flexDirection: 'row',
    paddingHorizontal: spacings.lg,
  },
  titleStyle: {
    fontSize: fontSize.xxl,
    color: colors.black,
    fontWeight: 'bold',
    marginLeft: spacings.xl,
  },
  searchBarWrapper: {
    width: '85%',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#323232',
    borderRadius: 50,
    paddingLeft: 10,
    backgroundColor: '#222222',
  },
  searchBar: {
    width: '90%',
    height: spacings.xxl,
    paddingHorizontal: spacings.sm,
    fontSize: fontSize.md,
    color: colors.white,
  },
  closeButtonText: {
    fontSize: fontSize.md,
    color: colors.primary,
    padding: spacings.md,
    fontWeight: 'bold',
  },
  filters: {
    flexDirection: 'row',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.grey,
    paddingVertical: 5,
    paddingRight: 10,
  },
  filterChip: {
    marginLeft: spacings.lg,
    marginVertical: 10,
    borderWidth: 1.5,
    borderColor: colors.darkGrey,
    borderRadius: 25,
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterChipText: {
    color: colors.darkGrey,
    fontSize: fontSize.lg,
  },
  checkedbox: {
    backgroundColor: colors.primary,
    height: 30,
    width: 30,
    borderRadius: 2,
  },
  uncheckedbox: {
    backgroundColor: colors.grey,
    height: 30,
    width: 30,
    borderRadius: 2,
  },
  picker: {
    width: 115,
    height: 40,
  },
  pickerContainer: {
    width: 115,
  },
  resultsCard: {
    padding: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 4,
    borderBottomColor: colors.lightGrey,
    minHeight: WIDTH * 0.35,
  },
  searchImage: {
    minHeight: WIDTH * 0.2,
    height: '100%',
    width: '100%',
    borderRadius: 4,
  },
  titleStyles: {
    fontSize: fontSize.md,
    color: colors.black,
    textTransform: 'capitalize',
    fontFamily: 'Gugi-Regular',
    lineHeight: 15,
  },
  textSection: {
    width: '50%',
  },
  imageSection: {
    position: 'absolute',
    top: 20,
    right: 20,
    bottom: 20,
    width: '45%',
    borderRadius: 4,
    overflow: 'hidden',
  },
  voteBadge: {
    color: colors.white,
    fontSize: fontSize.xs,
    marginLeft: spacings.sm,
    fontWeight: 'bold',
  },
  indicator: {
    backgroundColor: colors.primary,
    borderRadius: 25,
  },
  barStyle: {
    backgroundColor: colors.white,
    elevation: 1,
    borderTopWidth: 3,
    borderTopColor: colors.lightGrey,
  },
  tabBarLabelStyle: {
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
    fontWeight: '700',
  },
  userProfileContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: colors.grey,
    alignItems: 'center',
  },
  userInfoContainer: {
    marginLeft: 16,
  },
  titleStyle: {
    fontFamily: 'Inter-Medium',
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitleStyle: {
    fontFamily: 'Inter-Regular',
    opacity: 0.5,
    marginVertical: 2,
  },
});
