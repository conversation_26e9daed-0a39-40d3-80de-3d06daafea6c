import React, {useCallback, useState} from 'react';
import {
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Text,
  View,
} from 'react-native';
import {useMutation} from '@apollo/client';

import {UPDATE_PROFILE} from '../../apollo/mutations';
import {profileSettingsScreen} from '../../assets/strings';
import ImagePickerModal from '../../components/ImagePicker';
import useImageUpload from '../../tools/hooks/useImageUpload';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import ProfileImage from '../../components/ProfileImage';
import ImageViewer from '../../components/ImageViewer';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import locale from '../../assets/locale.json';
import InfoModal from '../../components/Modal/InfoModal';

const {width: WIDTH} = Dimensions.get('screen');
const IMAGE_WIDTH = WIDTH / 2.4;

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: locale.Error,
    description: '',
  },
};

const ChangeProfilePic = ({refetch = () => null, user = null}) => {
  const {upload} = useImageUpload();
  const [updateProfile] = useMutation(UPDATE_PROFILE, {onCompleted: refetch});
  const [localImage, setLocalImage] = React.useState(null);

  const [pickDialogVisible, setPickDialogVisible] = useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
  const [loading, setLoading] = React.useState(false);

  const handleImageUploadClick = () => setPickDialogVisible(true);

  const deleteImage = React.useCallback(async () => {
    try {
      setLoading(true);
      const {data} = await updateProfile({
        variables: {
          image: '',
        },
      });
      const isSuccess = data?.updateProfile?.success;

      if (isSuccess) {
        setLocalImage(null);
        await refetch?.();
      } else {
        throw new Error();
      }
    } catch (error) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
        data: {
          ...previousState.data,
          description: locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    } finally {
      setLoading(false);
    }
  }, [updateProfile]);

  const handleImageUpload = useCallback(
    async img => {
      setLoading(true);
      setLocalImage(null);
      try {
        const {path} = await upload(img);
        const {data} = await updateProfile({
          variables: {
            image: path,
          },
        });
        const isSuccess = data?.updateProfile?.success;
        if (isSuccess) {
          setLocalImage(img?.path);
          await refetch?.();
        } else {
          throw new Error();
        }
      } catch (_) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          data: {
            ...previousState.data,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }));
      } finally {
        setLoading(false);
      }
    },
    [updateProfile, upload, refetch],
  );

  const hasImage = !(user?.image == null || user?.image?.length == 0);

  return (
    <>
      <View style={styles.profilePhotoWrapper}>
        <View style={styles.container}>
          <ProfileImageViewer
            image={localImage ?? user?.image}
            handleImageUploadClick={handleImageUploadClick}
            loading={loading}
          />
          <View style={styles.buttonWrapper}>
            <TouchableOpacity
              style={styles.button}
              onPress={handleImageUploadClick}
              disabled={loading}>
              <Text style={styles.buttonLabel}>
                {hasImage ? locale.Change : locale.Add}
              </Text>
            </TouchableOpacity>
            {hasImage ? (
              <TouchableOpacity
                disabled={loading}
                style={[styles.button, {marginLeft: 20}]}
                onPress={deleteImage}>
                <Text style={[styles.buttonLabel, {color: colors.darkGrey}]}>
                  {profileSettingsScreen.REMOVE}
                </Text>
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
      </View>
      {pickDialogVisible ? (
        <ImagePickerModal
          visible={pickDialogVisible}
          setVisible={setPickDialogVisible}
          onImageSelected={handleImageUpload}
        />
      ) : null}
      <InfoModal
        setVisible={() =>
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }))
        }
        popUp={popup}
      />
    </>
  );
};

function ProfileImageViewer({
  image = '',
  handleImageUploadClick = () => null,
  loading = false,
}) {
  const [visible, setVisible] = React.useState(false);

  const isDisabled = image == null || image?.length == 0;

  const handleImagePress = () => {
    if (!isDisabled) {
      setVisible(true);
    } else {
      handleImageUploadClick();
    }
  };

  return (
    <>
      <TouchAwareButton
        onPress={handleImagePress}
        style={{alignSelf: 'center', opacity: 1}}
        disabled={loading}>
        <ProfileImage url={image} size={70} isLoading={loading} canViewImage />
      </TouchAwareButton>
      <ImageViewer
        visible={visible}
        setVisible={setVisible}
        images={[{url: image}]}
      />
    </>
  );
}

export default ChangeProfilePic;

const styles = StyleSheet.create({
  container: {
    marginBottom: spacings.lg,
  },
  buttonWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    marginVertical: spacings.md,
  },
  buttonLabel: {
    color: colors.primary,
    fontSize: fontSize.lg,
    fontFamily: 'Inter-Medium',
  },
  imagePlaceHolder: {
    height: IMAGE_WIDTH / 1.5,
    width: '100%',
    resizeMode: 'cover',
    borderRadius: 2,
  },
  profilePhotoWrapper: {
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: spacings.lg,
    borderBottomWidth: 2,
    borderBottomColor: colors.lightGrey,
  },
  imageStyles: {
    height: 100,
    width: 100,
    borderRadius: 100,
    borderWidth: 5,
    borderColor: colors.lightGrey,
  },
});
