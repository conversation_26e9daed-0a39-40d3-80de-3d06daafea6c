import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import dayjs from 'dayjs';
import {useNavigation} from '@react-navigation/native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {screens} from '../../assets/strings';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ProfileImage from '../../components/ProfileImage';
import locale from '../../assets/locale.json';
import NotificationOwnershipHistoryTagged from './Row/NotificationOwnershipHistoryTagged';
import NotificationHistoryRequestedToBeTagged from './Row/NotificationHistoryRequestedToBeTagged';
import LogoOnly from '../../assets/Images/logo-only-2.jpg';
import { type } from 'os';

export const NOTIFICATION_TYPES = Object.freeze({
  MASS_NOTIFICATION: 'MASS_NOTIFICATION',
  ACCOUNT_FOLLOWED: 'ACCOUNT_FOLLOWED',
  NEW_VOTE_ON_POST: 'NEW_VOTE_ON_POST',
  AUTHENTICATION_ASSIGNED_EXPERT: 'AUTHENTICATION_ASSIGNED_EXPERT',
  AUTHENTICATION_ACCEPTED: 'AUTHENTICATION_ACCEPTED',
  AUTHENTICATION_REJECTED: 'AUTHENTICATION_REJECTED',
  NEW_COMMENT_ON_POST: 'NEW_COMMENT_ON_POST',
  NEW_COMMENT_ON_POST_IMAGE: 'NEW_COMMENT_ON_POST_IMAGE',
  NFT_TRANSFERRED: 'NFT_TRANSFERRED',
  AUTHENTICATION_REVIEWED_BY_EXPERT: 'AUTHENTICATION_REVIEWED_BY_EXPERT',
  NEW_MENTION_ON_POST_COMMENT: 'NEW_MENTION_ON_POST_COMMENT',
  NEW_MENTION_ON_POST_IMAGE_COMMENT: 'NEW_MENTION_ON_POST_IMAGE_COMMENT',
  NEW_LIKE_ON_POST: 'NEW_LIKE_ON_POST',
  OWNERSHIP_HISTORY_TAGGED: 'OWNERSHIP_HISTORY_TAGGED',
  OWNERSHIP_HISTORY_REQUESTED_TO_BE_TAGGED:
    'OWNERSHIP_HISTORY_REQUESTED_TO_BE_TAGGED',
  SUBSCRIPTION_NEW_VOTE_TO_POST: 'SUBSCRIPTION_NEW_VOTE_TO_POST',
  SUBSCRIPTION_NEW_COMMENT_TO_POST: 'SUBSCRIPTION_NEW_COMMENT_TO_POST',
  SUBSCRIPTION_NEW_COMMENT_TO_POST_IMAGE:
    'SUBSCRIPTION_NEW_COMMENT_TO_POST_IMAGE',
  SUBSCRIPTION_AUTHENTICATION_REVIEWED_BY_EXPERT:
    'SUBSCRIPTION_AUTHENTICATION_REVIEWED_BY_EXPERT',
  COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED:
    'COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED',
  SUBSCRIPTION_COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED:
    'SUBSCRIPTION_COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED',
});

const DECISION_MAPPING = {
  REAL: 'PASS',
  FAKE: 'FAIL',
};

function NotificationRow({item = null}) {
  console.log("item", NOTIFICATION_TYPES.AUTHENTICATION_ASSIGNED_EXPERT)
  const navigation = useNavigation();

  if (item?.type === NOTIFICATION_TYPES.MASS_NOTIFICATION) {
    return (
      <View style={styles.notificationsCard}>
        <View style={styles.descWrapper}>
          <ProfileImage
            url={''}
            fallbackImage={LogoOnly}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>{item?.title}</Text>
            <Text style={[styles.description, {flex: 1}]}>{item?.message}</Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </View>
    );
  } else if (item?.type === NOTIFICATION_TYPES.ACCOUNT_FOLLOWED) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(
            item?.followed_by?.role == 'EXPERT'
              ? screens.EXPERTS_PROFILE
              : screens.PUBLIC_PROFILES,
            {
              ...(item?.followed_by?.role == 'EXPERT'
                ? {expertUserId: item?.followed_by?.id}
                : {params: item?.followed_by?.id}),
            },
          )
        }>
        <View style={styles.descWrapper}>
          <ProfileImage
            url={item?.followed_by?.image}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              <Text style={styles.userName}>{item?.followed_by?.username}</Text>{' '}
              {locale.hasStartedFollowingYou}.
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (
    [
      NOTIFICATION_TYPES.NEW_VOTE_ON_POST,
      NOTIFICATION_TYPES.SUBSCRIPTION_NEW_VOTE_TO_POST,
    ].includes(item?.type)
  ) {
    return (
      <TouchAwareButton
        key={item?.id?.toString()}
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item?.post?.id,
          })
        }>
        <View style={styles.descWrapper}>
          <TouchAwareButton
            onPress={() =>
              navigation.navigate(
                item?.vote?.user?.role == 'EXPERT'
                  ? screens.EXPERTS_PROFILE
                  : screens.PUBLIC_PROFILES,
                {
                  ...(item?.followed_by?.role == 'EXPERT'
                    ? {expertUserId: item?.vote?.user?.id}
                    : {params: item?.vote?.user?.id}),
                },
              )
            }>
            <ProfileImage
              url={item?.vote?.user?.image}
              size={45}
              style={{marginRight: 10}}
            />
          </TouchAwareButton>

          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              <Text style={{fontWeight: '700'}}>
                {item?.vote?.user?.username}
              </Text>{' '}
              {locale.hasVoted}{' '}
              <Text style={{fontWeight: '700'}}>
                {DECISION_MAPPING?.[item?.vote?.decision]}
              </Text>{' '}
              {{
                [NOTIFICATION_TYPES.NEW_VOTE_ON_POST]: 'on your post',
                [NOTIFICATION_TYPES.SUBSCRIPTION_NEW_VOTE_TO_POST]:
                  'on a post you are subscribed to.',
              }?.[item?.type] ?? null}{' '}
              {item?.type === NOTIFICATION_TYPES.NEW_VOTE_ON_POST ? (
                <Text style={styles.authenticationTitle}>
                  {item?.post?.title}.
                </Text>
              ) : null}
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (item?.type === NOTIFICATION_TYPES.AUTHENTICATION_ASSIGNED_EXPERT) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item?.post_id,
          })
        }>
        <View style={styles.descWrapper}>
          <ProfileImage
            url={item?.post?.images?.featured || item?.post?.images?.front}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
            {item?.title}
              <Text style={styles.authenticationTitle}>
                
              </Text>{' '}
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (item?.type === NOTIFICATION_TYPES.AUTHENTICATION_ACCEPTED) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item?.post?.id,
          })
        }>
        <View style={styles.descWrapper}>
          <ProfileImage
            url={item?.post?.images?.featured || item?.post?.images?.front}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              {locale.YourAuthentication}{' '}
              <Text style={styles.authenticationTitle}>
                {item?.post?.title}
              </Text>{' '}
              {locale.hasBeenAccepted}.
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (item?.type === NOTIFICATION_TYPES.AUTHENTICATION_REJECTED) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        disableLongPressAnimation
        onPress={() =>
          navigation.navigate(screens.ADD_PHOTOS, {
            authenticationId: item?.queue?.id,
            isAuthenticationResubmission: true,
          })
        }>
        <View style={[styles.descWrapper, {alignItems: 'center'}]}>
          <ProfileImage
            url={item?.queue?.images?.featured || item?.queue?.images?.front}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              {locale.YourAuthentication}{' '}
              <Text style={styles.authenticationTitle}>
                {item?.queue?.title}
              </Text>{' '}
              {locale.hasBeenRejected} - {item?.rejection?.description}
            </Text>
            <Text
              style={[
                styles.text,
                {
                  color: colors.secondary,
                  fontSize: 13,
                  marginVertical: 5,
                },
              ]}>
              ({locale.TapToResubmit})
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (
    [
      NOTIFICATION_TYPES.NEW_COMMENT_ON_POST,
      NOTIFICATION_TYPES.SUBSCRIPTION_NEW_COMMENT_TO_POST,
    ].includes(item?.type)
  ) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.COMMENTS, {
            postId: item?.comment?.post?.id,
            showPostNavigationLink: true,
          })
        }>
        <View style={styles.descWrapper}>
          <TouchAwareButton
            onPress={() =>
              navigation.navigate(
                item?.comment?.user?.role == 'EXPERT'
                  ? screens.EXPERTS_PROFILE
                  : screens.PUBLIC_PROFILES,
                {
                  ...(item?.followed_by?.role == 'EXPERT'
                    ? {expertUserId: item?.comment?.user?.id}
                    : {params: item?.comment?.user?.id}),
                },
              )
            }>
            <ProfileImage
              url={item?.comment?.user?.image}
              size={45}
              style={{marginRight: 10}}
            />
          </TouchAwareButton>

          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              <Text style={{fontFamily: 'Inter-Medium'}}>
                <Text style={{fontWeight: '700'}}>
                  {item?.comment?.user?.username}
                </Text>{' '}
                {
                  {
                    [NOTIFICATION_TYPES.NEW_COMMENT_ON_POST]:
                      locale.hasCommentedOnYourPost,
                    [NOTIFICATION_TYPES.SUBSCRIPTION_NEW_COMMENT_TO_POST]:
                      'has commented on a post you are subscribed to.',
                  }?.[item?.type]
                }
              </Text>{' '}
              {item?.type === NOTIFICATION_TYPES.NEW_COMMENT_ON_POST ? (
                <Text style={styles.authenticationTitle}>
                  {item?.comment?.post?.title}.
                </Text>
              ) : null}{' '}
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
    // Map the screen
  } else if (
    [
      NOTIFICATION_TYPES.NEW_COMMENT_ON_POST_IMAGE,
      NOTIFICATION_TYPES.SUBSCRIPTION_NEW_COMMENT_TO_POST_IMAGE,
    ].includes(item?.type)
  ) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.POST_IMAGE_COMMENT, {
            postId: item?.comment?.post?.id,
            image_name: item?.comment?.item_image,
            showPostNavigationLink: true,
          })
        }>
        <View style={styles.descWrapper}>
          <TouchAwareButton
            onPress={() =>
              navigation.navigate(
                item?.comment?.user?.role == 'EXPERT'
                  ? screens.EXPERTS_PROFILE
                  : screens.PUBLIC_PROFILES,
                {
                  ...(item?.followed_by?.role == 'EXPERT'
                    ? {expertUserId: item?.comment?.user?.id}
                    : {params: item?.comment?.user?.id}),
                },
              )
            }>
            <ProfileImage
              url={item?.comment?.user?.image}
              size={45}
              style={{marginRight: 10}}
            />
          </TouchAwareButton>
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              <Text style={{fontWeight: '700'}}>
                {item?.comment?.user?.username}
              </Text>{' '}
              {
                {
                  [NOTIFICATION_TYPES.NEW_COMMENT_ON_POST_IMAGE]:
                    locale.hasCommentedOnYourPost,
                  [NOTIFICATION_TYPES.SUBSCRIPTION_NEW_COMMENT_TO_POST_IMAGE]:
                    'has commented on a post image that you are subscribed to.',
                }?.[item?.type]
              }{' '}
              {item?.type === NOTIFICATION_TYPES.NEW_COMMENT_ON_POST_IMAGE ? (
                <>
                  <Text style={styles.authenticationTitle}>
                    {item?.comment?.post?.title}{' '}
                  </Text>
                  {locale.image}.
                </>
              ) : null}
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (item?.type === NOTIFICATION_TYPES.NFT_TRANSFERRED) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item?.post?.id,
          })
        }>
        <View style={styles.descWrapper}>
          <ProfileImage
            url={item?.post?.images?.featured || item?.post?.images?.front}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              {locale.YourNFTForAuthentication}{' '}
              <Text style={styles.authenticationTitle}>
                {item?.post?.title}
              </Text>{' '}
              {locale.hasBeenTransferred}.
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (
    [
      NOTIFICATION_TYPES.AUTHENTICATION_REVIEWED_BY_EXPERT,
      NOTIFICATION_TYPES.SUBSCRIPTION_AUTHENTICATION_REVIEWED_BY_EXPERT,
    ].includes(item?.type)
  ) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item?.post?.id,
          })
        }>
        <View style={styles.descWrapper}>
          <ProfileImage
            url={item?.post?.images?.featured || item?.post?.images?.front}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              {item?.type ===
              NOTIFICATION_TYPES.AUTHENTICATION_REVIEWED_BY_EXPERT ? (
                <>
                  {locale.TheResultsForYourExpertServicesForA}{' '}
                  <Text style={styles.authenticationTitle}>
                    {item?.post?.title}
                  </Text>{' '}
                  {locale.areReady}
                </>
              ) : (
                locale['TheResultsForAPostYouAreSubscribed...']
              )}
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (item?.type === NOTIFICATION_TYPES.NEW_MENTION_ON_POST_COMMENT) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.COMMENTS, {
            postId: item?.comment?.post?.id,
            showPostNavigationLink: true,
          })
        }>
        <View style={styles.descWrapper}>
          <TouchAwareButton
            onPress={() =>
              navigation.navigate(
                item?.comment?.user?.role == 'EXPERT'
                  ? screens.EXPERTS_PROFILE
                  : screens.PUBLIC_PROFILES,
                {
                  ...(item?.followed_by?.role == 'EXPERT'
                    ? {expertUserId: item?.comment?.user?.id}
                    : {params: item?.comment?.user?.id}),
                },
              )
            }>
            <ProfileImage
              url={item?.comment?.user?.image}
              size={45}
              style={{marginRight: 10}}
            />
          </TouchAwareButton>

          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              <Text style={{fontFamily: 'Inter-Medium'}}>
                {item?.comment?.user?.username} {locale.hasMentionedYouOnAPost}
              </Text>{' '}
              <Text style={styles.authenticationTitle}>
                {item?.comment?.post?.title}.
              </Text>{' '}
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (
    item?.type === NOTIFICATION_TYPES.NEW_MENTION_ON_POST_IMAGE_COMMENT
  ) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.POST_IMAGE_COMMENT, {
            postId: item?.comment?.post?.id,
            image_name: item?.comment?.item_image,
            showPostNavigationLink: true,
          })
        }>
        <View style={styles.descWrapper}>
          <TouchAwareButton
            onPress={() =>
              navigation.navigate(
                item?.comment?.user?.role == 'EXPERT'
                  ? screens.EXPERTS_PROFILE
                  : screens.PUBLIC_PROFILES,
                {
                  ...(item?.followed_by?.role == 'EXPERT'
                    ? {expertUserId: item?.comment?.user?.id}
                    : {params: item?.comment?.user?.id}),
                },
              )
            }>
            <ProfileImage
              url={item?.comment?.user?.image}
              size={45}
              style={{marginRight: 10}}
            />
          </TouchAwareButton>
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              <Text style={{fontWeight: '700'}}>
                {item?.comment?.user?.username}
              </Text>{' '}
              {locale.hasMentionedYouOnAPost}{' '}
              <Text style={styles.authenticationTitle}>
                {item?.comment?.post?.title}
              </Text>{' '}
              {locale.image}.
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (item?.type === NOTIFICATION_TYPES.NEW_LIKE_ON_POST) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item?.post_id,
          })
        }>
        <View style={styles.descWrapper}>
          <TouchAwareButton
            onPress={() =>
              navigation.navigate(
                item?.user?.role == 'EXPERT'
                  ? screens.EXPERTS_PROFILE
                  : screens.PUBLIC_PROFILES,
                {
                  ...(item?.user?.role == 'EXPERT'
                    ? {expertUserId: item?.user?.id}
                    : {params: item?.user?.id}),
                },
              )
            }>
            <ProfileImage
              url={item?.user?.image}
              size={45}
              style={{marginRight: 10}}
            />
          </TouchAwareButton>

          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              <Text style={{fontWeight: '700'}}>{item?.user?.username}</Text>{' '}
              {locale.likedYourPost}{' '}
              <Text style={styles.authenticationTitle}>
                {item?.post?.title}.
              </Text>
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (
    [
      NOTIFICATION_TYPES.COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED,
      NOTIFICATION_TYPES.SUBSCRIPTION_COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED,
    ].includes(item?.type)
  ) {
    return (
      <TouchAwareButton
        style={styles.notificationsCard}
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {
            params: item?.post?.id,
          })
        }>
        <View style={styles.descWrapper}>
          <ProfileImage
            url={item?.post?.images?.featured || item?.post?.images?.front}
            size={45}
            style={{marginRight: 10}}
          />
          <View style={{flex: 1, flexDirection: 'column'}}>
            <Text style={[styles.text, {flex: 1}]}>
              {item?.type ===
              NOTIFICATION_TYPES.COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED ? (
                <>
                  The results of your community authentication{' '}
                  <Text style={styles.authenticationTitle}>
                    {item?.post?.title}
                  </Text>{' '}
                  are ready.
                </>
              ) : (
                'The results of a post you are subscribed to are ready.'
              )}
            </Text>
            <Text style={[styles.date]}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
          </View>
        </View>
      </TouchAwareButton>
    );
  } else if (item?.type === NOTIFICATION_TYPES.OWNERSHIP_HISTORY_TAGGED) {
    return <NotificationOwnershipHistoryTagged item={item} />;
  } else if (
    item?.type === NOTIFICATION_TYPES.OWNERSHIP_HISTORY_REQUESTED_TO_BE_TAGGED
  ) {
    return <NotificationHistoryRequestedToBeTagged item={item} />;
  } else {
    return null;
  }
}

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  imagesStyles: {
    height: 45,
    width: 45,
    borderRadius: 45,
    borderWidth: 2,
    borderColor: colors.grey,
    marginRight: spacings.md,
  },
  notificationsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacings.lg,
  },
  iconWrapper: {
    alignItems: 'flex-end',
    padding: spacings.sm,
  },
  descWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyView: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyViewText: {
    marginTop: 150,
    color: colors.black,
    fontSize: fontSize.md,
  },
  userName: {
    color: colors.black,
    fontFamily: 'Inter-Bold',
  },
  authenticationTitle: {
    color: colors.black,
    fontFamily: 'Inter-Bold',
    textTransform: 'capitalize',
  },
  text: {
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
  description: {
    fontSize: 12,
    color: colors.black,
    fontFamily: 'Inter-Regular',
    opacity: 0.9,
  },
  date: {
    color: colors.darkGrey,
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
});

export default React.memo(NotificationRow);
