PODS:
  - boost-for-react-native (1.63.0)
  - BVLinearGradient (2.6.2):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.64.4)
  - FBReactNativeSpec (0.64.4):
    - RCT-<PERSON>olly (= 2020.01.13.00)
    - RCTRequired (= 0.64.4)
    - RCTTypeSafety (= 0.64.4)
    - React-Core (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - Firebase/CoreOnly (8.15.0):
    - FirebaseCore (= 8.15.0)
  - Firebase/Messaging (8.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 8.15.0)
  - FirebaseCore (8.15.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.15.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Reachability (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - nanopb (~> 2.30908.0)
  - glog (0.3.5)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - lottie-ios (3.4.4)
  - lottie-react-native (5.1.6):
    - lottie-ios (~> 3.4.0)
    - React-Core
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - Permission-Camera (3.8.0):
    - RNPermissions
  - Permission-MediaLibrary (3.8.0):
    - RNPermissions
  - Permission-Microphone (3.8.0):
    - RNPermissions
  - Permission-Notifications (3.8.0):
    - RNPermissions
  - Permission-PhotoLibrary (3.8.0):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - RCT-Folly (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - RCT-Folly/Default (= 2020.01.13.00)
  - RCT-Folly/Default (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - RCTRequired (0.64.4)
  - RCTTypeSafety (0.64.4):
    - FBLazyVector (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.4)
    - React-Core (= 0.64.4)
  - React (0.64.4):
    - React-Core (= 0.64.4)
    - React-Core/DevSupport (= 0.64.4)
    - React-Core/RCTWebSocket (= 0.64.4)
    - React-RCTActionSheet (= 0.64.4)
    - React-RCTAnimation (= 0.64.4)
    - React-RCTBlob (= 0.64.4)
    - React-RCTImage (= 0.64.4)
    - React-RCTLinking (= 0.64.4)
    - React-RCTNetwork (= 0.64.4)
    - React-RCTSettings (= 0.64.4)
    - React-RCTText (= 0.64.4)
    - React-RCTVibration (= 0.64.4)
  - React-callinvoker (0.64.4)
  - React-Core (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/CoreModulesHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/Default (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/DevSupport (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.4)
    - React-Core/RCTWebSocket (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-jsinspector (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTBlobHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTImageHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTTextHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-Core/RCTWebSocket (0.64.4):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsiexecutor (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - Yoga
  - React-CoreModules (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/CoreModulesHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-RCTImage (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-cxxreact (0.64.4):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-jsinspector (= 0.64.4)
    - React-perflogger (= 0.64.4)
    - React-runtimeexecutor (= 0.64.4)
  - React-jsi (0.64.4):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-jsi/Default (= 0.64.4)
  - React-jsi/Default (0.64.4):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
  - React-jsiexecutor (0.64.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-perflogger (= 0.64.4)
  - React-jsinspector (0.64.4)
  - react-native-blur (0.8.0):
    - React
  - react-native-blurhash (1.1.10):
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-cameraroll (5.6.0):
    - React-Core
  - react-native-get-random-values (1.9.0):
    - React-Core
  - react-native-netinfo (6.2.1):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-randombytes (3.6.1):
    - React-Core
  - react-native-safe-area-context (3.2.0):
    - React-Core
  - react-native-webview (11.26.1):
    - React-Core
  - React-perflogger (0.64.4)
  - React-RCTActionSheet (0.64.4):
    - React-Core/RCTActionSheetHeaders (= 0.64.4)
  - React-RCTAnimation (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTAnimationHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTBlob (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTBlobHeaders (= 0.64.4)
    - React-Core/RCTWebSocket (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-RCTNetwork (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTImage (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTImageHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-RCTNetwork (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTLinking (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - React-Core/RCTLinkingHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTNetwork (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTNetworkHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTSettings (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.4)
    - React-Core/RCTSettingsHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-RCTText (0.64.4):
    - React-Core/RCTTextHeaders (= 0.64.4)
  - React-RCTVibration (0.64.4):
    - FBReactNativeSpec (= 0.64.4)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTVibrationHeaders (= 0.64.4)
    - React-jsi (= 0.64.4)
    - ReactCommon/turbomodule/core (= 0.64.4)
  - React-runtimeexecutor (0.64.4):
    - React-jsi (= 0.64.4)
  - ReactCommon/turbomodule/core (0.64.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.4)
    - React-Core (= 0.64.4)
    - React-cxxreact (= 0.64.4)
    - React-jsi (= 0.64.4)
    - React-perflogger (= 0.64.4)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNBootSplash (4.7.5):
    - React-Core
  - RNCAsyncStorage (1.13.4):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCMaskedView (0.2.9):
    - React-Core
  - RNDeviceInfo (8.7.1):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (14.12.0):
    - Firebase/CoreOnly (= 8.15.0)
    - React-Core
  - RNFBMessaging (14.12.0):
    - Firebase/Messaging (= 8.15.0)
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNIap (8.6.7):
    - React-Core
  - RNImageCropPicker (0.37.3):
    - React-Core
    - React-RCTImage
    - RNImageCropPicker/QBImagePickerController (= 0.37.3)
    - TOCropViewController
  - RNImageCropPicker/QBImagePickerController (0.37.3):
    - React-Core
    - React-RCTImage
    - TOCropViewController
  - RNNotifee (4.1.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 4.1.0)
  - RNNotifee/NotifeeCore (4.1.0):
    - React-Core
  - RNOS (1.2.6):
    - React
  - RNPermissions (3.8.0):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.4.0):
    - React-Core
    - React-RCTImage
  - RNShare (7.9.1):
    - React-Core
  - RNSVG (9.6.4):
    - React
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - TOCropViewController (2.7.4)
  - Yoga (1.14.0)

DEPENDENCIES:
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - lottie-ios (from `../node_modules/lottie-ios`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-MediaLibrary (from `../node_modules/react-native-permissions/ios/MediaLibrary`)
  - Permission-Microphone (from `../node_modules/react-native-permissions/ios/Microphone`)
  - Permission-Notifications (from `../node_modules/react-native-permissions/ios/Notifications`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-blurhash (from `../node_modules/react-native-blurhash`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - "react-native-cameraroll (from `../node_modules/@react-native-camera-roll/camera-roll`)"
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-randombytes (from `../node_modules/react-native-randombytes`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - RNBootSplash (from `../node_modules/react-native-bootsplash`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-community/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNIap (from `../node_modules/react-native-iap`)
  - RNImageCropPicker (from `../node_modules/react-native-image-crop-picker`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNOS (from `../node_modules/react-native-os`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - boost-for-react-native
    - Firebase
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - libwebp
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - TOCropViewController

EXTERNAL SOURCES:
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  lottie-ios:
    :path: "../node_modules/lottie-ios"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-MediaLibrary:
    :path: "../node_modules/react-native-permissions/ios/MediaLibrary"
  Permission-Microphone:
    :path: "../node_modules/react-native-permissions/ios/Microphone"
  Permission-Notifications:
    :path: "../node_modules/react-native-permissions/ios/Notifications"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-blurhash:
    :path: "../node_modules/react-native-blurhash"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-camera-roll/camera-roll"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-randombytes:
    :path: "../node_modules/react-native-randombytes"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNBootSplash:
    :path: "../node_modules/react-native-bootsplash"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-community/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNIap:
    :path: "../node_modules/react-native-iap"
  RNImageCropPicker:
    :path: "../node_modules/react-native-image-crop-picker"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNOS:
    :path: "../node_modules/react-native-os"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  BVLinearGradient: 34a999fda29036898a09c6a6b728b0b4189e1a44
  DoubleConversion: cf9b38bf0b2d048436d9a82ad2abe1404f11e7de
  FBLazyVector: fa8275d5086566e22a26ddc385ab5772e7f9b1bd
  FBReactNativeSpec: 18ecb19647e612471fbb15fd830612a703b3dd9e
  Firebase: 5f8193dff4b5b7c5d5ef72ae54bb76c08e2b841d
  FirebaseCore: 5743c5785c074a794d35f2fff7ecc254a91e08b1
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: 5e5118a2383b3531e730d974680954c679ca0a13
  glog: 73c2498ac6884b13ede40eda8228cb1eee9d9d62
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  lottie-ios: 8f97d3271e155c2d688875c29cd3c74908aef5f8
  lottie-react-native: 8f9d4be452e23f6e5ca0fdc11669dc99ab52be81
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  Permission-Camera: e6d142d7d8b714afe0a83e5e6ae17eb949f1e3e9
  Permission-MediaLibrary: 6a5888dd77669b568716bea80c21340aaab74c51
  Permission-Microphone: 644b1de8bcc2afcaf934e09a22bee507a95796a7
  Permission-Notifications: aa91ec29236626ff59cb60e08389c0a59a9d32c5
  Permission-PhotoLibrary: 31787bbe77d0d3ae6a5267b8435e4a2e9ef78f1d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: ec7a233ccc97cc556cf7237f0db1ff65b986f27c
  RCTRequired: f85fa00af016059cf88b90b8f8ff9a6af9e4b6c3
  RCTTypeSafety: 5279aaf0fb1ad715cbbbbee32d5c98c72598bc9c
  React: ff4e89fbcb05461c9533fd4da3c0f44cda6ab618
  React-callinvoker: 4670ac7842699e4a39b19a08b4ede02573c1e5dd
  React-Core: ee61e8a8aea912e1504ebec230b3e07d96cf82e1
  React-CoreModules: 8544ba0d319003b33707cdeed66e3122685d31a0
  React-cxxreact: 668500d4ce359515bbf8f907bca0e66fd357d36f
  React-jsi: 64f80675a66899bf0f4a58b8e3908966fa516234
  React-jsiexecutor: 8c077bef1c64430b6034f27df1000d194551e2eb
  React-jsinspector: d4f6973dd474357dbaaf6f52f31ffc713bf3e766
  react-native-blur: cad4d93b364f91e7b7931b3fa935455487e5c33c
  react-native-blurhash: add4df9a937b4e021a24bc67a0714f13e0bd40b7
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-cameraroll: 755bcc628148a90a7c9cf3f817a252be3a601bc5
  react-native-get-random-values: dee677497c6a740b71e5612e8dbd83e7539ed5bb
  react-native-netinfo: 3d3769f0d65de15c83a9bf1346f8be71de5a24bf
  react-native-pager-view: da490aa1f902c9a5aeecf0909cc975ad0e92e53e
  react-native-randombytes: 421f1c7d48c0af8dbcd471b0324393ebf8fe7846
  react-native-safe-area-context: f0906bf8bc9835ac9a9d3f97e8bde2a997d8da79
  react-native-webview: 9f111dfbcfc826084d6c507f569e5e03342ee1c1
  React-perflogger: 5a890ca0911669421b7611661e9b58f91c805f5c
  React-RCTActionSheet: bd180e0879f8424a73650c5c28fbef4f3b5b27fb
  React-RCTAnimation: 1004d2b4be1f2cedfdc4cb2326adc95b989e6c6b
  React-RCTBlob: 55a984137d10e4c41071e8d39374336b48656e54
  React-RCTImage: fa346c899c2f7301eddc79860c37f836db185eb2
  React-RCTLinking: 7af1444a261610ff29a1afeab8c1cfcb5c3280a9
  React-RCTNetwork: fdaad596311091a2c48e0327b8431ba3e011a684
  React-RCTSettings: a7879d48e2951d107763f53e1db80be29f462ab7
  React-RCTText: d7e66b2600487f631531f77bb9d336e33c1187d9
  React-RCTVibration: 761849eea2a1abc99d5e4171bae17ab3da3143ac
  React-runtimeexecutor: 5b441857030bb6c3abaa7517f333cb01875ae499
  ReactCommon: b4a65d2d6e9eeffd4b32dde1245962b3f43907d0
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNBootSplash: 85f6b879c080e958afdb4c62ee04497b05fd7552
  RNCAsyncStorage: 44539979e1f234262d64d3ce86ec21cceb1b2c5e
  RNCClipboard: 41d8d918092ae8e676f18adada19104fa3e68495
  RNCMaskedView: 949696f25ec596bfc697fc88e6f95cf0c79669b6
  RNDeviceInfo: aad3c663b25752a52bf8fce93f2354001dd185aa
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBApp: e4439717c23252458da2b41b81b4b475c86f90c4
  RNFBMessaging: 40dac204b4197a2661dec5be964780c6ec39bf65
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: a479ebd5ed4221a810967000735517df0d2db211
  RNIap: 9bc008662cc46865ca0919267fb2f54afb99ebd1
  RNImageCropPicker: 44e2807bc410741f35d4c45b6586aedfe3da39d2
  RNNotifee: f1ba270897bd7491f889a1a257d0c038a43376f7
  RNOS: 6f2f9a70895bbbfbdad7196abd952e7b01d45027
  RNPermissions: 215c54462104b3925b412b0fb3c9c497b21c358b
  RNReanimated: 39e61976e5b5c8a891ea8916fb264ae176e8765d
  RNScreens: 21b73c94c9117e1110a79ee0ee80c93ccefed8ce
  RNShare: a5dc3b9c53ddc73e155b8cd9a94c70c91913c43c
  RNSVG: a53a5de0d90ec96a6502cc3a4534dfcc0c130622
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  Yoga: d1fc3575b8b68891ff5ef3c276daa855e841eb32

PODFILE CHECKSUM: 7d26e6b1460f71b3f27f2f9cd47414b614b2b1e1

COCOAPODS: 1.15.2
