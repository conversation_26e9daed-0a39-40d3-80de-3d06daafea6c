// @ts-check
import React from 'react';
import {getVersion} from 'react-native-device-info';
import {Platform} from 'react-native';

import {GET_APP_DETAILS} from '../api';

const APP_DETAILS_INITIAL_DATA = {
  appVersion: getVersion(),
  appVersionFromServer: getVersion(), // appVersion === appVersionFromServer should always be true at first
  storeURL: null,
  loading: false,
};

function useAuthDetails({manuallyRefetch = false}) {
  const [appData, setAppData] = React.useState(APP_DETAILS_INITIAL_DATA);

  const fetch = React.useCallback(async () => {
    setAppData(previousData => ({
      ...previousData,
      loading: true,
    }));
    try {
      const {data: response} = await GET_APP_DETAILS();
      if (response?.success) {
        const data = response?.data;

        const appVersionFromServer =
          data?.appVersion?.[Platform.OS.toLowerCase()];
        const appStoreUrl = data?.appStoreUrl;
        const playStoreUrl = data?.playStoreUrl;

        setAppData(previousData => ({
          ...previousData,
          appVersionFromServer,
          storeURL: Platform.OS === 'ios' ? appStoreUrl : playStoreUrl,
        }));
      }
    } catch (_) {
    } finally {
      setAppData(previousData => ({
        ...previousData,
        loading: false,
      }));
    }
  }, []);

  React.useEffect(() => {
    if (!manuallyRefetch) {
      fetch();
    }
  }, [fetch, manuallyRefetch]);

  return {appData, refetch: fetch};
}

export default useAuthDetails;
