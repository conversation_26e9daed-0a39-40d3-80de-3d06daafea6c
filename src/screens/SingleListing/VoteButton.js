import React, {useState} from 'react';
import {TouchableOpacity, Text, View, Keyboard} from 'react-native';
import {BottomSheetTextInput as TextInput} from '@gorhom/bottom-sheet';

import {useMutation} from '@apollo/client';
import dayjs from 'dayjs';
import BottomSheet from '../../components/BottomSheet';
import {ADD_VOTE_TO_POST} from '../../apollo/mutations';
import {styles} from './styles';
import {icons, singleListingScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import Icon from '../../components/Icon';
import Button from '../../components/Button';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';

const keys = {
  ADD_VOTE: 'ADD_VOTE',
  SHOW_VOTE: 'SHOW_VOTE',
};

const VoteButton = ({data, refetch = () => null}) => {
  const [sheetVisible, setSheetVisible] = React.useState({
    key: keys.ADD_VOTE,
    visible: false,
  });

  const handleExpandPress = (key = keys.ADD_VOTE) => {
    setSheetVisible({
      visible: true,
      key,
    });
  };

  const handleClose = () => {
    setSheetVisible({
      visible: false,
      key: keys.ADD_VOTE,
    });
  };

  const didIVoteAlready = data && !(data?.getPost?.data?.my_vote == null);
  const myDecision = data?.getPost?.data?.my_vote?.decision;
  const myAppraisalValue = data?.getPost?.data?.my_vote?.appraisal_value;
  const actualDecision = {REAL: 'Pass', FAKE: 'Fail'}[myDecision] ?? null;
  const comments = data?.getPost?.data?.my_vote?.remarks;
  const isVotingClosed = data?.getPost?.data?.is_voting_closed;

  return (
    <>
      <View style={styles.voteButtonContainer}>
        {didIVoteAlready ? (
          <Button
            style={styles.showResults}
            onPress={handleExpandPress.bind(null, keys.SHOW_VOTE)}>
            <Icon
              name={myDecision === 'FAKE' ? icons.CANCEL_X : icons.PASS_ICON}
              size={16}
              tint={myDecision === 'FAKE' ? colors.warning : colors.green}
            />
            <Text style={[styles.showResultsLabel, {marginLeft: 10}]}>
              {singleListingScreen.YOU_VOTED}
              {actualDecision}
            </Text>
          </Button>
        ) : !isVotingClosed ? (
          <Button
            containerStyle={{
              ...styles.voteButton,
              width: '100%',
            }}
            style={{width: '100%'}}
            onPress={handleExpandPress}>
            <Text style={styles.voteButtonLabel}>
              {singleListingScreen.VOTE}
            </Text>
          </Button>
        ) : null}
      </View>

      <BottomSheet
        visible={sheetVisible.visible}
        setVisible={handleClose}
        automaticallyAdjustHeight
        key={sheetVisible.key}>
        {sheetVisible.key === keys.ADD_VOTE ? (
          <VoteSheet data={data} refetch={refetch} closeSheet={handleClose} />
        ) : (
          <View style={styles.visibleSection}>
            <Text style={styles.modalTitle}>
              Voted{' '}
              <Text
                style={{
                  color: myDecision === 'REAL' ? colors.green : colors.warning,
                }}>
                {actualDecision}
              </Text>
            </Text>
            <Text style={[styles.modal, {marginBottom: spacings.md}]}>
              Voted on:{' '}
              <Text
                style={{
                  fontFamily: 'Inter-Bold',
                }}>
                {dayjs(data?.getPost?.data?.my_vote?.created_at).format(
                  'DD/MM/YYYY',
                )}
              </Text>
            </Text>
            <Text style={[styles.modal, {marginBottom: spacings.md}]}>
              Appraisal:{' '}
              <Text
                style={{
                  fontFamily: 'Inter-Bold',
                }}>
                ${myAppraisalValue ? myAppraisalValue : null}
              </Text>
            </Text>
            {comments?.length ? (
              <View style={{flexDirection: 'row'}}>
                <Text style={styles.modal}>Comment: </Text>
                <Text numberOfLines={4} style={[styles.modal]}>
                  <Text
                    style={{
                      color: colors.darkGrey,
                    }}>
                    {comments}
                  </Text>
                </Text>
              </View>
            ) : null}
          </View>
        )}
      </BottomSheet>
    </>
  );
};

function VoteSheet({data, refetch = () => null, closeSheet = () => null}) {
  const [vote, setVote] = useState(false);
  const [commentValue, setCommentValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [appraisalText, setAppraisalText] = useState(null);
  const [error, setError] = useState(null);
  const [infoModal, setInfoModal] = React.useState(false);

  const [addVoteToPost] = useMutation(ADD_VOTE_TO_POST, {
    onCompleted: refetch,
  });

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handleClosePress = () => {
    closeSheet(false);
  };

  const handleVoting = async () => {
    if (!appraisalText || !vote) {
      setError(locale.PleaseFullOutAllTheRequiredFields);
      return;
    }
    if (vote == 'FAKE' && commentValue?.length === 0) {
      setError(locale['PleaseClearlyExplainYourReasons...']);
      return;
    }
    Keyboard.dismiss();
    setIsLoading(true);
    try {
      const {data: voteData} = await addVoteToPost({
        variables: {
          postId: data?.getPost?.data?.id,
          decision: vote,
          appraisal_value: parseFloat(appraisalText),
          ...(commentValue?.length ? {comment: commentValue} : {}),
        },
      });
      let isSuccess = voteData?.addVoteToPost?.success;
      const code = voteData?.addVoteToPost?.code;
      if (code === 'ALREADY_VOTED_FOR_THIS_AUTHENTICATION') {
        isSuccess = true;
      }
      if (isSuccess) {
        await refetch?.();
        handleClosePress();
      } else {
        throw new Error();
      }
    } catch (e) {
      setInfoModal(true);
    } finally {
      setIsLoading(false);
    }
  };

  const startVote = value => {
    setVote(value);
  };

  return (
    <>
      <TouchableOpacity
        activeOpacity={1}
        onPressIn={dismissKeyboard}
        style={[{padding: 20}]}>
        <>
          <Text style={styles.formLabelHeading}>
            {singleListingScreen.VOTE}
          </Text>
          <Text style={styles.formLabel}>
            <Text style={{color: colors.warning}}>*</Text>{' '}
            {singleListingScreen.YOUR_VOTE}
          </Text>
          <View style={styles.smallVoteButtonWrapper}>
            <TouchableOpacity
              style={[
                styles.smallVoteButtons,
                vote === 'REAL' ? {backgroundColor: colors.black} : null,
              ]}
              onPress={() => {
                startVote('REAL');
                setError('');
              }}>
              <Icon name={icons.PASS_ICON} size={15} tint={colors.green} />
              <Text
                style={[
                  styles.voteButtonLabel,
                  vote === 'REAL'
                    ? {color: colors.black, marginRight: spacings.md}
                    : null,
                  {color: colors.white, marginRight: spacings.md},
                ]}>
                Pass
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.smallVoteButtons,
                vote === 'FAKE' ? {backgroundColor: colors.black} : null,
              ]}
              onPress={() => {
                startVote('FAKE');
                setError('');
              }}>
              <Icon name={icons.CANCEL_X} size={12} tint={colors.warning} />
              <Text
                style={[
                  styles.voteButtonLabel,
                  vote === 'FAKE'
                    ? {color: colors.black, marginRight: spacings.md}
                    : null,
                  {color: colors.white, marginRight: spacings.md},
                ]}>
                Fail
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles.formWrapper}>
            <Text style={styles.formLabel}>
              <Text style={{color: colors.warning}}>*</Text>{' '}
              {singleListingScreen.YOUR_APPRAISAL}
            </Text>
            <TextInput
              value={appraisalText}
              onChangeText={value => {
                setAppraisalText(value);
                setError('');
              }}
              style={styles.textField}
              placeholderTextColor={colors.grey}
              selectionColor={colors.grey}
              keyboardType="numeric"
            />
          </View>
          <View style={styles.formWrapper}>
            <Text style={styles.formLabel}>
              {' '}
              {vote === 'FAKE' ? (
                <Text style={{color: colors.warning}}>* </Text>
              ) : null}
              {singleListingScreen.COMMENT}
            </Text>
            <TextInput
              value={commentValue}
              onChangeText={value => {
                setCommentValue(value);
                setError('');
              }}
              style={styles.textField}
              placeholderTextColor={colors.grey}
              selectionColor={colors.grey}
            />
          </View>
          {error ? (
            <Text
              style={{
                color: colors.warning,
                fontSize: fontSize.sm,
                marginTop: spacings.md,
              }}>
              {error}
            </Text>
          ) : null}
          <Button
            style={styles.submitButton}
            onPress={handleVoting}
            isLoading={isLoading}
            text={singleListingScreen.SUBMIT}
            disabled={isLoading}
          />
        </>
      </TouchableOpacity>
      <InfoModal
        setVisible={setInfoModal}
        popUp={{
          state: infoModal,
          isError: true,
          data: {
            title: locale.Error,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }}
      />
    </>
  );
}

export default VoteButton;
