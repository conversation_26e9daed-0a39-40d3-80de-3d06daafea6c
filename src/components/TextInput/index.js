import {View, Text, TextInput as NativeTextInput, Platform} from 'react-native';
import React from 'react';
import Animated, {FadeInUp, Layout} from 'react-native-reanimated';

import styles from './styles';
import colors from '../../assets/colors';

const isIOS = Platform.OS === 'ios';

function TextInput({
  value,
  onChange = () => null,
  placeholder = '',
  style = {},
  disabled = false,
  keyboardType = 'default',
  containerStyle = {},
  isInvalid = false,
  invalidText = '',
  multiline = false,
  numberOfLines = 1,
  maxLength = null,
  textAlignVertical = null,
  textArea = false,
  placeholderTextColor = colors.grey,
  children = null,
}) {
  return (
    <View style={containerStyle}>
      <NativeTextInput
        value={value}
        editable={!disabled}
        onChangeText={onChange}
        placeholder={placeholder}
        autoCorrect={false}
        autoCapitalize="none"
        multiline={multiline}
        style={[
          styles.input,
          {
            borderColor: isInvalid ? colors.warning : colors.grey,
            ...(textArea ? {minHeight: 80, maxHeight: 80} : {}),
          },
          style,
        ]}
        placeholderTextColor={placeholderTextColor}
        keyboardType={keyboardType}
        numberOfLines={numberOfLines}
        {...(maxLength == null ? {} : {maxLength})}
        {...(textAlignVertical?.length ? {textAlignVertical} : {})}
      />
      {isInvalid ? <InvalidTextComponent text={invalidText} /> : null}
      {children}
    </View>
  );
}

export function InvalidTextComponent({text = '', style = {}}) {
  return (
    <Animated.View
      entering={FadeInUp.springify()
        .damping(isIOS ? 200 : 300)
        .stiffness(1000)}
      layout={Layout}>
      <Text style={[styles.errorText, style]}>{text}</Text>
    </Animated.View>
  );
}

export default TextInput;
