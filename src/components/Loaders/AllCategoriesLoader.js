import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH / 2 - spacings.xl;

const AllCategoriesLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        {new Array(10).fill(100).map((_, index) => (
          <SkeletonPlaceholder.Item
            key={index.toString()}
            flexDirection="row"
            justifyContent="space-between">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={20}
              height={CARD_WIDTH / 2}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={20}
              height={CARD_WIDTH / 2}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        ))}
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
});

export default AllCategoriesLoader;
