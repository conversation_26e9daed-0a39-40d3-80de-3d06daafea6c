import React from 'react'
import { useNavigation } from '@react-navigation/native'

import checkPermission from '../../tools/permissions'
import { permissionStorage } from '../../tools/utils'

export default (WrappedComponent, willStay = true, requireAlert = true) => (
  props
) => {
  const [granted, setGranted] = React.useState(false)
  const navigation = useNavigation()

  
  React.useEffect(() => {
    checkPermission(
      permissionStorage,
      setGranted,
      willStay,
      requireAlert,
      true,
      navigation
    )
  }, [navigation, setGranted])

  return (
    <WrappedComponent
      {...props}
      storagePermission={granted}
      setStorageGranted={setGranted}
    />
  )
}

// Go to  withCameraPermission HOC for argument details
