import React from 'react';
import {Calendar} from 'react-native-calendars';

import Modal from '../../components/Modal';
import colors from '../../assets/colors';

export default function CalendarPicker({
  visible = true,
  setVisible = () => null,
  onDayPress = () => null,
  date,
}) {
  return (
    <Modal
      visible={visible}
      setVisible={setVisible}
      containerStyle={{padding: 0}}
      blurBackground>
      <Calendar
        theme={{
          selectedDayBackgroundColor: colors.primary,
          selectedDotColor: colors.primary,
          arrowColor: colors.primary,
        }}
        style={{minWidth: '100%'}}
        onDayPress={onDayPress}
        {...(date?.length > 0 ? {initialDate: date} : {})}
      />
    </Modal>
  );
}
