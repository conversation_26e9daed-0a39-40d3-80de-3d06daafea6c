/* eslint-disable prettier/prettier */
import React, {useCallback} from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {useQuery} from '@apollo/client';
import Ionicons from 'react-native-vector-icons/Ionicons';

import {GET_ALL_POSTS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import FeedCard from '../../components/FeedCard';
import {screens, icons, feedScreen} from '../../assets/strings';
import {fontSize} from '../../assets/font';

const Index = ({navigation}) => {
  const {data, loading, error} = useQuery(GET_ALL_POSTS, {
    variables: {
      filters: {
        myFollowing: true,
      },
    },
  });

  const renderItem = useCallback(
    ({item}) => (
      <TouchableOpacity
        onPress={() =>
          navigation.navigate(screens.SINGLE_LISTING, {params: item.id})
        }>
        <FeedCard item={item} />
      </TouchableOpacity>
    ),
    [navigation],
  );

  if (!error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorContainerText}>Something went wrong.</Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.emptyContainer}>
        <ActivityIndicator color={colors.primary} />
      </View>
    );
  }

  const Empty = () => {
    return (
      <View style={styles.emptyView}>
        <Ionicons
          name={icons.IMAGE_OUTLINE}
          size={70}
          color={colors.darkGrey}
        />
        <Text style={styles.emptyViewText}>{feedScreen.NO_POSTS}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={data?.getPosts.data.edges}
        columnWrapperStyle={styles.contentContainer}
        renderItem={renderItem}
        keyExtractor={item => item.id.toString()}
        numColumns={2}
        ListHeaderComponent={<View style={{paddingTop: spacings.lg}} />}
        ListEmptyComponent={<Empty />}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  contentContainer: {
    justifyContent: 'space-between',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.lg,
    marginTop: spacings.lg,
  },
  emptyView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});

export default Index;
