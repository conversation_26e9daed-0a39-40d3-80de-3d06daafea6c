import React from 'react';
import {View, Text} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import Modal from '../../components/Modal';
import BaseLayout from '../../layout';
import useLogout from '../../tools/hooks/useLogout';
import colors from '../../assets/colors';
import locale from '../../assets/locale.json';
import ActivityIndicator from '../../components/ActivityIndicator';

const ForceLogout = () => {
  const navigation = useNavigation();

  const onError = () => {
    navigation.canGoBack() && navigation.goBack();
  };

  const {logout} = useLogout({onError});

  React.useEffect(() => {
    logout();
  }, [logout]);

  return (
    <BaseLayout scrollChildren={false}>
      <Modal visible style={{minHeight: 100}}>
        <View>
          <Text
            style={{
              fontSize: 20,
              marginBottom: 15,
              fontFamily: 'Inter-Bold',
            }}>
            {locale.SessionExpired}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <ActivityIndicator color={colors.primary} />
            <Text style={{marginLeft: 10, fontFamily: 'Inter-Medium'}}>
              {locale.LoggingYouOut}
            </Text>
          </View>
        </View>
      </Modal>
    </BaseLayout>
  );
};

export default ForceLogout;
