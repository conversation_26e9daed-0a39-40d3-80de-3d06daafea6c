import React from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {authenticationTypeScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {screens, persistent} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Lottie from '../../components/Lottie';
import {
  useBlockBackNavigationForAndroid,
  usePostShareURLConstructor,
} from '../../tools/hooks';
import locale from '../../assets/locale.json';
import Layout from '../../layout';
import QRCode from '../../components/QRCode';

const AuthSuccessful = ({route, navigation}) => {
  const confirmation_code = route?.params?.params;
  const authenticationType = route?.params?.authenticationType ?? 2;
  const isResubmission = route?.params?.isResubmission ?? false;

  useBlockBackNavigationForAndroid();

  const {url} = usePostShareURLConstructor({
    authenticationKey: confirmation_code ?? '',
  });

  const isExpertAuthentication = [2, 3, 5].includes(+authenticationType);
  const isTagAuthentication = authenticationType == 4;

  React.useLayoutEffect(() => {
    navigation?.setOptions?.({
      title: isExpertAuthentication
        ? 'Authenticate (4/4)'
        : 'Authenticate (3/3)',
    });
  }, [isExpertAuthentication, navigation]);

  useFocusEffect(
    React.useCallback(function () {
      (async () => {
        try {
          AsyncStorage.removeItem(persistent.COUPON_CODE)
            .then(() => null)
            .catch(() => null);
        } catch (_) {}
      })();
    }, []),
  );

  return (    
    <Layout contentContainerStyle={styles.container}>
      <View style={styles.labelWrapper}>      
        <Lottie
          loop={false}
          name="success"
          style={{width: 50, height: 50}}
          colorFilterPresetName="green"
        />
        <Text style={styles.label}>
          {authenticationTypeScreen.TRANSACTION_SUCCESSFUL}
        </Text>
        <Text style={styles.confirmation}>
          {authenticationTypeScreen.CONFIRMATION}
          <Text style={{fontFamily: 'Inter-Bold', textTransform: 'uppercase'}}>
            {`#${confirmation_code ?? ''}`}
          </Text>
        </Text>
      </View>
      <View style={styles.textWrapper}>
        {authenticationTypeScreen[
          isResubmission
            ? isExpertAuthentication
              ? 'AUTHENTICATION_RESUBMISSION_SUCCESSFUL_MESSAGE_EXPERT'
              : isTagAuthentication
              ? 'AUTHENTICATION_RESUBMISSION_SUCCESSFUL_MESSAGE_TAG'
              : 'AUTHENTICATION_RESUBMISSION_SUCCESSFUL_MESSAGE'
            : isExpertAuthentication
            ? 'AUTHENTICATION_SUCCESSFUL_MESSAGE_EXPERT'
            : isTagAuthentication
            ? 'AUTHENTICATION_SUCCESSFUL_MESSAGE_TAG'
            : 'AUTHENTICATION_SUCCESSFUL_MESSAGE'
        ]?.map((item, index) => (
          <Text key={index.toString()} style={styles.text}>
            {item}
          </Text>
        ))}
        <View
          style={{
            justifyContent: 'center',
            flexDirection: 'row',
            marginVertical: 30,
          }}>
          <QRCode url={url} />
        </View>
      </View>
      <Layout.Footer style={{backgroundColor: colors.white}}>
        <View style={{margin: 20}}>
          <TouchableOpacity
            style={styles.homeButton}
            onPress={() => {
              navigation.reset({
                routes: [
                  {
                    name: screens.DRAW_NAVIGATOR,
                    state: {
                      routes: [
                        {
                          name: screens.TAB_NAVIGATOR,
                          state: {
                            routes: [{name: screens.MY_STUFF}],
                          },
                        },
                      ],
                    },
                  },
                ],
              });
            }}>
            <Text style={styles.homeButtonText}>
              {locale.MyLegiteem8Wallet}
            </Text>
          </TouchableOpacity>
        </View>
      </Layout.Footer>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: spacings.lg,
    alignItems: 'center',
    paddingBottom: 100,
  },
  label: {
    fontSize: fontSize.xxxl,
    color: colors.black,
    marginVertical: spacings.md,
    fontFamily: 'Gugi-Regular',
  },
  labelWrapper: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 3,
    borderBottomColor: colors.lightGrey,
    paddingVertical: spacings.lg,
    marginBottom: spacings.lg,
  },
  textWrapper: {
    width: '100%',
  },
  text: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
    marginBottom: spacings.lg,
    fontFamily: 'Inter-Medium',
  },
  homeButton: {
    height: 50,
    width: '100%',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
  },
  homeButtonText: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
  confirmation: {
    fontSize: fontSize.md,
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
});

export default AuthSuccessful;
