import React from 'react';

export default (
  inputArray = [],
  invalidity = [],
  initialValueKeyValuePair = {},
) => {
  const reduceItems = (array = [], initialValue = '') => {
    return array.reduce((accumulator, currVal) => {
      accumulator[currVal] = initialValue;
      return accumulator;
    }, {});
  };

  // State for storing input values
  const [values, setValues] = React.useState(() => {
    return inputArray.reduce((accumulator, currVal) => {
      accumulator[currVal] =
        initialValueKeyValuePair != {}
          ? !!initialValueKeyValuePair[currVal]
            ? initialValueKeyValuePair[currVal]
            : ``
          : ``;
      return accumulator;
    }, {});
  });

  // Create state for checking invalidity in forms
  let invalidItems;
  if (!invalidity) {
    invalidItems = reduceItems(inputArray, false);
  } else {
    if (!Array.isArray(invalidity)) {
      throw new Error('Pass an array of items for invalidity check');
    } else if (!invalidity.every(item => typeof item === 'string')) {
      throw new Error('Array items must be string');
    }
    invalidItems = reduceItems(invalidity, false);
  }

  const [invalid, setInvalid] = React.useState(() => invalidItems);

  const handleChange = React.useCallback(
    (name, value) => {
      setValues(previousValue => ({...previousValue, [name]: value}));
    },
    [setValues],
  );

  // Check if which input are empty
  const checkIsEmpty = React.useMemo(() => {
    let empty = {};
    for (let [key, value] of Object.entries(values)) {
      if (invalidity.includes(key)) {
        empty[key] = value === '';
      }
    }
    let isEmpty = Object.values(empty).some(item => item);
    return [empty, isEmpty];
  }, [values, invalidity]);

  const resetValues = React.useCallback(() => {
    setValues(reduceItems(inputArray, ''));
  }, [reduceItems, inputArray]);

  return {
    values,
    setValues,
    handleChange,
    invalid,
    setInvalid,
    checkIsEmpty,
    resetValues,
  };
};
