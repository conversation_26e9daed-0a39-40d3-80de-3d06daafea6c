/* eslint-disable prettier/prettier */
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import { TransitionPresets } from '@react-navigation/stack';
import { Platform } from 'react-native';

import StartWithEmail from '../screens/Register/StartWithEmail';
import InitialScreen from '../screens/InitialScreen';
import Login from '../screens/Login';
import ForgotPassword from '../screens/ForgotPassword';
import ResetPassword from '../screens/ResetPassword';
import ValidateCode from '../screens/ValidateCode';
import {
  forgotPasswordScreen,
  validateCodeScreen,
  loginScreen,
  registerScreen,
  screens,
  resetPasswordScreen,
  fa2ValidateCodeScreen,
} from '../assets/strings';
import colors from '../assets/colors';
import EmailVerification from '../screens/Register/EmailVerification';
import CreatePassword from '../screens/Register/CreatePassword';
import OnBoarding from '../screens/OnBoarding';
import FA2ValidateCode from '../screens/FA2ValidateCode';

const Stack = createStackNavigator();

const isIOS = Platform.OS == 'ios';

const AuthNavigator = () => {
  const { isFirstTimeUsingApp } = useSelector(state => state.auth);

  if (isFirstTimeUsingApp) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          headerTitleAlign: 'center',
          ...(isIOS ? { headerBackTitle: 'Back' } : ''),
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
        initialRouteName={screens.ONBOARDING_SCREEN}>
        <Stack.Screen
          name={screens.ONBOARDING_SCREEN}
          component={OnBoarding}
          options={{ headerShown: false }}
        />
      </Stack.Navigator>
    );
  }
  return (
    <Stack.Navigator
      initialRouteName={screens.INITIAL_SCREEN}
      screenOptions={{
        ...(isIOS ? { title: '' } : ''),
        ...(isIOS ? { headerBackTitle: 'Back' } : ''),

        headerStyle: {
          backgroundColor: colors.black,
        },
        headerTitleAlign: 'center',
        headerTintColor: colors.white,
        gestureEnabled: true,
        ...TransitionPresets.SlideFromRightIOS,
      }}>
      <Stack.Screen
        name={screens.INITIAL_SCREEN}
        component={InitialScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name={screens.LOGIN}
        component={Login}
        options={{
          headerTitle: loginScreen.HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.REGISTER}
        component={StartWithEmail}
        options={{
          headerTitle: registerScreen.SCREEN1_HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.EMAIL_VERIFICATION}
        component={EmailVerification}
        options={{
          headerTitle: registerScreen.SCREEN2_HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.CREATE_PASSWORD}
        component={CreatePassword}
        options={{
          headerTitle: registerScreen.SCREEN3_HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.FORGOT_PASSWORD}
        component={ForgotPassword}
        options={{
          headerTitle: forgotPasswordScreen.HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.RESET_PASSWORD}
        component={ResetPassword}
        options={{
          headerTitle: resetPasswordScreen.HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
      <Stack.Screen
        name={screens.VALIDATE_CODE}
        component={ValidateCode}
        options={{
          headerTitle: validateCodeScreen.HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />

      <Stack.Screen
        name={screens.FA2_VALIDATE_CODE}
        component={FA2ValidateCode}
        options={{
          headerTitle: fa2ValidateCodeScreen.HEADER,
          headerTitleStyle: { fontFamily: 'Gugi-Regular' },
        }}
      />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
