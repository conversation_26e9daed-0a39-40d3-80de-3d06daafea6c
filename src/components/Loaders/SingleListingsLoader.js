/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH - spacings.xl;

const SingleListingsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width={WIDTH}
          marginBottom={20}
          height={CARD_WIDTH}
        />
        <View styles={styles.bottomSection}>
          <SkeletonPlaceholder.Item
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            marginHorizontal={20}
            marginBottom={20}>
            <SkeletonPlaceholder.Item
              alignItems="center"
              justifyContent="center"
              flexDirection="row">
              <SkeletonPlaceholder.Item
                marginRight={10}
                width={20}
                height={20}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginRight={10}
                width={20}
                height={20}
                borderRadius={50}
              />
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH / 4}
                height={20}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH / 3}
              height={20}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            flexDirection="row"
            justifyContent="space-between"
            marginHorizontal={20}>
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 2}
              height={20}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 4}
              height={20}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            width={WIDTH}
            height={3}
            marginBottom={20}
          />
          <SkeletonPlaceholder.Item
            flexDirection="row"
            justifyContent="space-between"
            marginHorizontal={20}>
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 2}
              height={20}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 4}
              height={20}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            flexDirection="row"
            justifyContent="space-between"
            marginHorizontal={20}>
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 2}
              height={20}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 4}
              height={20}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            flexDirection="row"
            justifyContent="space-between"
            marginHorizontal={20}>
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 2}
              height={20}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 4}
              height={20}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            width={WIDTH}
            height={3}
            marginBottom={20}
          />
          <SkeletonPlaceholder.Item
            marginHorizontal={20}
            marginBottom={20}
            width={CARD_WIDTH / 4}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={20}
            marginHorizontal={20}
            width={CARD_WIDTH}
            height={80}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH}
            height={3}
            marginBottom={20}
          />
          <SkeletonPlaceholder.Item
            marginBottom={20}
            marginHorizontal={20}
            width={CARD_WIDTH / 4}
            height={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            marginBottom={20}
            width={CARD_WIDTH}
            marginHorizontal={20}
            height={80}
            borderRadius={4}
          />
        </View>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomSection: {
    width: WIDTH,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default SingleListingsLoader;
