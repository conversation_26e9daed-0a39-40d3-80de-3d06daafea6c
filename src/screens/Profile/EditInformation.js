import React, {useState} from 'react';
import {StyleSheet, Text, TextInput, View} from 'react-native';
import {useMutation} from '@apollo/client';

import {UPDATE_PROFILE} from '../../apollo/mutations';
import {profileSettingsScreen} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import Button from '../../components/Button';
import locale from '../../assets/locale.json';
import {useUser} from '../../tools/hooks';
import InfoModal from '../../components/Modal/InfoModal';
import {validateEmail, validateUsername} from '../../tools/utils';
import {InvalidTextComponent} from '../../components/TextInput';
import Layout from '../../layout';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: locale.Error,
    description: '',
  },
};

const EditInformation = ({navigation}) => {
  const {user, refresh} = useUser({focused: true});

  const [firstName, setFirstName] = useState(user?.first_name ?? '');
  const [email, setEmail] = useState(user?.email ?? '');
  const [bio, setBio] = useState(user?.bio ?? '');
  const [username, setUsername] = useState(user?.username ?? '');
  const [socialMediaUrl, setSocialMediaUrl] = useState(
    user?.social_media?.[0]?.url ?? '',
  );
  const [isLoading, setIsLoading] = useState(false);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
  const [invalid, setInvalid] = React.useState({
    email: '',
    username: '',
  });

  const [updateProfile] = useMutation(UPDATE_PROFILE);

  const checkValidity = React.useCallback(
    ({email: _email, username: _username}) => {
      const _invalid = {
        email: '',
        username: '',
      };
      if (!_email?.length) {
        _invalid.email = locale.EmailIsRequired;
      } else {
        if (!validateEmail(_email)) {
          _invalid.email = locale.InvalidEmailFormat;
        }
      }
      if (!_username?.length) {
        _invalid.username = locale.UsernameIsRequired;
      } else {
        const {isValid, message} = validateUsername(_username);
        if (!isValid) {
          _invalid.username = message;
        }
      }
      setInvalid(previousState => ({...previousState, ..._invalid}));
      return Object.values(_invalid).every(item => item?.length == 0);
    },
    [],
  );

  const changeDetails = React.useCallback(async () => {
    if (!checkValidity({email, username})) {
      return;
    }
    setIsLoading(true);
    try {
      const {data} = await updateProfile({
        variables: {
          email: email,
          first_name: firstName?.trim?.(),
          bio: bio?.trim?.(),
          username: username,
          social_media_link: socialMediaUrl,
        },
      });
      const isSuccess = data?.updateProfile?.success;
      const code = data?.updateProfile?.code;
      const message = data?.updateProfile?.message;

      if (isSuccess) {
        await refresh();
        navigation.goBack();
      } else {
        if (code?.length) {
          setPopup(previousState => ({
            ...previousState,
            state: true,
            data: {
              ...previousState.data,
              description: message,
            },
          }));
        } else {
          throw new Error();
        }
      }
    } catch (e) {
      setPopup(previousState => ({
        ...previousState,
        state: true,
        data: {
          ...previousState.data,
          description:
            e?.response?.data?.message ??
            locale.SomethingWentWrongPleaseTryAgain,
        },
      }));
    } finally {
      setIsLoading(false);
    }
  }, [
    updateProfile,
    email,
    firstName,
    bio,
    username,
    socialMediaUrl,
    navigation,
    refresh,
    checkValidity,
  ]);

  return (
    <Layout style={styles.container}>
      <Text style={styles.title}>{locale.AddYourPersonalInformation}</Text>
      <View style={styles.inputCard}>
        <Text style={styles.label}>{profileSettingsScreen.FIRST_NAME}</Text>
        <TextInput
          value={firstName}
          onChangeText={setFirstName}
          style={styles.inputField}
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
      </View>
      <View style={styles.inputCard}>
        <Text style={styles.label}>{profileSettingsScreen.USERNAME}</Text>
        <TextInput
          value={username}
          onChangeText={val => {
            setInvalid(previousState => ({...previousState, username: ''}));
            setUsername(val);
          }}
          style={styles.inputField}
          keyboardType="email-address"
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
        {invalid.username?.length ? (
          <InvalidTextComponent text={invalid.username} />
        ) : null}
      </View>

      <View style={styles.inputCard}>
        <Text style={styles.label}>{profileSettingsScreen.EMAIL}</Text>
        <TextInput
          value={email}
          onChangeText={val => {
            setInvalid(previousState => ({...previousState, email: ''}));
            setEmail(val);
          }}
          style={styles.inputField}
          keyboardType="email-address"
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
        {invalid.email?.length ? (
          <InvalidTextComponent text={invalid.email} />
        ) : null}
      </View>

      <View style={styles.inputCard}>
        <Text style={styles.label}>{locale.Website}</Text>
        <TextInput
          value={socialMediaUrl}
          onChangeText={setSocialMediaUrl}
          style={styles.inputField}
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
          keyboardType="url"
          autoCapitalize="none"
        />
      </View>
      <View style={styles.inputCard}>
        <Text style={styles.label}>{profileSettingsScreen.BIO}</Text>
        <TextInput
          multiline={true}
          numberOfLines={4}
          value={bio}
          onChangeText={setBio}
          maxLength={255}
          style={[
            styles.inputField,
            {textAlignVertical: 'top', minHeight: 80, maxHeight: 80},
          ]}
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
      </View>

      <Button
        text={profileSettingsScreen.SAVE_CHANGES}
        isLoading={isLoading}
        disabled={isLoading}
        onPress={changeDetails}
        style={{borderRadius: 5, marginVertical: 20, height: 50}}
      />
      <InfoModal
        setVisible={() =>
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }))
        }
        popUp={popup}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  inputField: {
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 4,
    marginTop: spacings.sm,
    color: colors.black,
    paddingHorizontal: spacings.md,
    minHeight: 45,
    fontSize: fontSize.md,
    fontFamily: 'Inter-Regular',
  },
  inputCard: {
    marginTop: spacings.lg,
  },
  submitButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    backgroundColor: colors.primary,
    height: 50,
    borderRadius: 4,
    marginVertical: spacings.xxl,
  },
  submitButtonText: {
    fontSize: fontSize.lg,
    color: colors.white,
    fontWeight: 'bold',
  },
  label: {
    fontSize: fontSize.md,
    color: colors.black,
    fontFamily: 'Inter-Regular',
  },
  title: {
    fontFamily: 'Gugi-Regular',
    fontSize: fontSize.lg,
    color: colors.black,
    marginTop: spacings.lg,
  },
});

export default EditInformation;
