import {screens, persistent} from '../../assets/strings';
import AsyncStorage from '@react-native-async-storage/async-storage';

const notificationType = {
  MASS_NOTIFICATION: 'MASS_NOTIFICATION',
  'SUBSCRIPTION@COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED':
    'SUBS<PERSON>IPTION@COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED',
  'SUBSCRIPTION@NEW_COMMENT_TO_POST': 'SUBSCRIPTION@NEW_COMMENT_TO_POST',
  'SUBSCRIPTION@NEW_COMMENT_TO_POST_IMAGE':
    'SUBSCRIPTION@NEW_COMMENT_TO_POST_IMAGE',
  'SUBSCRIPTION@NEW_VOTE_TO_POST': 'SUBSCRIPTION@NEW_VOTE_TO_POST',
  'SUBSCRIPTION@AUTHENTICATION_REVIEWED_BY_EXPERT':
    'SUBSCRIPTION@AUTHENTICATION_REVIEWED_BY_EXPERT',
  NEW_CHAT_MESSAGE: 'NEW_CHAT_MESSAGE',
  AUTHENTICATION_ACCEPTED: 'AUTHENTICATION_ACCEPTED',
  NEW_VOTE_ON_POST: 'NEW_VOTE_ON_POST',
  NEW_COMMENT_ON_POST: 'NEW_COMMENT_ON_POST',
  ACCOUNT_FOLLOWED: 'ACCOUNT_FOLLOWED',
  AUTHENTICATION_REJECTED: 'AUTHENTICATION_REJECTED',
  NEW_COMMENT_ON_POST_IMAGE: 'NEW_COMMENT_ON_POST_IMAGE',
  NFT_TRANSFERRED: 'NFT_TRANSFERRED',
  AUTHENTICATION_REVIEWED_BY_EXPERT: 'AUTHENTICATION_REVIEWED_BY_EXPERT',
  NEW_LIKE_ON_POST: 'NEW_LIKE_ON_POST',
  NEW_MENTION_ON_POST_COMMENT: 'NEW_MENTION_ON_POST_COMMENT',
  NEW_MENTION_ON_POST_IMAGE_COMMENT: 'NEW_MENTION_ON_POST_IMAGE_COMMENT',
  COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED:
    'COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED',
};

const defaultOptions = {
  shouldDisableForegroundNotification: {
    userId: null,
  },
};

// Map the screens
function mapNotificationScreens(notificationData) {
  switch (notificationData?.type) {
    case notificationType.NEW_CHAT_MESSAGE:
      return {
        name: screens.CHAT_SCREEN,
        params: {
          params: notificationData?.fromUserId,
        },
      };
    case notificationType.AUTHENTICATION_ACCEPTED:
      return {
        name: screens.SINGLE_LISTING,
        params: {
          params: notificationData?.postId,
        },
      };
    case notificationType.AUTHENTICATION_REJECTED:
      return {
        name: screens.ADD_PHOTOS,
        params: {
          authenticationId: notificationData?.queueId,
          isAuthenticationResubmission: true,
        },
      };
    case notificationType.NEW_VOTE_ON_POST:
    case notificationType['SUBSCRIPTION@NEW_VOTE_TO_POST']:
      return {
        name: screens.VOTES,
        params: {
          postId: notificationData?.postId,
        },
      };
    case notificationType.NEW_COMMENT_ON_POST:
    case notificationType.NEW_MENTION_ON_POST_COMMENT:
    case notificationType['SUBSCRIPTION@NEW_COMMENT_TO_POST']:
      return {
        name: screens.COMMENTS,
        params: {
          postId: notificationData?.postId,
          showPostNavigationLink: true,
        },
      };
    case notificationType.NEW_COMMENT_ON_POST_IMAGE:
    case notificationType.NEW_MENTION_ON_POST_IMAGE_COMMENT:
    case notificationType['SUBSCRIPTION@NEW_COMMENT_TO_POST_IMAGE']:
      return {
        name: screens.POST_IMAGE_COMMENT,
        params: {
          postId: notificationData?.postId,
          image_name: notificationData?.imageName,
          showPostNavigationLink: true,
        },
      };
    case notificationType.NFT_TRANSFERRED:
      return {
        name: screens.SINGLE_LISTING,
        params: {
          params: notificationData?.postId,
        },
      };
    case notificationType.AUTHENTICATION_REVIEWED_BY_EXPERT:
    case notificationType.COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED:
    case notificationType[
      'SUBSCRIPTION@COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED'
    ]:
    case notificationType['SUBSCRIPTION@AUTHENTICATION_REVIEWED_BY_EXPERT']:
      return {
        name: screens.SINGLE_LISTING,
        params: {
          params: notificationData?.postId,
        },
      };

    case notificationType.NEW_LIKE_ON_POST:
      return {
        name: screens.SINGLE_LISTING,
        params: {
          params: notificationData?.postId,
        },
      };

    default:
      return {
        name: screens.NOTIFICATION_MESSAGES,
      };
  }
}

async function shouldDisableForegroundNotification(
  notification,
  options = defaultOptions.shouldDisableForegroundNotification,
) {
  options = {...defaultOptions.shouldDisableForegroundNotification, ...options};

  const notificationData = notification?.data;
  if (!notificationData) {
    return false;
  }

  try {
    switch (notificationData?.type) {
      // Direct Message
      case notificationType.NEW_CHAT_MESSAGE:
        const directMessageNotificationAllowed = await AsyncStorage.getItem(
          persistent.ALLOW_IN_APP_DIRECT_MESSAGE_NOTIFICATION,
        );
        if (directMessageNotificationAllowed == '0') {
          return true;
        }
        const isConversationListFocused = await AsyncStorage.getItem(
          persistent.IS_CONVERSATION_LIST_FOCUSED,
        );
        if (isConversationListFocused == '1') {
          return true;
        }
        const currentConversation = await AsyncStorage.getItem(
          persistent.CURRENT_CONVERSATION_USER_ID,
        );
        if (!(currentConversation == null)) {
          const conversationWithUserId = notificationData?.fromUserId;
          if (conversationWithUserId == options.userId) {
            return true;
          }
        }
        return false;

      // Account Follow
      case notificationType.ACCOUNT_FOLLOWED:
        const accountFollowedNotificationAllowed = await AsyncStorage.getItem(
          persistent.ALLOW_IN_APP_FOLLOW_STATUS_NOTIFICATION,
        );
        if (accountFollowedNotificationAllowed == '0') {
          return true;
        }
        return false;

      // Post Reaction
      case notificationType.NEW_COMMENT_ON_POST:
      case notificationType.NEW_VOTE_ON_POST:
      case notificationType.NEW_COMMENT_ON_POST_IMAGE:
      case notificationType.NEW_MENTION_ON_POST_COMMENT:
      case notificationType.NEW_MENTION_ON_POST_IMAGE_COMMENT:
        const postReactionNotificationAllowed = await AsyncStorage.getItem(
          persistent.ALLOW_IN_APP_POST_REACTION_NOTIFICATION,
        );
        if (postReactionNotificationAllowed == '0') {
          return true;
        }
        return false;

      // Authentication submission
      case notificationType.AUTHENTICATION_ACCEPTED:
      case notificationType.AUTHENTICATION_REJECTED:
      case notificationType.AUTHENTICATION_REVIEWED_BY_EXPERT:
      case notificationType.COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED:
        const authenticationSubmissionNotificationAllowed = await AsyncStorage.getItem(
          persistent.ALLOW_IN_APP_AUTHENTICATION_SUBMISSION_STATUS_NOTIFICATION,
        );
        if (authenticationSubmissionNotificationAllowed == '0') {
          return true;
        }
        return false;
      default:
        return false;
    }
  } catch (error) {
    return false;
  }
}

function getNotificationDynamicScreensKeys() {
  return [
    persistent.CURRENT_CONVERSATION_USER_ID,
    persistent.IS_CONVERSATION_LIST_FOCUSED,
  ];
}

export {
  mapNotificationScreens,
  shouldDisableForegroundNotification,
  getNotificationDynamicScreensKeys,
};
