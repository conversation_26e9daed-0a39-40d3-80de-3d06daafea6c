import React from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import dayjs from 'dayjs';
import {useNavigation} from '@react-navigation/native';
import {useMutation} from '@apollo/client';

import {
  ACCEPT_REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION,
  REJECT_REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION,
} from '../../../apollo/mutations';
import colors from '../../../assets/colors';
import {screens} from '../../../assets/strings';
import TouchAwareButton from '../../../components/Button/TouchAwareButton';
import ProfileImage from '../../../components/ProfileImage';
import locale from '../../../assets/locale.json';
import {styles} from '../NotificationRow';
import {showToast} from '../../../components/Toast';

export default function NotificationOwnershipHistoryTagged({item}) {
  const navigation = useNavigation();

  const [loading, setLoading] = React.useState(false);
  const [isAccepted, setAccepted] = React.useState();

  const [acceptRequestToBeTaggedAsPreviousOwnerOfAuthentication] = useMutation(
    ACCEPT_REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION,
  );

  const [rejectRequestToBeTaggedAsPreviousOwnerOfAuthentication] = useMutation(
    REJECT_REQUEST_TO_BE_TAGGED_AS_PREVIOUS_OWNER_OF_AUTHENTICATION,
  );

  const handleDecision = React.useCallback(
    async (isAccepted = true) => {
      setLoading(true);
      try {
        const {data} = await (isAccepted
          ? acceptRequestToBeTaggedAsPreviousOwnerOfAuthentication
          : rejectRequestToBeTaggedAsPreviousOwnerOfAuthentication)({
          variables: {
            postOwnershipId: item?.post_ownership_id,
          },
        });
        const target =
          data?.[
            isAccepted
              ? 'acceptRequestToBeTaggedAsPreviousOwnerOfAuthentication'
              : 'rejectRequestToBeTaggedAsPreviousOwnerOfAuthentication'
          ];
        const success = target?.success;
        const message = target?.message;
        setLoading(false);
        if (success) {
          setAccepted(isAccepted);
        } else {
          showToast({message: message ?? locale['SomethingWentWrong.']});
        }
      } catch (error) {
        setLoading(false);
        showToast({message: locale['SomethingWentWrong.']});
      }
    },
    [
      item,
      acceptRequestToBeTaggedAsPreviousOwnerOfAuthentication,
      rejectRequestToBeTaggedAsPreviousOwnerOfAuthentication,
    ],
  );

  return (
    <TouchableOpacity
      activeOpacity={1}
      style={styles.notificationsCard}
      onPress={() =>
        navigation.navigate(screens.SINGLE_LISTING, {
          params: item?.post_id,
        })
      }>
      <View style={styles.descWrapper}>
        <TouchAwareButton
          onPress={() =>
            navigation.navigate(
              item?.user?.role == 'EXPERT'
                ? screens.EXPERTS_PROFILE
                : screens.PUBLIC_PROFILES,
              {
                ...(item?.user?.role == 'EXPERT'
                  ? {expertUserId: item?.user?.id}
                  : {params: item?.user?.id}),
              },
            )
          }>
          <ProfileImage
            url={item?.user?.image}
            size={45}
            style={{marginRight: 10}}
          />
        </TouchAwareButton>

        <View style={{flex: 1, flexDirection: 'column'}}>
          <Text style={[styles.text, {flex: 1}]}>
            {item?.user?.username}{' '}
            {locale.hasRequestedToBeTaggedAsAPreviousOwnerOf}
            {' - '}
            <Text style={styles.authenticationTitle}>{item?.post?.title}.</Text>
          </Text>
          {isAccepted == null ? (
            <View
              style={{
                flexDirection: 'row',
                alignSelf: 'flex-start',
              }}>
              <TouchableOpacity
                style={{
                  paddingVertical: 10,
                  paddingRight: 15,
                }}
                onPress={handleDecision}
                disabled={loading}>
                <Text
                  style={{
                    fontFamily: 'Inter-Bold',
                    color: colors.green2,
                    opacity: loading ? 0.3 : 1,
                  }}>
                  Accept
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  padding: 10,
                  paddingLeft: 15,
                }}
                onPress={() => handleDecision(false)}>
                <Text
                  style={{
                    fontFamily: 'Inter-Bold',
                    color: colors.red,
                    opacity: loading ? 0.3 : 1,
                  }}>
                  Reject
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <Text
              style={{
                fontFamily: 'Inter-Bold',
                color: isAccepted ? colors.green2 : colors.red,
                fontStyle: 'italic',
              }}>
              {isAccepted ? 'Accepted' : 'Rejected'}
            </Text>
          )}
          <Text style={[styles.date]}>{dayjs(item?.created_at).fromNow()}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}
