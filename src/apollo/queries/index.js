import {gql} from '@apollo/client';

export const GET_USER_DATA = gql`
  query {
    me {
      success
      data {
        id
        username
        email
        first_name
        last_name
        image
        bio
        accuracy
        followersCount
        followingsCount
        badge {
          id
          name
          image_url
          level
        }
        social_media {
          id
          name
          url
        }
        role
        authentication_count
        doIFollow
        followersCount
        followingsCount
        isBlockedByMe
        number_of_votes
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_EXPERT_FEED_ACTIVITY = gql`
  query getExpertFeedActivity(
    $userId: ID!
    $limit: Int
    $after: String
    $filters: ExpertFeedActivityFilters
  ) {
    getExpertFeedActivity(
      userId: $userId
      limit: $limit
      after: $after
      filters: $filters
    ) {
      success
      data {
        edges {
          ... on FEED_ACTIVITY_NEW_EXPERT_VOTE {
            id
            vote {
              decision
              appraisal_value
              created_at
            }
            post {
              id
              title
              final_decision
              created_at
              appraisal_value
              authentication_result {
                decision
                score
                expert_result {
                  id
                  decision
                  appraisal_value
                }
              }
              images {
                id
                featured
                front
                closeup_front
              }
            }
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_USER_FEED_ACTIVITY = gql`
  query getUserFeedActivity(
    $userId: ID!
    $limit: Int
    $after: String
    $filters: FeedActivityFilters
  ) {
    getUserFeedActivity(
      userId: $userId
      limit: $limit
      after: $after
      filters: $filters
    ) {
      success
      data {
        edges {
          ... on FEED_ACTIVITY_NEW_POST {
            id
            post {
              id
              title
              final_decision
              created_at
              approved_at
              appraisal_value
              authentication_result {
                decision
                score
                community_result {
                  id
                  decision
                  totalVotes
                  score
                }
                expert_result {
                  id
                  decision
                  appraisal_value
                }
              }
              images {
                id
                featured
                front
                closeup_front
              }
              user {
                id
                username
              }
              my_vote {
                id
                decision
                created_at
                appraisal_value
                remarks
              }
            }
          }
          ... on FEED_ACTIVITY_NEW_VOTE {
            id
            vote {
              decision
              appraisal_value
              remarks
              created_at
              user {
                id
                username
              }
            }
            post {
              id
              title
              final_decision
              created_at
              appraisal_value
              authentication_result {
                decision
                score
                community_result {
                  id
                  decision
                  totalVotes
                  score
                }
                expert_result {
                  id
                  decision
                  appraisal_value
                }
              }
              images {
                id
                featured
                front
                closeup_front
              }
              user {
                id
                username
              }
              my_vote {
                id
                decision
                created_at
                appraisal_value
                remarks
              }
            }
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_FOLLOWING_USER_ACTIVITY = gql`
  query getFollowingUserFeedActivity(
    $limit: Int
    $after: String
    $filters: FeedActivityFilters
  ) {
    getFollowingUserFeedActivity(
      limit: $limit
      after: $after
      filters: $filters
    ) {
      success
      data {
        edges {
          ... on FEED_ACTIVITY_NEW_POST {
            id
            post {
              id
              title
              authentication_type {
                id
                name
              }
              authentication_result {
                id
                decision
                score
                decision
                expert_result {
                  id
                  decision
                  appraisal_value
                }
                community_result {
                  id
                  decision
                  totalVotes
                  score
                }
              }
              appraisal_value
              created_at
              approved_at
              images {
                id
                front
                closeup_front
                featured
              }
              my_vote {
                id
                decision
                appraisal_value
              }
              user {
                id
                username
                image
                role
              }
            }
          }
          ... on FEED_ACTIVITY_NEW_VOTE {
            id
            vote {
              id
              decision
              appraisal_value
              remarks
              created_at
              user {
                id
                username
                image
                role
              }
            }
            post {
              id
              title
              authentication_type {
                id
                name
              }
              authentication_result {
                id
                decision
                score
                decision
                expert_result {
                  id
                  decision
                  appraisal_value
                }
                community_result {
                  id
                  decision
                  totalVotes
                  score
                }
              }
              appraisal_value
              images {
                id
                front
                closeup_front
                featured
              }
              my_vote {
                id
                decision
                appraisal_value
              }
            }
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_POST_VOTES = gql`
  query getPostVotes($postId: ID!, $limit: Int, $after: String) {
    getPostVotes(postId: $postId, limit: $limit, after: $after) {
      success
      data {
        edges {
          id
          decision
          created_at
          user {
            id
            username
            image
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_ALL_BRANDS = gql`
  query getBrands($limit: Int, $after: String, $term: String) {
    getBrands(limit: $limit, after: $after, term: $term) {
      id @client
      success
      data {
        edges {
          id
          name
          image_url
          is_nft_enabled
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_BRAND = gql`
  query getBrand($id: Int!) {
    getBrand(id: $id) {
      success
      message
      data {
        id
        name
        image_url
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_AUTHENTICATION_INFO = gql`
  query {
    getAuthenticationInfo {
      success
      data {
        type {
          id
          name
          pricing
          iap_key_ios
          iap_price_ios
        }
        sizes {
          id
          name
        }
        materials {
          id
          name
        }
        stitches {
          id
          name
        }
        conditions {
          id
          name
        }
        from_decades {
          id
          name
        }
        upgradeToExpertCertifiedNFTFromExpertCertifiedPricing
        upgradeToExpertCertifiedNFTFromExpertCertified_IAP_Pricing
        upgradeToExpertCertifiedNFTFromExpertCertified_IAP_Key
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_AUTHENTICATION_INFO_FOR_SEARCH = gql`
  query {
    getAuthenticationInfo {
      success
      data {
        type {
          id
          name
          pricing
        }
        materials {
          id
          name
        }
        stitches {
          id
          name
        }
        from_decades {
          id
          name
        }
        sizes {
          id
          name
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_SETTINGS = gql`
  query getSettings($key: SettingsKey) {
    getSettings(key: $key) {
      success
      data
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_STRIPE_PUBLISHABLE_KEY = gql`
  query {
    getStripePublishableKey {
      success
      data
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_ALL_CARDS = gql`
  query {
    getAllCards {
      success
      data {
        id
        name
        number
        brand
        expiry_month
        expiry_year
        is_default
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_CARD = gql`
  query getCard($id: ID!) {
    getCard(id: $id) {
      success
      data {
        id
        name
        number
        brand
        expiry_month
        expiry_year
        is_default
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_NOTIFICATIONS = gql`
  query($limit: Int, $after: String) {
    getNotifications(limit: $limit, after: $after) {
      data {
        pageInfo {
          hasMore
          endCursor
        }
        edges {
          ... on NOTIFICATION_MASS_NOTIFICATION {
            id
            title
            message
            is_read
            type
            created_at
          }
          ... on NOTIFICATION_AUTHENTICATION_ASSIGNED_EXPERT {
            id
            title
            message
            is_read            
            category
            type
            created_at
            post_id
            post {
              images {
                featured
              }
            }
          }
          ... on NOTIFICATION_PLAIN {
            id
            title
            message
            is_read
            type
            category
            created_at
          }
          ... on NOTIFICATION_NEW_VOTE_ON_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
            vote {
              id
              decision
              appraisal_value
              remarks
              created_at
              user {
                id
                username
                image
                role
              }
            }
          }
          ... on NOTIFICATION_ACCOUNT_FOLLOWED {
            id
            title
            message
            is_read
            type
            category
            created_at
            followed_by {
              id
              username
              image
              role
            }
          }
          ... on NOTIFICATION_AUTHENTICATION_ACCEPTED {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            queue_id
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_AUTHENTICATION_REJECTED {
            id
            title
            message
            is_read
            type
            category
            created_at
            queue_id
            rejection {
              id
              description
            }
            queue {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }

          ... on NOTIFICATION_NEW_COMMENT_ON_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              message_type
              is_reply_allowed
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_NEW_COMMENT_ON_POST_IMAGE {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
              item_image
            }
          }
          ... on NOTIFICATION_NFT_TRANSFERRED {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_AUTHENTICATION_REVIEWED_BY_EXPERT {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_NEW_MENTION_ON_POST_COMMENT {
            id
            title
            message
            is_read
            type
            created_at
            post_id
            comment {
              id
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_NEW_MENTION_ON_POST_IMAGE_COMMENT {
            id
            title
            message
            is_read
            type
            created_at
            post_id
            comment {
              id
              item_image
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_NEW_LIKE_ON_POST {
            id
            title
            message
            type
            created_at
            post_id
            user {
              id
              username
              role
              image
            }
            post {
              id
              title
            }
          }
          ... on NOTIFICATION_OWNERSHIP_HISTORY_TAGGED {
            id
            title
            message
            type
            created_at
            post_id
            post_ownership_id
            user {
              id
              username
              role
              image
            }
            post {
              id
              title
            }
          }
          ... on NOTIFICATION_OWNERSHIP_HISTORY_REQUESTED_TO_BE_TAGGED {
            id
            title
            message
            type
            created_at
            post_id
            post_ownership_id
            user {
              id
              username
              role
              image
            }
            post {
              id
              title
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_NEW_VOTE_TO_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
            vote {
              id
              decision
              appraisal_value
              remarks
              created_at
              user {
                id
                username
                image
                role
              }
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_NEW_COMMENT_TO_POST {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              message_type
              is_reply_allowed
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_NEW_COMMENT_TO_POST_IMAGE {
            id
            title
            message
            is_read
            type
            category
            created_at
            post_id
            comment {
              id
              message
              imageUrl
              created_at
              user {
                id
                username
                image
                role
              }
              post {
                id
                title
              }
              item_image
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_AUTHENTICATION_REVIEWED_BY_EXPERT {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
          ... on NOTIFICATION_SUBSCRIPTION_COMMUNITY_AUTHENTICATION_RESULTS_PUBLISHED {
            id
            type
            category
            post_id
            created_at
            post {
              id
              title
              images {
                id
                front
                closeup_front
                featured
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_PUSH_NOTIFICATION_SETTING = gql`
  query {
    getPushNotificationSetting {
      success
      data {
        id
        allow_direct_message
        allow_post_reaction
        allow_authentication_submission_status
        allow_follow_status
        allow_in_app_direct_message
        allow_in_app_post_reaction
        allow_in_app_authentication_submission_status
        allow_in_app_follow_status
      }
      message
      errors {
        path
        message
      }
    }
  }
`;

export const GET_UNREAD_CHAT_MESSAGES_COUNT = gql`
  query getUnreadChatMessagesCount($userId: ID!) {
    getUnreadChatMessagesCount(userId: $userId) {
      success
      data
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_UNREAD_CHAT_COUNT = gql`
  query {
    getUnreadChatCount {
      success
      data
      errors {
        path
        message
      }
      message
    }
  }
`;

export const GET_UNREAD_NOTIFICATION_COUNT = gql`
  query {
    getUnreadNotificationsCount {
      success
      data
      message
      errors {
        path
        message
      }
    }
  }
`;

export const GET_OTHER_USER_PROFILES = gql`
  query getUserProfile($id: ID!) {
    getUserProfile(id: $id) {
      success
      data {
        id
        username
        first_name
        last_name
        image
        bio
        accuracy
        badge {
          id
          name
          image_url
        }
        social_media {
          id
          name
          url
        }
        role
        authentication_count
        doIFollow
        followersCount
        followingsCount
        isBlockedByMe
        number_of_votes
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_SINGLE_POST = gql`
  query getPost($id: ID, $key: String) {
    getPost(id: $id, key: $key) {
      success
      data {
        id
        queue_id
        title
        show_score
        is_private
        appraisal_value
        my_vote {
          id
          decision
          appraisal_value
          remarks
          created_at
        }
        authentication_result {
          id
          decision
          score
          expert_result {
            id
            decision
            appraisal_value
            comment
            expert {
              id
              expertise {
                id
                name
              }
              user {
                id
                username
                first_name
                last_name
                image
              }
            }
          }
          community_result {
            id
            decision
            totalVotes
            score
          }
        }
        authentication_key
        authentication_status
        authentication_type {
          id
          name
        }
        brand {
          id
          name
          image_url
        }
        is_custom_brand
        pit_to_pit_measurement
        collar_to_bottom_measurement
        size {
          id
          name
        }
        material {
          id
          name
        }
        custom_material
        stitching {
          id
          name
        }
        condition {
          id
          name
        }
        category {
          id
          name
        }
        condition_description
        provenance
        from_decade {
          id
          name
        }
        post_status_type
        images {
          id
          front
          back
          front_tag
          front_tag
          back_tag
          closeup_front
          closeup_back
          copyright
          arm_hem_stitching
          lower_hem_stitching
          extra
        }
        user {
          id
          username
          image
          bio
          accuracy
          badge {
            id
            name
            image_url
          }
        }
        post_updates_subscription {
          id
        }
        created_at
        approved_at
        final_decision
        number_of_post_comments
        number_of_post_image_comments {
          id
          authentication_image
          count
        }
        number_of_likes
        is_voting_closed
        voting_close_date
        rn_number
        has_certificate
        certificate_image_url
        nft_detail {
          id
          etherscan_url
        }
        is_liked_by_me
        number_of_likes
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_POST_IMAGES = gql`
  query getPost($id: ID, $key: String) {
    getPost(id: $id, key: $key) {
      success
      data {
        id
        images {
          id
          front
          back
          front_tag
          front_tag
          back_tag
          closeup_front
          closeup_back
          copyright
          arm_hem_stitching
          lower_hem_stitching
          extra
        }
        authentication_type {
          id
          name
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_POST_AUTHENTICATION_TYPE_ONLY = gql`
  query getPost($id: ID, $key: String) {
    getPost(id: $id, key: $key) {
      success
      data {
        id
        authentication_type {
          id
          name
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_SINGLE_POST_FOR_UPGRADE = gql`
  query getPost($id: ID) {
    getPost(id: $id) {
      success
      data {
        id
        title
        authentication_key
        post_status_type
        authentication_type {
          id
          name
        }
        brand {
          id
          name
        }
        is_custom_brand
        pit_to_pit_measurement
        collar_to_bottom_measurement
        size {
          id
          name
        }
        material {
          id
          name
        }
        stitching {
          id
          name
        }
        condition {
          id
          name
        }
        category {
          id
          name
        }
        condition_description
        provenance
        from_decade {
          id
          name
        }
        images {
          id
          front
          front_upload_method
          back
          back_upload_method
          front_tag
          front_tag
          front_tag_upload_method
          back_tag
          back_tag_upload_method
          closeup_front
          closeup_front_upload_method
          closeup_back
          closeup_back_upload_method
          copyright
          copyright_upload_method
          arm_hem_stitching
          arm_hem_stitching_upload_method
          lower_hem_stitching
          lower_hem_stitching_upload_method
          extra
        }
        images_stripped {
          id
          front
          back
          front_tag
          front_tag
          back_tag
          closeup_front
          closeup_back
          copyright
          arm_hem_stitching
          lower_hem_stitching
          extra
        }
        rn_number
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_POST_IMAGE_COMMENTS = gql`
  query getPostImageComments(
    $postId: ID!
    $authenticationImage: AuthenticationImageType!
    $limit: Int = 15
    $after: String
  ) {
    getPostImageComments(
      postId: $postId
      authenticationImage: $authenticationImage
      limit: $limit
      after: $after
    ) {
      success
      data {
        edges {
          id
          message
          created_at
          user {
            id
            username
            image
            role
          }
          post {
            id
            images {
              front
              back
              front_tag
              back_tag
              closeup_front
              closeup_back
              copyright
              arm_hem_stitching
              lower_hem_stitching
              extra
            }
            number_of_post_image_comments {
              authentication_image
              count
            }
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_RECENT_CATEGORIES = gql`
  query getRecentCategories($limit: Int) {
    getRecentCategories(limit: $limit) {
      success
      data {
        id
        name
        image_url
      }
      message
      code
      errors {
        path
        message
      }
    }
  }
`;

export const GET_CATEGORIES = gql`
  query {
    getCategories {
      success
      data {
        id
        name
        image_url
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_CATEGORY = gql`
  query getCategory($id: ID!) {
    getCategory(id: $id) {
      success
      data {
        id
        name
        image_url
      }
      message
      code
      errors {
        path
        message
      }
    }
  }
`;

export const GET_RECENT_BRANDS = gql`
  query getRecentBrands($limit: Int) {
    getRecentBrands(limit: $limit) {
      success
      data {
        id
        name
        description
        image_url
        created_at
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;
export const GET_ALL_LISTING_HOME_TAB = gql`
  query getPosts($limit: Int, $after: String, $filters: PostFilter) {
    getPosts(limit: $limit, after: $after, filters: $filters) {
      id @client
      success
      data {
        edges {
          id
          post_status_type
          is_private
          title
          show_score
          appraisal_value
          authentication_type {
            id
            name
          }
          authentication_result {
            id
            decision
            score
            expert_result {
              id
              appraisal_value
              decision
            }
            community_result {
              id
              totalVotes
              decision
              score
            }
          }
          images {
            id
            featured
            front
            closeup_front
          }
          my_vote {
            id
            decision
            appraisal_value
            remarks
            created_at
          }
          user {
            id
            username
            image
          }
          is_pinned
          created_at
          approved_at
          can_claim_nft
          is_voting_closed
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_FOLLOWING_COUNT = gql`
  query getFollowing($limit: Int, $after: String, $filters: FollowingFilters) {
    getFollowing(limit: $limit, after: $after, filters: $filters) {
      success
      data {
        edges {
          id
          username
          last_name
          first_name
          doIFollow
          role
          badge {
            id
            name
            image_url
          }
          accuracy
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_FOLLOWERS_COUNT = gql`
  query getFollowers($limit: Int, $after: String, $filters: FollowersFilters) {
    getFollowers(limit: $limit, after: $after, filters: $filters) {
      success
      data {
        edges {
          id
          username
          last_name
          first_name
          doIFollow
          role
          badge {
            id
            name
            image_url
          }
          accuracy
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;
export const GET_ALL_POST_COMMENTS = gql`
  query getPostComments($postId: ID!, $limit: Int) {
    getPostComments(postId: $postId, limit: $limit) {
      success
      data {
        edges {
          id
          message
          created_at
          user {
            id
            username
            image
            role
          }
          post_image_comment {
            id
            message
            created_at
            item_image
            item_image_url
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      message
      errors {
        path

        message
      }
      code
    }
  }
`;

export const GET_FOLLOWING = gql`
  query getFollowing($filters: FollowingFilters, $limit: Int) {
    getFollowing(filters: $filters, limit: $limit) {
      success
      data {
        edges {
          id
          username
          image
          role
          accuracy
          badge {
            id
            name
          }
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_FOLLOWERS = gql`
  query getFollowers($filters: FollowersFilters, $limit: Int) {
    getFollowers(filters: $filters, limit: $limit) {
      success
      data {
        edges {
          id
          username
          image
          accuracy
          role
          badge {
            id
            name
          }
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_CHAT_LIST = gql`
  query getChatList($limit: Int, $after: String) {
    getChatList(limit: $limit, after: $after) {
      success
      message
      data {
        edges {
          id
          text
          created_at
          is_read_by_receiver
          isFromMe
          toUser {
            id
            username
            image
            role
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      errors {
        path
        message
      }
    }
  }
`;

export const GET_CHAT_MESSAGES = gql`
  query getChatMessages($userId: ID!, $limit: Int, $after: String) {
    getChatMessages(userId: $userId, limit: $limit, after: $after) {
      success
      message
      data {
        edges {
          id
          text
          message_type
          created_at
          isFromMe
          toUser {
            id
            username
            image
          }
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      errors {
        path
        message
      }
    }
  }
`;
export const GET_PENDING_CHAT_MESSAGES = gql`
  query getPendingChatMessages($userId: ID!, $cursor: String!) {
    getPendingChatMessages(userId: $userId, cursor: $cursor) {
      success
      message
      data {
        id
        text
        image_url
        message_type
        created_at
        isFromMe
        toUser {
          id
          username
          first_name
          last_name
          image
        }
      }
      errors {
        path
        message
      }
    }
  }
`;

export const GET_ALL_POSTS = gql`
  query getPosts($limit: Int, $after: String, $filters: PostFilter) {
    getPosts(limit: $limit, after: $after, filters: $filters) {
      id @client
      success
      data {
        edges {
          id
          is_comment_allowed
          is_like_allowed
          post_status_type
          is_private
          title
          description
          show_score
          appraisal_value
          authentication_key
          authentication_status
          authentication_type {
            id
            name
            pricing
          }
          authentication_result {
            decision
            score
            expert_result {
              id
              appraisal_value
              decision
              comment
              expert {
                id
                expertise {
                  id
                  name
                }
                user {
                  id
                  image
                  username
                }
              }
            }
            community_result {
              id
              totalVotes
              decision
              score
            }
          }
          brand {
            id
            name
            description
            image_url
            created_at
          }
          pit_to_pit_measurement
          collar_to_bottom_measurement
          size {
            id
            name
          }
          material {
            id
            name
          }
          custom_material
          stitching {
            id
            name
          }
          condition {
            id
            name
          }
          category {
            id
            name
          }
          condition_description
          provenance
          from_decade {
            id
            name
          }
          images {
            id
            featured
            front
            front_upload_method
            back
            back_upload_method
            front_tag
            front_tag_upload_method
            back_tag
            back_tag_upload_method
            closeup_front
            closeup_front_upload_method
            closeup_back
            closeup_back_upload_method
            copyright
            copyright_upload_method
            arm_hem_stitching
            arm_hem_stitching_upload_method
            lower_hem_stitching
            lower_hem_stitching_upload_method
            extra
          }
          user {
            id
            email
            username
            first_name
            last_name
            phone
            image
            bio
            accuracy
            allow_push_notifications
            allow_message_from_non_follower
            account_verified
            two_factor_authentication
            badge {
              id
              name
              image_url
            }
            social_media {
              id
              name
              url
            }
            role
            authentication_count
            doIFollow
            followersCount
            followingsCount
            isBlockedByMe
          }
          created_at
          approved_at
          my_vote {
            id
            decision
            appraisal_value
            remarks
            created_at
          }
          queue_id
          number_of_post_comments
          number_of_post_image_comments {
            id
            authentication_image
            count
          }
          can_claim_nft
          nft_detail {
            id
          }
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_PAYMENT_TRANSACTIONS = gql`
  query getPaymentTransactions($limit: Int = 15, $after: String) {
    getPaymentTransactions(limit: $limit, after: $after) {
      success
      data {
        edges {
          id
          total
          tax
          payment_status
          created_at
          authentication_details {
            authentication_id
            authentication_type
            authentication_key
            title
            created_at
          }
        }
        pageInfo {
          hasMore
          endCursor
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_MY_VOTED_POSTS = gql`
  query getMyVotedPosts($limit: Int = 15, $after: String) {
    getMyVotedPosts(limit: $limit, after: $after) {
      success
      data {
        edges {
          id
          title
          appraisal_value
          is_voting_closed
          can_delete_vote
          authentication_result {
            id
            decision
            score
            community_result {
              id
              decision
              totalVotes
              score
            }
          }
          images {
            id
            front
            closeup_front
            featured
          }
          created_at
          my_vote {
            id
            decision
            appraisal_value
            remarks
            created_at
          }
        }

        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_EXPERT = gql`
  query getExpert($id: ID, $userId: ID) {
    getExpert(id: $id, userId: $userId) {
      success
      data {
        id
        expertise {
          id
          name
        }
        number_of_certifications
        user {
          id
          username
          bio
          image
          badge {
            id
            image_url
          }
          social_media {
            id
            url
          }
          doIFollow
          followersCount
          followingsCount
        }
      }
      errors {
        path
        message
      }
      code
      message
    }
  }
`;

export const GET_ALL_EXPERTISE = gql`
  query getAllExpertise($limit: Int, $after: String) {
    getAllExpertise(limit: $limit, after: $after) {
      success
      data {
        edges {
          id
          name
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_EXPERTS = gql`
  query getExperts($limit: Int, $after: String, $filters: ExpertFilters) {
    getExperts(limit: $limit, after: $after, filters: $filters) {
      success
      data {
        edges {
          id
          expertise {
            id
            name
          }
          user {
            id
            username
            first_name
            last_name
            image
            bio
            accuracy
            allow_push_notifications
            allow_message_from_non_follower
            badge {
              id
              name
            }
            social_media {
              id
              name
              url
            }
            role
            authentication_count
            doIFollow
          }
        }
        pageInfo {
          endCursor
          hasMore
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_CURRENT_USER_ROLE = gql`
  query {
    me {
      success
      data {
        role
      }
      errors {
        path
        message
      }
      message
      code
    }
  }
`;

export const GET_REJECTED_AUTHENTICATION = gql`
  query getRejectedAuthentication($id: ID!) {
    getRejectedAuthentication(id: $id) {
      success
      message
      code
      data {
        id
        title
        authentication_key
        authentication_type {
          id
          name
        }
        title
        brand {
          id
          name
        }
        is_custom_brand
        category {
          id
          name
        }
        size {
          id
          name
        }
        material {
          id
          name
        }
        stitching {
          id
          name
        }
        condition {
          id
          name
        }
        from_decade {
          id
          name
        }
        pit_to_pit_measurement
        collar_to_bottom_measurement
        condition_description
        provenance
        images {
          id
          front
          front_tag
          closeup_front
          back
          back_tag
          closeup_back
          copyright
          arm_hem_stitching
          lower_hem_stitching
          extra
        }
        images_stripped {
          id
          front
          front_tag
          closeup_front
          back
          back_tag
          closeup_back
          copyright
          arm_hem_stitching
          lower_hem_stitching
          extra
        }
        rn_number
      }
    }
  }
`;

export const GET_PAYMENT_DETAILS = gql`
  query getPaymentDetails(
    $authenticationId: ID!
    $discountId: Int
    $isUpgradeFromExpertCertifiedToExpertCertifiedNFT: Boolean = false
    $isIOS: Boolean = false
  ) {
    getPaymentDetails(
      authenticationId: $authenticationId
      discountId: $discountId
      isUpgradeFromExpertCertifiedToExpertCertifiedNFT: $isUpgradeFromExpertCertifiedToExpertCertifiedNFT
      isIOS: $isIOS
    ) {
      data {
        tax
        total
        subtotal
        isDiscountApplied
        discountAmount
        totalWithoutDiscount
        isFullDiscount
        iapKeyIOS
      }
      code
      message
      success
    }
  }
`;

export const GET_USERS = gql`
  query getUsers($limit: Int, $after: String, $filters: UserFilters) {
    getUsers(
      limit: $limit
      after: $after
      filters: $filters
      returnEmptyOnEmptyTerm: true
    ) {
      success
      data {
        edges {
          id
          username
          image
          role
          accuracy
          badge {
            id
            name
          }
        }
        pageInfo {
          endCursor
          hasMore
          totalCount
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_USER_CREDENTIAL = gql`
  query getUserProfile($id: ID, $username: String) {
    getUserProfile(id: $id, username: $username) {
      success
      data {
        id
        username
        role
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_FAQS = gql`
  query getFAQs {
    getFAQs {
      success
      data {
        id
        title
        body
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_POST_LIKED_BY_USERS = gql`
  query getPostLikedByUsers($postId: ID!, $limit: Int, $after: String) {
    getPostLikedByUsers(postId: $postId, limit: $limit, after: $after) {
      success
      data {
        pageInfo {
          endCursor
          hasMore
        }
        edges {
          id
          username
          image
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;

export const GET_AUTHENTICATION_OWNERSHIP_HISTORY = gql`
  query getAuthenticationOwnershipHistory(
    $postId: ID!
    $limit: Int
    $after: String
  ) {
    getAuthenticationOwnershipHistory(
      limit: $limit
      after: $after
      postId: $postId
    ) {
      success
      data {
        edges {
          id
          date
          ownership_status
          user {
            id
            username
            image
            role
          }
        }
        pageInfo {
          endCursor
          hasMore
          totalCount
        }
      }
      message
      errors {
        path
        message
      }
      code
    }
  }
`;
