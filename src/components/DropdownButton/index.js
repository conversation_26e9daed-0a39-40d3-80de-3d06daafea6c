import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';

import styles from './styles';
import Icon from '../Icon';
import colors from '../../assets/colors';

export default function DropdownButton({
  text = '',
  style = {},
  textStyle = {},
  onPress = () => null,
  iconTint = colors.black,
  iconSize = 10,
  iconStyle = {},
}) {
  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onPress}>
      <Text numberOfLines={1} style={[styles.text, textStyle]}>
        {text}
      </Text>
      <Icon
        name="chevron_down"
        size={iconSize}
        tint={iconTint}
        containerStyle={{marginLeft: 'auto', ...iconStyle}}
      />
    </TouchableOpacity>
  );
}
