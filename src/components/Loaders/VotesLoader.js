/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('screen');

const VotesLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          marginTop={10}
          paddingHorizontal={20}>
          <SkeletonPlaceholder.Item
            justifyContent="space-around"
            flexDirection="row">
            <SkeletonPlaceholder.Item
              width={50}
              height={50}
              borderRadius={50}
              marginRight={10}
            />
            <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
              <SkeletonPlaceholder.Item
                width={60}
                height={15}
                borderRadius={4}
                marginRight={5}
              />
              <SkeletonPlaceholder.Item
                width={50}
                height={15}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item width={50} height={30} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          marginTop={10}
          width={WIDTH}
          height={3}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          marginTop={10}
          paddingHorizontal={20}>
          <SkeletonPlaceholder.Item
            justifyContent="space-around"
            flexDirection="row">
            <SkeletonPlaceholder.Item
              width={50}
              height={50}
              borderRadius={50}
              marginRight={10}
            />
            <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
              <SkeletonPlaceholder.Item
                width={60}
                height={15}
                borderRadius={4}
                marginRight={5}
              />
              <SkeletonPlaceholder.Item
                width={50}
                height={15}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item width={50} height={30} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          marginTop={10}
          width={WIDTH}
          height={3}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          marginTop={10}
          paddingHorizontal={20}>
          <SkeletonPlaceholder.Item
            justifyContent="space-around"
            flexDirection="row">
            <SkeletonPlaceholder.Item
              width={50}
              height={50}
              borderRadius={50}
              marginRight={10}
            />
            <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
              <SkeletonPlaceholder.Item
                width={60}
                height={15}
                borderRadius={4}
                marginRight={5}
              />
              <SkeletonPlaceholder.Item
                width={50}
                height={15}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item width={50} height={30} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          marginTop={10}
          width={WIDTH}
          height={3}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          marginTop={10}
          paddingHorizontal={20}>
          <SkeletonPlaceholder.Item
            justifyContent="space-around"
            flexDirection="row">
            <SkeletonPlaceholder.Item
              width={50}
              height={50}
              borderRadius={50}
              marginRight={10}
            />
            <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
              <SkeletonPlaceholder.Item
                width={60}
                height={15}
                borderRadius={4}
                marginRight={5}
              />
              <SkeletonPlaceholder.Item
                width={50}
                height={15}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item width={50} height={30} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          marginTop={10}
          width={WIDTH}
          height={3}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          marginTop={10}
          paddingHorizontal={20}>
          <SkeletonPlaceholder.Item
            justifyContent="space-around"
            flexDirection="row">
            <SkeletonPlaceholder.Item
              width={50}
              height={50}
              borderRadius={50}
              marginRight={10}
            />
            <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
              <SkeletonPlaceholder.Item
                width={60}
                height={15}
                borderRadius={4}
                marginRight={5}
              />
              <SkeletonPlaceholder.Item
                width={50}
                height={15}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item width={50} height={30} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          marginTop={10}
          width={WIDTH}
          height={3}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          marginTop={10}
          paddingHorizontal={20}>
          <SkeletonPlaceholder.Item
            justifyContent="space-around"
            flexDirection="row">
            <SkeletonPlaceholder.Item
              width={50}
              height={50}
              borderRadius={50}
              marginRight={10}
            />
            <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
              <SkeletonPlaceholder.Item
                width={60}
                height={15}
                borderRadius={4}
                marginRight={5}
              />
              <SkeletonPlaceholder.Item
                width={50}
                height={15}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item width={50} height={30} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          marginTop={10}
          width={WIDTH}
          height={3}
          borderRadius={4}
        />
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: spacings.md,
    backgroundColor: colors.white,
  },
});

export default VotesLoader;
