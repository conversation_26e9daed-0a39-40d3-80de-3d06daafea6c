/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH - spacings.xl;

const AuthenticationTypeLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH / 1.5}
          marginBottom={10}
          height={10}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={10}
          height={20}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />

        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={40}
          height={20}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />

        <SkeletonPlaceholder.Item
          width={CARD_WIDTH / 3}
          marginBottom={10}
          height={10}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={20}
          height={60}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={20}
          height={60}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={40}
          height={60}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH / 3}
          marginBottom={10}
          height={10}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={20}
          height={60}
          borderRadius={4}
          flexDirection="row"
          justifyContent="space-between"
        />
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default AuthenticationTypeLoader;
