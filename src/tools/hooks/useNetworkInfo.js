import React from 'react';
import NetInfo from '@react-native-community/netinfo';

function useNetworkInfo() {
  const [networkStatus, setNetworkStatus] = React.useState({
    type: null,
    isConnected: true,
    isWifiEnabled: true,
  });

  React.useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setNetworkStatus({
        type: state.type,
        isConnected: state.isConnected,
        isWifiEnabled: state.isWifiEnabled,
      });
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return {networkStatus};
}

export default useNetworkInfo;
