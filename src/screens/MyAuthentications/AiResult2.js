import React, { useState } from 'react';
import { View, Text, Image, FlatList, StyleSheet, ScrollView, Modal, TouchableOpacity, Dimensions, BackHandler } from 'react-native';
import Icon from '../../components/Icon';
import { icons, images } from '../../assets/strings';
import colors from '../../assets/colors';
//import FastImage from '../../components/FastImage';

import FastImage from 'react-native-fast-image';
import ImageViewerModal from './ImageViewerModal';


const AuthenticationGrid = ({ route }) => {

  const { similarImagesList, overallResult } = route.params;

  const [modalVisible, setModalVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // React.useEffect(() => {
  //   const backAction = () => {
  //     if (modalVisible) {
  //       setModalVisible(false);  // Dismiss the modal
  //       return true;  // Prevent default behavior (closing the app)
  //     }
  //     return false;  // Allow the default back button behavior
  //   };

  //   const backHandler = BackHandler.addEventListener(
  //     "hardwareBackPress",
  //     backAction
  //   );

  //   // Cleanup the event listener on component unmount
  //   return () => backHandler.remove();
  // }, [modalVisible]);

   // Handle image click
   const onImagePress = (index) => {
    setCurrentImageIndex(index);
    setModalVisible(true);
  };

  // Dynamically render images in the layout
  const renderGallery = () => {
    const centerImage = similarImagesList[0];                // First image for the center
    const leftImages = similarImagesList.slice(1, 3);        // 2nd and 3rd images (left)
    const rightImages = similarImagesList.slice(3, 5);       // 4th and 5th images (right)
    const bottomImages = similarImagesList.slice(5);         // Remaining images for the bottom

    return (
      <ScrollView contentContainerStyle={styles.galleryContainer}>
        <View style={styles.row}>
          {/* Left Images (2nd and 3rd) */}
          <View style={styles.sideImagesWrapper}>
            {leftImages.length > 0 ? (
              leftImages.map((image, index) => (
                <View key={index} style={styles.imageWrapper}>
                  <Text style={styles.imageTitle}>{image.type}</Text>
                  <TouchableOpacity onPress={() => onImagePress(index + 1)}>
                    <View style={styles.imageContainer}>
                      <Image style={styles.sideImage} source={{ uri: image.uri }} />
                      <Icon style={styles.icon} size={20}
                        name={
                          image.passed ? icons.PASS_ICON_2 : icons.CANCEL_X_2
                        }
                        tint={
                          image.passed ? colors.green : colors.warning
                        }
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              ))
            ) : (
              <Text>No Left Images</Text> // Placeholder for missing images
            )}
          </View>

           {/* Center Image */}
           <View style={styles.centerImageWrapper}>
            {centerImage ? (
              <View style={styles.imageWrapper}>
                <Text style={styles.imageTitle}>{centerImage.type}</Text>
                <TouchableOpacity onPress={() => onImagePress(0)}>
                  <View style={styles.imageContainer}>
                    <Image style={styles.centerImage} source={{ uri: centerImage.uri }} />
                    <Icon style={styles.icon} size={20}
                        name={
                          centerImage.passed ? icons.PASS_ICON_2 : icons.CANCEL_X_2
                        }
                        tint={
                          centerImage.passed ? colors.green : colors.warning
                        }
                      />
                  </View>
                </TouchableOpacity>
              </View>
            ) : (
              <Text>No Center Image</Text> // Placeholder for missing center image
            )}
          </View>

          {/* Right Images (4th and 5th) */}
          <View style={styles.sideImagesWrapper}>
            {rightImages.length > 0 ? (
              rightImages.map((image, index) => (
                <View key={index} style={styles.imageWrapper}>
                  <Text style={styles.imageTitle}>{image.type}</Text>
                  <TouchableOpacity onPress={() => onImagePress(index + 3)}>
                    <View style={styles.imageContainer}>
                      <Image style={styles.sideImage} source={{ uri: image.uri }} />
                      <Icon style={styles.icon} size={20}
                        name={
                          image.passed ? icons.PASS_ICON_2 : icons.CANCEL_X_2
                        }
                        tint={
                          image.passed ? colors.green : colors.warning
                        }
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              ))
            ) : (
              <Text>No Right Images</Text> // Placeholder for missing right images
            )}
          </View>
        </View>

        {/* Bottom Row (Remaining Images) */}
        {bottomImages.length > 0 ? (
          <View style={styles.bottomRow}>
            {bottomImages.map((image, index) => (
              <View key={index} style={styles.bottomImageWrapper}>
                <Text style={styles.imageTitle}>{image.type}</Text>
                <TouchableOpacity onPress={() => onImagePress(index + 5)}>
                  <View style={styles.imageContainer}>
                    <Image style={styles.bottomImage} source={{ uri: image.uri }} />
                    <Icon style={styles.icon} size={20}
                        name={
                          image.passed ? icons.PASS_ICON_2 : icons.CANCEL_X_2
                        }
                        tint={
                          image.passed ? colors.green : colors.warning
                        }
                      />



                  </View>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        ) : (
          <Text>No Bottom Images</Text> // Placeholder for missing bottom images
        )}
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      {/* Main Heading */}
      <Text style={styles.mainHeading}>{overallResult}</Text>

      {/* Render the gallery dynamically based on the number of images */}
      {renderGallery()}

{/* Image Viewer Modal */}
<ImageViewerModal
        isVisible={modalVisible}
        images={similarImagesList.map((img) => ({ url: img.uri, title: img.title }))}
        onClose={() => setModalVisible(false)}
        initialIndex={currentImageIndex}
      />
      
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
    backgroundColor: '#fff',
  },
  mainHeading: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  galleryContainer: {
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 10,
  },
  sideImagesWrapper: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginHorizontal: 5,
  },
  imageWrapper: {
    alignItems: 'center',
    marginVertical: 5,
  },
  imageTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  imageContainer: {
    position: 'relative', // This is required to position the icon within the image
  },
  sideImage: {
    width: 100,
    height: 100,
  },
  centerImageWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  centerImage: {
    width: 100, // Set to match the width of the side images
    height: 150, // Set to match the height of the side images
    margin: 5,
  },
  bottomRow: {
    flexDirection: 'row',
    flexWrap: 'wrap', // Allows the images to wrap to the next line if there are many
    justifyContent: 'space-around',
    marginTop: 20,
  },
  bottomImageWrapper: {
    marginVertical: 10,
    marginHorizontal: 5,
    alignItems: 'center',
    flexBasis: '30%', // Take about 1/3rd of the row width
  },
  bottomImage: {
    width: 100,
    height: 100,
  },
  icon: {
    position: 'absolute',
    bottom: 5,
    right: 5,
  },
  button: {
    backgroundColor: '#007BFF',
    padding: 10,
    alignItems: 'center',
    borderRadius: 5,
    marginTop: 20,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
});
export default AuthenticationGrid;
