import React, {useCallback, useState} from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import {Formik, Field} from 'formik';
import * as yup from 'yup';
import {useMutation} from '@apollo/client';

import {CHANGE_PASSWORD} from '../../apollo/mutations';
import CustomInput from '../../components/AuthInput';
import {styles} from './style';
import {
  profileSettingsScreen,
  passwordErrors,
  icons,
} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import Icon from '../../components/Icon';
import {spacings} from '../../assets/spacing';
import InfoModal from '../../components/Modal/InfoModal';
import locale from '../../assets/locale.json';
import Layout from '../../layout';
import Button from '../../components/Button';

const resetPasswordSchema = yup.object().shape({
  currentPassword: yup.string().required(passwordErrors.REQUIRED),
  password: yup
    .string()
    .matches(/(?=.*\d)/, passwordErrors.INCLUDE_NUMBER)
    .matches(
      /((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})/,
      passwordErrors.INCLUDE_SPECIAL_CHARACTER,
    )
    .matches(/^(\S+$)/, passwordErrors.NO_SPACES)
    .min(8, passwordErrors.MINIMUM_CHARACTERS)
    .required(passwordErrors.REQUIRED),
  loginpassword: yup
    .string()
    .oneOf([yup.ref('password'), null], "Password doesn't match")
    .required('Please confirm your password'),
});

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

const Index = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentHidden, setCurrentHidden] = useState(true);
  const [confirmHidden, setConfirmHidden] = useState(true);
  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [changePassword] = useMutation(CHANGE_PASSWORD);

  const handlePasswordChange = useCallback(
    async ({current_password, new_password}) => {
      setIsLoading(true);
      try {
        const {data} = await changePassword({
          variables: {
            oldPassword: current_password,
            newPassword: new_password,
          },
        });
        const isSuccess = data?.changePassword?.success;
        const code = data?.changePassword?.code;
        const message = data?.changePassword?.message;

        if (isSuccess) {
          setPopup(previousState => ({
            ...previousState,
            state: true,
            isError: false,
            data: {
              title: locale.Success,
              description: locale.PasswordChangedSuccessfully,
            },
          }));
        } else {
          if (code?.length) {
            setPopup(previousState => ({
              ...previousState,
              state: true,
              isError: true,
              data: {
                title: locale.Error,
                description: message,
              },
            }));
          } else {
            throw new Error();
          }
        }
      } catch (_) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: true,
          data: {
            title: locale.Error,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }));
      } finally {
        setIsLoading(false);
      }
    },
    [changePassword],
  );

  const toggleCurrentPassword = () => {
    setCurrentHidden(!currentHidden);
  };

  const toggleConfirmPassword = () => {
    setConfirmHidden(!confirmHidden);
  };

  return (
    <Layout contentContainerStyle={styles.container}>
      <Formik
        validationSchema={resetPasswordSchema}
        initialValues={{
          currentPassword: '',
          password: '',
          loginpassword: '',
        }}
        onSubmit={async values => {
          let data = {
            current_password: values.currentPassword,
            new_password: values.password,
            confirm_password: values.loginpassword,
          };
          handlePasswordChange(data);
        }}>
        {({handleSubmit}) => (
          <>
            <View style={styles.loginContainer}>
              <View
                style={[
                  styles.inputCard,
                  {
                    borderBottomWidth: 2,
                    borderBottomColor: colors.lightGrey,
                    paddingBottom: spacings.xl,
                  },
                ]}>
                <Text style={styles.inputLabel}>
                  {profileSettingsScreen.CURRENT_PASSWORD}
                </Text>
                <View style={{justifyContent: 'center', position: 'relative'}}>
                  <Field
                    style={styles.inputField}
                    component={CustomInput}
                    name="currentPassword"
                    placeholder="Password"
                    keyboardType="default"
                    secureTextEntry={currentHidden}
                    autofocus
                  />
                  <TouchableOpacity
                    style={[styles.togglePassword]}
                    onPress={toggleCurrentPassword}>
                    {currentHidden ? (
                      <Icon
                        name={icons.EYE_OPEN}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    ) : (
                      <Icon
                        name={icons.EYE_CLOSED}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {profileSettingsScreen.PASSWORD}
                </Text>
                <View>
                  <Field
                    style={styles.inputField}
                    component={CustomPasswordComponent}
                    name="password"
                    placeholder="New password"
                    keyboardType="default"
                    autofocus
                  />
                </View>
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {profileSettingsScreen.CONFIRM_PASSWORD}
                </Text>
                <View style={{justifyContent: 'center', position: 'relative'}}>
                  <Field
                    style={styles.inputField}
                    component={CustomInput}
                    name="loginpassword"
                    placeholder="Confirm password"
                    keyboardType="default"
                    secureTextEntry={confirmHidden}
                    autofocus
                  />
                  <TouchableOpacity
                    style={[styles.togglePassword]}
                    onPress={toggleConfirmPassword}>
                    {confirmHidden ? (
                      <Icon
                        name={icons.EYE_OPEN}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    ) : (
                      <Icon
                        name={icons.EYE_CLOSED}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              <Button
                text={profileSettingsScreen.BUTTON_LABEL}
                isLoading={isLoading}
                disabled={isLoading}
                onPress={handleSubmit}
                style={{
                  minHeight: 50,
                  width: '100%',
                  borderRadius: 5,
                }}
                containerStyle={{alignSelf: 'stretch', marginTop: 20}}
              />
            </View>
          </>
        )}
      </Formik>
      <InfoModal
        setVisible={() => {
          if (popup.state && !popup.isError) {
            navigation.goBack();
          }
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
    </Layout>
  );
};

function CustomPasswordComponent({...props}) {
  const [passwordHidden, setPasswordHidden] = useState(true);

  const togglePassword = () => {
    setPasswordHidden(!passwordHidden);
  };

  return (
    <View style={styles.passwordWrapper}>
      <CustomInput {...props} secureTextEntry={passwordHidden}>
        <TouchableOpacity
          style={styles.togglePassword}
          onPress={togglePassword}>
          {
            <Icon
              name={passwordHidden ? icons.EYE_OPEN : icons.EYE_CLOSED}
              size={fontSize.xxxxl}
              tint={colors.black}
            />
          }
        </TouchableOpacity>
      </CustomInput>
    </View>
  );
}

export default Index;
