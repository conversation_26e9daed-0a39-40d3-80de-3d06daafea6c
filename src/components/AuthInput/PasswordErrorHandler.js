import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import {fontSize} from '../../assets/font';
import {icons, passwordErrors} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import Icon from '../Icon';

const specialCharacterFormat = /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
const spaceCharacter = ' ';

const PasswordErrorHandler = ({errors, value}) => {
  const [eightCharacter, setEightCharacter] = useState(colors.grey);
  const [includeNumber, setIncludeNumber] = useState(colors.grey);
  const [specialCharacter, setSpecialCharacter] = useState(colors.grey);
  const [noSpaces, setNoSpaces] = useState(colors.grey);

  useEffect(() => {
    if (errors?.password === passwordErrors.REQUIRED) {
      setEightCharacter(colors.grey);
      setIncludeNumber(colors.grey);
      setSpecialCharacter(colors.grey);
      setNoSpaces(colors.grey);
      return;
    }

    if (value.length > 7) {
      setEightCharacter(colors.green);
    } else if (value.length < 8) {
      setEightCharacter(colors.grey);
    }

    if (
      errors?.password === passwordErrors.INCLUDE_NUMBER &&
      value.length > 0
    ) {
      setIncludeNumber(colors.grey);
    } else if (
      errors?.password !== passwordErrors.INCLUDE_NUMBER &&
      value.length > 0
    ) {
      setIncludeNumber(colors.green);
    }

    if (value.match(specialCharacterFormat)) {
      setSpecialCharacter(colors.green);
    } else {
      setSpecialCharacter(colors.grey);
    }

    if (value.match(spaceCharacter) || value < 1) {
      setNoSpaces(colors.grey);
    } else {
      setNoSpaces(colors.green);
    }
  }, [errors, specialCharacter, value]);

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.charactersWrapper}>
          <Icon
            name={
              eightCharacter === colors.grey
                ? icons.PASSWORD_FALSE
                : icons.PASSWORD_CORRECT
            }
            tint={eightCharacter}
            size={fontSize.lg}
          />
          <Text style={styles.passwordErrorText}>
            {passwordErrors.CHECK_MINIMUM_CHARACTERS}
          </Text>
        </View>
        <View style={styles.charactersWrapper}>
          <Icon
            name={
              includeNumber === colors.grey
                ? icons.PASSWORD_FALSE
                : icons.PASSWORD_CORRECT
            }
            tint={includeNumber}
            size={fontSize.lg}
          />
          <Text style={styles.passwordErrorText}>
            {passwordErrors.CHECK_INCLUDE_NUMBER}
          </Text>
        </View>
      </View>
      <View style={styles.section}>
        <View style={styles.charactersWrapper}>
          <Icon
            name={
              specialCharacter === colors.grey
                ? icons.PASSWORD_FALSE
                : icons.PASSWORD_CORRECT
            }
            tint={specialCharacter}
            size={fontSize.lg}
          />
          <Text numberOfLines={2} style={styles.passwordErrorText}>
            {passwordErrors.CHECK_INCLUDE_SPECIAL_CHARACTER}
          </Text>
        </View>
        <View style={styles.charactersWrapper}>
          <Icon
            name={
              noSpaces === colors.grey
                ? icons.PASSWORD_FALSE
                : icons.PASSWORD_CORRECT
            }
            color={noSpaces}
            size={fontSize.lg}
          />
          <Text numberOfLines={2} style={styles.passwordErrorText}>
            {passwordErrors.CHECK_NO_SPACES}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 10,
  },
  passwordErrorText: {
    fontSize: 12,
    marginLeft: 5,
    color: colors.darkGrey,
  },
  charactersWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1 / 2,
    marginRight: 20,
    paddingRight: 10,
  },
  section: {
    flexDirection: 'row',
    marginTop: spacings.sm,
  },
});

export default PasswordErrorHandler;
