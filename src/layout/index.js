import React from 'react';
import {View, SafeAreaView, StyleSheet} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import colors from '../assets/colors';

const CompoundContext = React.createContext({valid: false});

export const Header = ({children, ...otherProps}) => {
  const compoundContext = React.useContext(CompoundContext);
  return compoundContext?.valid ? (
    <View style={{...otherProps?.style}}>{children}</View>
  ) : null;
};

export const Footer = ({children, ...otherProps}) => {
  const compoundContext = React.useContext(CompoundContext);

  return compoundContext?.valid ? (
    <View style={{...otherProps?.style}}>{children}</View>
  ) : null;
};

export const Overlay = ({children}) => {
  const compoundContext = React.useContext(CompoundContext);

  return compoundContext?.valid ? children : null;
};

export default function BaseLayout({
  style = {},
  headerContainerStyle = {},
  rootStyle = {},
  scrollChildren = true,
  children: _children = null,
  contentContainerStyle = {},
}) {
  const children = [];
  const overlayChildren = [];
  const headerChildren = [];
  const footerChildren = [];

  React.Children.forEach(_children, child => {
    if (child?.type === Header) {
      headerChildren.push(child);
    } else if (child?.type === Footer) {
      footerChildren.push(child);
    } else if (child?.type === Overlay) {
      overlayChildren.push(child);
    } else {
      children.push(child);
    }
  });

  const ViewType = scrollChildren ? <KeyboardAwareScrollView /> : <View />;

  return (
    <CompoundContext.Provider value={{valid: true}}>
      <SafeAreaView style={styles.container}>
        <View style={{...styles.root, ...rootStyle}}>
          {overlayChildren}
          {headerChildren.length ? (
            <View style={{...styles.headerContainer, ...headerContainerStyle}}>
              {headerChildren}
            </View>
          ) : null}

          {React.cloneElement(ViewType, {
            style: {
              ...style,
              ...styles.main,
              paddingTop: !headerChildren.length ? 0 : 50,
            },
            contentContainerStyle: [contentContainerStyle],
            children,
          })}

          {footerChildren.length ? (
            <View style={styles.footerContainer}>{footerChildren}</View>
          ) : null}
        </View>
      </SafeAreaView>
    </CompoundContext.Provider>
  );
}

BaseLayout.Header = Header;
BaseLayout.Footer = Footer;
BaseLayout.Overlay = Overlay;

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 0,
  },
  main: {flex: 1},
  footerContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
});
