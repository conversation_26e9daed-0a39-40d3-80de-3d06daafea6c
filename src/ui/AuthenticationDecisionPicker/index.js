// @ts-check
import {View, Text, TouchableOpacity} from 'react-native';
import React from 'react';

import BottomSheet from '../../components/BottomSheet';
import styles from './styles';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';

const RESULTS = [
  {
    id: 'ALL',
    name: 'All',
  },
  {
    id: 'REAL',
    name: 'Pass',
  },
  {
    id: 'FAKE',
    name: 'Fail',
  },
  {
    id: 'UNDECIDED',
    name: 'Undecided',
  },
  {
    id: 'UNDETERMINED',
    name: 'Undetermined',
  },
];

export default function AuthenticationDecisionPicker({
  children = () => null,
  onSelected = () => null,
}) {
  const [visible, setVisible] = React.useState(false);
  const [selectedVote, setSelectedVote] = React.useState(RESULTS[0]);

  const handleOpenPicker = () => {
    setVisible(true);
  };

  const handleItemPressed = React.useCallback(
    item => {
      setSelectedVote(item);
      onSelected(item?.id == 'ALL' ? null : item);
      setVisible(false);
    },
    [onSelected],
  );

  return (
    <>
      {children({selected: selectedVote, openPicker: handleOpenPicker})}
      <BottomSheet
        visible={visible}
        setVisible={setVisible}
        automaticallyAdjustHeight
        key="authentication_decision">
        <View style={{minHeight: 50, padding: 20}}>
          {RESULTS?.map(item => (
            <TouchableOpacity
              key={item?.id}
              style={styles.optionWrapper}
              onPress={() => handleItemPressed(item)}>
              <Text style={styles.modalLabel}>{item?.name}</Text>
              <Icon
                name={
                  selectedVote?.id == item?.id
                    ? 'checked_radio'
                    : 'unchecked_radio'
                }
                size={20}
                tint={
                  selectedVote?.id == item?.id ? colors.primary : colors.grey
                }
              />
            </TouchableOpacity>
          ))}
        </View>
      </BottomSheet>
    </>
  );
}
