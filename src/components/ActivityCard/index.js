import React, {useMemo} from 'react';
import {StyleSheet, Text, View, Dimensions} from 'react-native';
import dayjs from 'dayjs';

import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {icons, images} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import FastImage from '../FastImage';
import {useUser} from '../../tools/hooks';

const {width} = Dimensions.get('window');
const WIDTH = width / 2;
const CARD_WIDTH = WIDTH - spacings.xl;

const ActivityCard = ({item}) => {
  const {user} = useUser();

  const backgroundColor = React.useMemo(() => {
    const decision = item?.authentication_result?.decision;
    switch (decision) {
      case 'REAL':
        return colors.greenBadge;
      case 'FAKE':
        return colors.redBadge;
      case 'UNDECIDED':
        return colors.darkBadge;
      case 'UNDETERMINED':
        return colors.amber;
      default:
        colors.darkBadge;
    }
  }, [item?.authentication_result?.decision]);

  const handleMyDecision = useMemo(() => {
    switch (item?.my_vote?.decision) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
        return 'UNDETERMINED';
      default:
        return 'PENDING';
    }
  }, [item?.my_vote?.decision]);

  const handleCommunityDecision = useMemo(() => {
    switch (item?.authentication_result?.decision) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
        return 'UNDETERMINED';
      default:
        return 'PENDING';
    }
  }, [item?.authentication_result?.decision]);

  const isCommunityAuthentication = React.useMemo(
    () => item?.authentication_type?.id == 1,
    [item?.authentication_type?.id],
  );

  const isPostByMyself = React.useMemo(() => item?.user?.id == user?.id, [
    user?.id,
    item?.user?.id,
  ]);

  const haveINotVoted = React.useMemo(() => item?.my_vote == null, [
    item?.my_vote,
  ]);

  const isVotingClosed = React.useMemo(
    () => (item?.is_voting_closed == null ? true : item?.is_voting_closed),
    [item?.is_voting_closed],
  );

  const isRevealing = React.useMemo(
    () => handleCommunityDecision !== 'PENDING' && !isVotingClosed,
    [handleCommunityDecision, isVotingClosed],
  );

  const canVote = React.useMemo(
    () =>
      isCommunityAuthentication &&
      !isPostByMyself &&
      haveINotVoted &&
      !isVotingClosed,
    [isCommunityAuthentication, isPostByMyself, haveINotVoted, isVotingClosed],
  );

  return (
    <>
    {console.log('item', item)}
      <View style={styles.cardsGroup}>
        <View>
          <FastImage
            source={
              item?.images?.featured || item?.images?.closeup_front
                ? {uri: item?.images?.featured ?? item?.images?.closeup_front}
                : images.PLACEHOLDER
            }
            style={styles.imageStyle}
          />

          <View
            style={[
              styles.badgeStyle,
              {
                backgroundColor,
              },
            ]}>
            <Icon
              name={
                isCommunityAuthentication
                  ? icons.PEOPLE_OUTLINE
                  : icons.PERSON_ICON
              }
              size={fontSize.sm}
              tint={colors.white}
            />

            {isCommunityAuthentication && isRevealing ? (
              <Text
                style={
                  styles.badgeText
                }>{`${handleCommunityDecision}ing`}</Text>
            ) : (
              <>
                <Text style={styles.badgeText}>{handleCommunityDecision}</Text>
                {item?.authentication_result?.decision === 'UNDECIDED' ||
                item?.authentication_type?.id === 2 ||
                item?.authentication_type?.id === 3 ? null : (
                  <Text style={[styles.badgeText, {marginRight: spacings.sm}]}>
                    {item?.authentication_result?.score}%
                  </Text>
                )}
              </>
            )}
          </View>
          {item?.my_vote ? (
            <View
              style={[
                styles.voteBadge,
                {
                  backgroundColor: colors.darkBadge,
                  alignItems: 'center',
                },
              ]}>
              <Icon
                name={
                  handleMyDecision === 'PASS' ? icons.PASS_ICON : icons.CANCEL_X
                }
                size={9}
                tint={
                  handleMyDecision === 'PASS' ? colors.green : colors.warning
                }
              />
              <Text style={[styles.badgeText, {fontSize: 10}]}>
                Voted {handleMyDecision}
              </Text>
            </View>
          ) : canVote ? (
            <View
              style={[
                styles.voteBadge,
                {
                  backgroundColor: colors.secondary,
                  alignItems: 'center',
                },
              ]}>
              <Text style={[styles.badgeText, {fontSize: 10}]}>Vote</Text>
            </View>
          ) : null}
          {(item?.authentication_type?.id == 1 &&
            item?.authentication_result?.decision === 'REAL' &&
            item?.appraisal_value !== null) ||
          (item?.authentication_result?.expert_result?.appraisal_value &&
            item?.authentication_result?.expert_result?.decision === 'REAL') ? (              
            <View style={[styles.appraisalBadge]}>

              {
                item.is_private ? null : 
                <Icon
                name={
                  isCommunityAuthentication
                    ? icons.PEOPLE_OUTLINE
                    : icons.PERSON_ICON
                }
                size={12}
                tint={colors.white}
              />
              }
              
              <Text style={[styles.badgeText, {fontSize: 10}]}>

                {
                  //!isPostByMyself && item.is_private ? null 
                  item.is_private ? null 
                  : '$' + item?.authentication_type?.id == 1 &&
                    item?.authentication_result?.decision !== 'FAKE' &&
                    item?.appraisal_value !== null
                      ?  item?.appraisal_value
                      : item?.authentication_result?.expert_result?.appraisal_value
                }


              </Text>
            </View>
          ) : null}
        </View>

        <View style={[styles.textBar, {marginTop: 5}]}>
          <Text numberOfLines={1} style={styles.titleStyle}>
            {item?.title}
          </Text>
          <View style={styles.userBar}>
            <FastImage
              source={
                item?.user?.image
                  ? {uri: item?.user?.image}
                  : images.PROFILE_PLACEHOLDER
              }
              style={styles.profileImageStyle}
            />
            <Text style={styles.userStyle}>{item?.user?.username}</Text>
          </View>
          <Text style={styles.timeStyle}>
            {dayjs(item?.approved_at ?? item?.created_at).fromNow()}
          </Text>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  cardsGroup: {
    width: CARD_WIDTH,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: colors.lightGrey,
  },
  imageStyle: {
    width: '100%',
    height: CARD_WIDTH / 1.4,
    borderRadius: 2,
  },
  profileImageStyle: {
    height: 20,
    width: 20,
    borderRadius: 20,
  },
  featured: {},
  userBar: {
    flexDirection: 'row',
    paddingVertical: spacings.sm,
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
  },
  textBar: {
    width: '100%',
    paddingBottom: spacings.md,
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  userStyle: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
    marginLeft: spacings.sm,
  },
  timeStyle: {
    fontSize: 10,
    color: colors.darkGrey,
  },
  voteBadge: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  appraisalBadge: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
    backgroundColor: colors.darkBadge,
  },
  badgeStyle: {
    position: 'absolute',
    right: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  badgeText: {
    fontSize: fontSize.xs,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: 3,
    textTransform: 'capitalize',
  },
  featuredWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 5,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    paddingVertical: 2,
    alignSelf: 'flex-start',
  },
  featuredText: {
    color: colors.white,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    letterSpacing: 0.5,
  },
});

export default React.memo(ActivityCard);
