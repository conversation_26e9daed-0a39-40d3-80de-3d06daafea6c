import React from 'react';
import Autolink from 'react-native-autolink';
import {useNavigation} from '@react-navigation/native';
import {useLazyQuery} from '@apollo/client';

import colors from '../../assets/colors';
import {screens} from '../../assets/strings';
import {GET_USER_CREDENTIAL} from '../../apollo/queries';
import {showToast, hideToast} from '../Toast';
import {replaceAll} from '../../tools/utils';
import {useCredential} from '../../tools/hooks/useUser';

export default function TextAutoLink({
  text,
  linkStyle = {},
  mentionStyle = {},
}) {
  const navigation = useNavigation();
  const credential = useCredential();

  const userId = credential?.user_id;

  const [getUserCredential] = useLazyQuery(GET_USER_CREDENTIAL);

  const handleUsernamePressed = React.useCallback(
    async match => {
      hideToast();
      showToast({type: 'loading', message: 'Please wait...'});
      const username = replaceAll(match?.matchedText, '@', '');
      try {
        const {data} = await getUserCredential({
          variables: {username: username},
        });

        const success = data?.getUserProfile?.success;
        const message = data?.getUserProfile?.message;
        const userData = data?.getUserProfile?.data;
        const isExpert = userData?.role === 'EXPERT';
        if (success) {
          hideToast();
          navigation.push(
            userId == userData?.id
              ? screens.USER_PROFILE
              : isExpert
              ? screens.EXPERTS_PROFILE
              : screens.PUBLIC_PROFILES,
            {
              ...(isExpert
                ? {
                    expertUserId: userData?.id,
                  }
                : {
                    params: userData?.id,
                  }),
            },
          );
        } else {
          hideToast();
          showToast({message});
        }
      } catch (_) {}
    },
    [navigation.push, getUserCredential, userId],
  );

  return (
    <>
      <Autolink
        useNativeSchemes
        text={text}
        email
        url
        phone
        linkStyle={[{color: colors.primary}, linkStyle]}
        matchers={[
          {
            pattern: /[@][A-Za-z][A-Za-z0-9]+(?:[.|_][A-Za-z0-9]+)*/g,
            onPress: handleUsernamePressed,
            style: [
              {
                fontWeight: 'bold',
                color: colors.primary,
                fontSize: 15,
              },
              mentionStyle,
            ],
            type: 'username',
            getLinkText: ([text]) => {
              return text;
            },
            onLongPress: handleUsernamePressed,
          },
        ]}
      />
    </>
  );
}
