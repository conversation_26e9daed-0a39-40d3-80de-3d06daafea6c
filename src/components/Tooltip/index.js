import React from 'react';
import {Text, View} from 'react-native';

import Icon from '../Icon';
import colors from '../../assets/colors';
import TouchAwareButton from '../Button/TouchAwareButton';
import BottomSheet from '../BottomSheet';
import styles from './styles';

export default function Tooltip({
  onPress = () => null,
  size = 18,
  tint = colors.darkGrey,
  style = {},
  containerStyle = {},
  enableSheetContent = false,
  title = '',
  description = '',
  automaticallyAdjustHeight = true,
  snapPoints = [0],
}) {
  const [sheetVisible, setSheetVisible] = React.useState(false);

  const handlePress = () => {
    setSheetVisible(true);
  };

  return (
    <>
      <TouchAwareButton
        onPress={enableSheetContent ? handlePress : onPress}
        scaleAnimationThreshold={0.95}
        disableLongPressAnimation
        style={containerStyle}>
        <Icon
          name="questionmark_circle"
          size={size}
          tint={tint}
          style={style}
        />
      </TouchAwareButton>
      {enableSheetContent ? (
        <BottomSheet
          visible={sheetVisible}
          setVisible={setSheetVisible}
          automaticallyAdjustHeight={automaticallyAdjustHeight}
          {...(automaticallyAdjustHeight ? {} : {snapPoints})}>
          <View style={styles.container}>
            <Text style={[styles.title]}>{title}</Text>
            <Text style={[styles.description]}>{description}</Text>
          </View>
        </BottomSheet>
      ) : null}
    </>
  );
}
