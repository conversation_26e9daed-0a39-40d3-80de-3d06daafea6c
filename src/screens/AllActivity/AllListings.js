import React, {useCallback, useState} from 'react';
import {StyleSheet, View, FlatList, Dimensions, Platform} from 'react-native';
import {useQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import TouchAwareButton from '../../components/Button/TouchAwareButton';
import HomeListingsLoader from '../../components/Loaders/HomeListingsLoader';
import {GET_ALL_LISTING_HOME_TAB} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import ActivityCard from '../../components/ActivityCard';
import {screens} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import ActivityIndicator from '../../components/ActivityIndicator';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import DropdownButton from '../../components/DropdownButton';
import {BrandPicker, CategoryPicker} from '../../ui';
import RefreshControl from '../../components/RefreshControl';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import locale from '../../assets/locale.json';
import {useInteractionManager} from '../../tools/hooks';

const {width: WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const LIMIT = 20;

const isIOS = Platform.OS === 'ios';

const AllListings = ({navigation}) => {
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [isFetchingMore, setIsFetchingMore] = React.useState(false);

  const scrollRef = React.useRef();

  const cursorRef = React.useRef([]);

  const {
    data: listingsData,
    refetch,
    fetchMore,
    loading: listingsLoading,
    variables,
  } = useQuery(GET_ALL_LISTING_HOME_TAB, {
    nextFetchPolicy: 'cache-only',
    variables: {
      cacheId: APOLLO_CACHE_IDS.getPosts_ALL_LISTINGS,
      limit: LIMIT,
      filters: {
        ...(selectedBrands?.length > 0 ? {brand_ids: selectedBrands} : {}),
        ...(selectedCategories?.length > 0
          ? {category_ids: selectedCategories}
          : {}),
      },
    },
  });

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          cursorRef.current = [];
          refetch?.();
        }
      } catch (error) {}
    }, [refetch]),
  );

  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: listingsData == null ? [] : ['android'],
  });

  const resetCursorRef = React.useCallback(() => {
    cursorRef.current = [];
  }, []);

  const handleCategoryChange = ids => {
    resetCursorRef();
    setSelectedCategories(ids);
  };

  const handleBrandChange = ids => {
    resetCursorRef();
    setSelectedBrands(ids);
  };

  const onRefresh = useCallback(async () => {
    try {
      setRefreshing(true);
      resetCursorRef();
      await refetch();
    } catch (_) {
    } finally {
      setRefreshing(false);
    }
  }, [refetch, resetCursorRef]);

  const handleItemPressed = React.useCallback(
    item => navigation.navigate(screens.SINGLE_LISTING, {params: item.id}),
    [navigation.navigate],
  );

  const renderItem = useCallback(
    ({item}) =>
      isInteractionsComplete ? (
        <TouchAwareButton onPress={() => handleItemPressed(item)}>
          <ActivityCard item={item} />
        </TouchAwareButton>
      ) : null,
    [handleItemPressed, isInteractionsComplete],
  );

  let cursor = React.useMemo(
    () => listingsData?.getPosts?.data?.pageInfo?.endCursor,
    [listingsData?.getPosts?.data?.pageInfo?.endCursor],
  );
  let hasMore = React.useMemo(
    () => listingsData?.getPosts?.data?.pageInfo?.hasMore,
    [listingsData?.getPosts?.data?.pageInfo?.hasMore],
  );

  const handleFetchMore = useCallback(() => {
    try {
      if (cursor !== null && hasMore && !cursorRef.current?.includes(cursor)) {
        cursorRef.current?.push(cursor);
        setIsFetchingMore(true);
        fetchMore?.({
          variables: {
            ...variables,
            after: cursor,
          },
          updateQuery(previousResult, {fetchMoreResult}) {
            setIsFetchingMore(false);

            const newCursor =
              fetchMoreResult?.getPosts?.data?.pageInfo?.endCursor;

            return {
              getPosts: {
                ...previousResult?.getPosts,
                data: {
                  edges: [
                    ...previousResult?.getPosts?.data?.edges,
                    ...fetchMoreResult?.getPosts?.data?.edges,
                  ],
                  pageInfo: {
                    endCursor: newCursor,
                    hasMore: fetchMoreResult?.getPosts?.data?.pageInfo?.hasMore,
                  },
                },
                code: fetchMoreResult?.getPosts?.code,
                errors: fetchMoreResult?.getPosts?.errors,
                message: fetchMoreResult?.getPosts?.message,
                success: fetchMoreResult?.getPosts?.success,
              },
            };
          },
        });
      }
    } catch (e) {
      setIsFetchingMore(false);
    }
  }, [cursor, fetchMore, hasMore, variables]);

  const renderHeader = React.useMemo(
    () => (
      <View style={styles.filters}>
        <BrandsFilter onSelected={handleBrandChange} />
        <CategoriesFilter onSelected={handleCategoryChange} />
      </View>
    ),
    [handleBrandChange, handleCategoryChange],
  );

  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  return (
    <View style={styles.container}>
      <FlatList
        data={listingsData?.getPosts?.data?.edges}
        columnWrapperStyle={styles.contentContainer}
        renderItem={renderItem}
        keyExtractor={extractKey}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={
          isInteractionsComplete ? (
            <View style={{paddingBottom: 100}}>
              {isFetchingMore ? <ActivityIndicator size={30} /> : null}
            </View>
          ) : (
            <View style={{flex: 1}}>
              <HomeListingsLoader />
            </View>
          )
        }
        ListEmptyComponent={
          !(listingsData?.getPosts?.data == null) ? (
            <View style={styles.emptyView}>
              <DefaultEmptyComponent
                lottieStyle={{width: 120, height: 120}}
                text={locale.NoListingsFound}
              />
            </View>
          ) : listingsLoading ? (
            <HomeListingsLoader />
          ) : null
        }
        onEndReachedThreshold={0.2}
        onEndReached={handleFetchMore}
        scrollToOverflowEnabled={true}
        scrollEventThrottle={1900}
        initialNumToRender={isIOS ? 10 : 5}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
      />
    </View>
  );
};

function BrandsFilter({onSelected = () => null}) {
  const [selected, setSelected] = React.useState([]);

  const handleApply = selectedItems => {
    setSelected(selectedItems);
    onSelected(selectedItems?.map(item => item?.id));
  };

  const handleClear = () => {
    setSelected([]);
    onSelected([]);
  };

  return (
    <>
      <BrandPicker
        onApply={handleApply}
        onClear={handleClear}
        selectedItems={selected}>
        {({openPicker}) => (
          <DropdownButton
            style={{
              width: '48%',
              ...(selected?.length > 0
                ? {backgroundColor: colors.primary}
                : {}),
            }}
            text={
              selected?.length > 0
                ? `Brand: ${selected?.map(item => item?.name)?.join(', ')}`
                : 'Brands: All'
            }
            textStyle={{
              color: selected?.length > 0 ? colors.white : colors.black,
            }}
            onPress={openPicker}
            iconTint={selected?.length > 0 ? colors.white : colors.black}
          />
        )}
      </BrandPicker>
    </>
  );
}

function CategoriesFilter({onSelected = () => null}) {
  const [selected, setSelected] = React.useState([]);

  const handleApply = selectedItems => {
    setSelected(selectedItems);
    onSelected(selectedItems?.map(item => item?.id));
  };
  const handleClear = () => {
    setSelected([]);
    onSelected([]);
  };
  return (
    <CategoryPicker
      onApply={handleApply}
      onClear={handleClear}
      selectedItems={selected}>
      {({openPicker}) => (
        <DropdownButton
          style={{
            width: '48%',
            ...(selected?.length > 0 ? {backgroundColor: colors.primary} : {}),
          }}
          text={
            selected?.length > 0
              ? `Category: ${selected?.map(item => item?.name)?.join(', ')}`
              : 'Category: All'
          }
          onPress={openPicker}
          textStyle={{
            color: selected?.length > 0 ? colors.white : colors.black,
          }}
          iconTint={selected?.length > 0 ? colors.white : colors.black}
        />
      )}
    </CategoryPicker>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },

  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.darkBadge,
  },
  visibleSection: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
    padding: spacings.xl,
    borderRadius: 4,
  },
  button: {
    width: WIDTH / 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacings.xl,
    paddingVertical: spacings.md,
    borderRadius: 4,
    marginVertical: spacings.md,
  },
  buttonText: {
    fontSize: fontSize.lg,
  },
  subHeading: {
    fontSize: fontSize.xxl,
    color: colors.black,
    marginVertical: spacings.lg,
  },
  heading: {
    fontSize: 30,
    fontWeight: 'bold',
    color: colors.black,
  },
  howItWorksButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.grey,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },

  titleStyle: {
    color: colors.black,
    fontWeight: 'bold',
    fontSize: fontSize.md,
    textTransform: 'capitalize',
  },

  filters: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.lg,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: colors.grey,
    marginBottom: 30,
  },
  pickerContainer: {
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 150,
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.md,
    backgroundColor: colors.lightGrey,
    marginBottom: spacings.lg,
  },
  pickerText: {
    color: colors.black,
    fontSize: fontSize.md,
    textTransform: 'capitalize',
  },
  brandLogoStyle: {
    height: 30,
    width: 50,
    resizeMode: 'contain',
  },
  logo: {
    height: '90%',
    width: '90%',
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacings.lg,
    marginTop: spacings.lg,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 1.7,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalSheetContainer: {
    paddingVertical: spacings.lg,
    marginHorizontal: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  checkedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 25,
    width: 25,
    borderRadius: 25,
    backgroundColor: colors.primary,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  uncheckedBox: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 25,
    width: 25,
    borderRadius: 25,
    backgroundColor: colors.lightGrey,
    borderWidth: 2,
    borderColor: colors.grey,
  },
});

export default AllListings;
