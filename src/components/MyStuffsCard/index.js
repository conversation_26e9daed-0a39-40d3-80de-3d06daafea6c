import React, {useCallback} from 'react';
import {StyleSheet, Text, View, Dimensions} from 'react-native';
import dayjs from 'dayjs';

import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {icons, followingFeedScreen, images} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import FastImage from '../FastImage';
import {fixedNumberTo} from '../../tools/utils';
import TouchAwareButton from '../Button/TouchAwareButton';

const {width} = Dimensions.get('window');
const WIDTH = width;
const CARD_WIDTH = WIDTH - spacings.xxl;

const MyStuffsCard = ({item, onMenuPressed = () => null}) => {
  const handleDecisionLayout = useCallback(type => {
    switch (type) {
      case 'REAL':
        return {
          backgroundColor: colors.greenBadge,
        };
      case 'FAKE':
        return {
          backgroundColor: colors.redBadge,
        };
      case 'UNDECIDED':
        return {
          backgroundColor: colors.darkBadge,
        };
      case 'UNDETERMINED':
        return {
          backgroundColor: colors.amber,
        };
    }
  }, []);

  const handleDecision = useCallback(decision => {
    switch (decision) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
        return 'UNDETERMINED';
      default:
        return 'PENDING';
    }
  }, []);

  const handleIconColor = useCallback(type => {
    switch (type) {
      case 'REAL':
        return colors.green;
      case 'FAKE':
        return colors.warning;
      case 'UNDECIDED':
        return colors.darkGrey;
      case 'UNDETERMINED':
        return colors.amber;
      default:
        return colors.grey;
    }
  }, []);

  const canDeleteVote = !item?.is_voting_closed && item?.can_delete_vote;

  return (
    <View style={styles.cardsGroup}>
      <View style={[styles.topBarWrapper, {marginBottom: 5}]}>
        <Text style={styles.titleStyle}>
          Voted item:{' '}
          <Text
            style={[
              styles.titleStyle,
              {
                fontFamily: 'Inter-Bold',
                color: handleIconColor(item?.my_vote?.decision),
              },
            ]}>
            {handleDecision(item?.my_vote?.decision)}{' '}
          </Text>
          <Text style={{fontFamily: 'Inter-Bold'}}>
            | ${item?.my_vote?.appraisal_value}
          </Text>
        </Text>
        {canDeleteVote ? (
          <TouchAwareButton
            onPress={onMenuPressed.bind(null, {voteId: item?.my_vote?.id})}>
            <Icon
              name={icons.ELIPSIS_HORIZONTAL}
              size={25}
              tint={colors.darkGrey}
              containerStyle={{
                paddingLeft: 10,
              }}
            />
          </TouchAwareButton>
        ) : null}
      </View>
      <View>
        <FastImage
          source={
            item?.images?.featured || item?.images?.closeup_front
              ? {uri: item?.images?.featured || item?.images?.closeup_front}
              : images.PLACEHOLDER
          }
          style={styles.imageStyle}
        />
        <View
          style={[
            styles.badgeStyle,
            handleDecisionLayout(
              item?.authentication_result?.community_result?.decision,
            ),
          ]}>
          <Icon
            name={icons.PEOPLE_OUTLINE}
            size={fontSize.md}
            tint={colors.white}
          />
          <Text style={styles.badgeText}>
            {handleDecision(
              item?.authentication_result?.community_result?.decision,
            )}
          </Text>
          {item?.authentication_result?.community_result?.decision ===
          'UNDECIDED' ? null : (
            <Text style={[styles.badgeText, {marginRight: spacings.sm}]}>
              (
              {fixedNumberTo(
                item?.authentication_result?.community_result?.score,
              )}
              )%
            </Text>
          )}
        </View>
        <View
          style={[
            styles.appraisalWrapper,
            {backgroundColor: colors.darkBadge},
          ]}>
          <Text style={styles.appraisalText}>
            {followingFeedScreen.APPRAISAL}
            Avg est: ${fixedNumberTo(item?.appraisal_value)}
          </Text>
        </View>
        <View style={styles.lowerBadgeStyle}>
          {item?.my_vote?.decision === 'REAL' ? (
            <Icon name="pass_icon" tint={colors.green} size={12} />
          ) : (
            <Icon name="cancel_x" tint={colors.warning} size={12} />
          )}
          <Text style={[styles.badgeText, {marginLeft: spacings.sm}]}>
            You voted {handleDecision(item?.my_vote?.decision)}
          </Text>
        </View>
      </View>
      <View style={styles.bottomBar}>
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          style={[
            styles.titleStyle,
            {fontFamily: 'Inter-Bold', maxWidth: '80%'},
          ]}>
          {item?.title}
        </Text>
        <Text style={styles.timeStyle}>
          {dayjs(item?.created_at).fromNow()}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardsGroup: {
    width: CARD_WIDTH,
    marginBottom: spacings.lg,
    borderRadius: 2,
    borderBottomColor: colors.lightGrey,
    paddingBottom: spacings.xl,
    borderBottomWidth: 3,
  },
  imageStyle: {
    width: '100%',
    height: CARD_WIDTH / 2,
    borderRadius: 2,
  },
  userBar: {
    flexDirection: 'row',
    paddingVertical: spacings.sm,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  textBar: {
    width: '100%',
    paddingBottom: spacings.md,
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Medium',
  },
  topBarWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  vote: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lowerBadgeStyle: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
    backgroundColor: colors.darkBadge,
  },
  badgeStyle: {
    position: 'absolute',
    right: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  badgeText: {
    fontSize: fontSize.sm,
    fontFamily: 'Inter-Medium',
    color: colors.white,
    marginLeft: spacings.sm,
    textTransform: 'capitalize',
  },
  appraisalWrapper: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  appraisalText: {
    fontSize: fontSize.sm,
    fontFamily: 'Inter-Bold',
    color: colors.white,
    marginLeft: spacings.sm,
  },
  profileImageStyle: {
    height: 20,
    width: 20,
    borderRadius: 20,
  },
  userStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    marginLeft: spacings.sm,
    fontFamily: 'Inter-Bold',
  },
  activityNotification: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
    marginLeft: spacings.sm,
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeStyle: {
    fontSize: 10,
    color: colors.darkGrey,
  },
  bottomBar: {
    marginTop: spacings.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  itemSeparator: {
    color: colors.white,
  },
});

export default MyStuffsCard;
