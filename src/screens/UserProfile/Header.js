import React from 'react';
import {TouchableOpacity, Text, View, Linking} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import {styles} from './styles';
import {screens, userProfileScreen} from '../../assets/strings';
import FastImage from '../../components/FastImage';
import ProfileImage from '../../components/ProfileImage';
import {stripHTTPSFromURL, formatURL} from '../../tools/utils';
import {fixedNumberTo} from '../../tools/utils';
import locale from '../../assets/locale.json';
import Button from '../../components/Button';

const Header = ({followersCount = 0, followingCount = 0, user = null}) => {
  const navigation = useNavigation();

  const socialMediaUrl = user?.social_media?.[0]?.url;

  const handleLinking = async () => {
    try {
      await Linking.openURL(formatURL(socialMediaUrl));
    } catch (_) {}
  };

  const handleEditProfilePress = React.useCallback(() => {
    navigation.navigate(screens.PERSONAL_INFORMATION);
  }, [navigation.navigate]);

  return (
    <View style={styles.headerContainer}>
      <View style={styles.imageWrapper}>
        <ProfileImage url={user?.image} size={70} canViewImage />
        <View style={styles.textSection}>
          <View style={{marginRight: 10}}>
            <Text style={styles.username}>{user?.username}</Text>
            <View style={styles.levelsWrapper}>
              <View style={styles.levelsButton}>
                <Text style={styles.levelsText}>{user?.badge?.name}</Text>
              </View>
            </View>
          </View>
          <FastImage
            source={{uri: user?.badge?.image_url}}
            style={styles.badge}
          />
        </View>
      </View>

      <View style={styles.descWrapper}>
        {user?.bio ? (
          <Text style={styles.desc} numberOfLines={5}>
            {user?.bio}{' '}
          </Text>
        ) : null}
        {socialMediaUrl ? (
          <TouchableOpacity onPress={handleLinking}>
            <Text style={styles.link} numberOfLines={1}>
              {stripHTTPSFromURL(socialMediaUrl)}
            </Text>
          </TouchableOpacity>
        ) : null}
      </View>
      <View style={styles.popularity}>
        <TouchableOpacity
          style={styles.popularityWrapper}
          onPress={() =>
            navigation.navigate(screens.FOLLOWING_LISTING, {
              params: user?.id,
            })
          }>
          <Text style={styles.popularityStat}>{followingCount ?? 0}</Text>

          <Text style={styles.desc}>{userProfileScreen.FOLLOWING}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.popularityWrapper}
          onPress={() =>
            navigation.navigate(screens.FOLLOWERS_LISTING, {
              params: user?.id,
            })
          }>
          <Text style={styles.popularityStat}>{followersCount ?? 0}</Text>

          <Text style={styles.desc}>{userProfileScreen.FOLLOWERS}</Text>
        </TouchableOpacity>
        <View style={styles.popularityWrapper}>
          <Text style={styles.popularityStat}>
            {user?.number_of_votes ?? 0}
          </Text>
          <Text style={styles.desc}>{locale.Votes}</Text>
        </View>
        <View style={styles.popularityWrapper}>
          <Text style={styles.popularityStat}>
            {fixedNumberTo(user?.accuracy ?? 0, {
              onlyFixIfItHasDecimal: true,
            })}
            {user?.accuracy === 0 || user?.accuracy == null ? null : '%'}
          </Text>
          <Text style={styles.desc}>{userProfileScreen.ACCURACY}</Text>
        </View>
      </View>
      <Button
        style={{width: '100%', marginVertical: 5, minHeight: 35}}
        text={locale.EditProfile}
        onPress={handleEditProfilePress}
      />
    </View>
  );
};

export default Header;
