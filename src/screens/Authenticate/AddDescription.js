import React, {useCallback, useState} from 'react';
import {Text, View, TouchableOpacity, FlatList, Switch} from 'react-native';

import {useMutation, useQuery} from '@apollo/client';
import {useSelector} from 'react-redux';
import {useIsFocused, useFocusEffect} from '@react-navigation/native';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import {ScrollView} from 'react-native-gesture-handler';

import {GET_AUTHENTICATION_INFO, GET_CATEGORIES} from '../../apollo/queries';
import {
  CREATE_AUTHENTICATION,
  RESUBMIT_REJECTED_AUTHENTICATION,
} from '../../apollo/mutations';
import {addDescriptionScreen, screens, icons} from '../../assets/strings';
import colors from '../../assets/colors';
import {styles} from './styles';
import Icon from '../../components/Icon';
import BottomSheet from '../../components/BottomSheet';
import Button from '../../components/Button';
import Layout from '../../layout';
import {useAuthenticationInfo} from '../../tools/hooks';
import locale from '../../assets/locale.json';
import {useInput} from '../../tools/hooks';
import Tooltip from '../../components/Tooltip';
import TextInput, {InvalidTextComponent} from '../../components/TextInput';
import InfoModal from '../../components/Modal/InfoModal';
import {normalizeNumberToNDecimalPlaces} from '../../tools/utils';
import {containsSymbol} from '../../tools/utils';

const mapInvalidity = function (authType) {
  switch (authType) {
    case 1:
    case 5:
      return [
        'name',
        'brand',
        'pit_to_pit_measurement',
        'collar_to_bottom_measurement',
        'stitching',
        'category',
        'decade',
        'condition',
      ];

    case 2:
    case 3:
      return [
        'name',
        'brand',
        'pit_to_pit_measurement',
        'collar_to_bottom_measurement',
        'stitching',
        'category',
        'decade',
        'condition',
        'size',
        'material',
        'condition_description',
        'provenance',
      ];
    case 4:
      return ['name', 'brand', 'stitching', 'category', 'decade'];
  }
};

const INFO_MODAL_INITIAL_STATE = {
  visible: false,
  id: null,
};

const ERROR_INITIAL_STATE = {
  state: false,
  text: '',
};

const FIELD_TYPE = Object.freeze({
  Category: 'Category',
  Decade: 'Decade',
  Condition: 'Condition',
  LabelSize: 'LabelSize',
  Material: 'Material',
  Brand: 'Brand',
  Stitching: 'Stitching',
  Name: 'Name',
});

const AddDescription = ({route, navigation}) => {
  const [
    {
      resubmissionData = null,
      isAuthenticationResubmission = false,
      authenticationId = null,
      isUpgradeToExpertAuthentication = false,
      upgradeData = null,
    },
  ] = React.useState(route?.params);

  const isResubmitting = React.useMemo(
    () => isAuthenticationResubmission && !!authenticationId,
    [isAuthenticationResubmission, authenticationId],
  );

  const [
    createAuthentication,
    {loading: createAuthenticationLoading},
  ] = useMutation(CREATE_AUTHENTICATION);

  const [
    resubmitRejectedAuthentication,
    {loading: resubmitRejectedAuthenticationLoading},
  ] = useMutation(RESUBMIT_REJECTED_AUTHENTICATION);

  const loading =
    createAuthenticationLoading || resubmitRejectedAuthenticationLoading;

    const [APICall, setAPICall] = React.useState(false);
  const [authType, setAuthType] = React.useState(1);
  const [error, setError] = React.useState(ERROR_INITIAL_STATE);
  const [invalidInput, setInvalidInput] = React.useState({
    rn_number: '',
  });

  const imageUploads = useSelector(state => state?.uploadMedia?.uploads);
  const [filterType, setFilterType] = useState(null);
  const [infoModal, setInfoModal] = useState(INFO_MODAL_INITIAL_STATE);
  const [sheetVisible, setSheetVisible] = useState(false);
  const [makePostPrivate, setMakePostPrivate] = React.useState(
    isResubmitting && !(resubmissionData?.post_status_type?.length > 0)
      ? resubmissionData?.post_status_type === 'PRIVATE'
      : isUpgradeToExpertAuthentication
      ? upgradeData?.postData?.post_status_type === 'PRIVATE'
      : false,
  );
  const [isBrandCustom, setIsBrandCustom] = React.useState(
    isResubmitting && !(resubmissionData?.brand?.id == null)
      ? resubmissionData?.is_custom_brand
      : isUpgradeToExpertAuthentication
      ? upgradeData?.postData?.is_custom_brand
      : false,
  );

  const [itemIds, setItemIds] = React.useState({
    brandId:
      isResubmitting && !(resubmissionData?.brand?.id == null)
        ? resubmissionData?.brand?.id
        : isUpgradeToExpertAuthentication
        ? upgradeData?.postData?.brand?.id
        : null,
    stitchingId:
      isResubmitting && !(resubmissionData?.stitching?.id == null)
        ? resubmissionData?.stitching?.id
        : isUpgradeToExpertAuthentication
        ? upgradeData?.postData?.stitching?.id
        : null,
    categoryId:
      isResubmitting && !(resubmissionData?.category?.id == null)
        ? resubmissionData?.category?.id
        : isUpgradeToExpertAuthentication
        ? upgradeData?.postData?.category?.id
        : null,
    decadeId:
      isResubmitting && !(resubmissionData?.from_decade?.id == null)
        ? resubmissionData?.from_decade?.id
        : isUpgradeToExpertAuthentication
        ? upgradeData?.postData?.from_decade?.id
        : null,
    sizeId:
      isResubmitting && !(resubmissionData?.size?.id == null)
        ? resubmissionData?.size?.id
        : isUpgradeToExpertAuthentication
        ? upgradeData?.postData?.size?.id
        : null,
    materialId:
      isResubmitting && !(resubmissionData?.material?.id == null)
        ? resubmissionData?.material?.id
        : isUpgradeToExpertAuthentication
        ? upgradeData?.postData?.material?.id
        : null,
    conditionId:
      isResubmitting && !(resubmissionData?.condition?.id == null)
        ? resubmissionData?.condition?.id
        : isUpgradeToExpertAuthentication
        ? upgradeData?.postData?.condition?.id
        : null,
  });
  const {values, invalid, checkIsEmpty, handleChange, setInvalid} = useInput(
    [
      'name',
      'brand',
      'pit_to_pit_measurement',
      'collar_to_bottom_measurement',
      'stitching',
      'category',
      'decade',
      'condition',
      'size',
      'material',
      'rn_number',
      'condition_description',
      'provenance',
    ],
    mapInvalidity(authType),
    isResubmitting || isUpgradeToExpertAuthentication
      ? mapInitialKeyValuePair(
          isResubmitting ? resubmissionData : upgradeData?.postData,
        )
      : {},
  );

  const {
    name: invalidName,
    brand: invalidBrand,
    pit_to_pit_measurement: invalidPitToPitMeasurement,
    collar_to_bottom_measurement: invalidCollarToBottomMeasurement,
    stitching: invalidStitching,
    category: invalidCategory,
    decade: invalidDecade,
    condition: invalidCondition,
    size: invalidSize,
    material: invalidMaterial,
    condition_description: invalidConditionDescription,
    provenance: invalidProvenance,
  } = invalid;

  const {
    name,
    brand,
    pit_to_pit_measurement,
    collar_to_bottom_measurement,
    stitching,
    category,
    decade,
    condition,
    size,
    material,
    condition_description,
    provenance,
  } = values;

  const handleSelection = useCallback(
    (value, type) => {
      switch (type) {
        case FIELD_TYPE.Category:
          handleChange('category', value?.name);
          setInvalid(previousState => ({...previousState, category: false}));
          setItemIds(previousIds => ({...previousIds, categoryId: value?.id}));
          break;
        case FIELD_TYPE.Decade:
          handleChange('decade', value?.name);
          setInvalid(previousState => ({...previousState, decade: false}));
          setItemIds(previousIds => ({...previousIds, decadeId: value?.id}));
          break;
        case FIELD_TYPE.Condition:
          handleChange('condition', value?.name);
          setInvalid(previousState => ({...previousState, condition: false}));
          setItemIds(previousIds => ({...previousIds, conditionId: value?.id}));
          break;
        case FIELD_TYPE.LabelSize:
          handleChange('size', value?.name);
          setInvalid(previousState => ({...previousState, size: false}));
          setItemIds(previousIds => ({...previousIds, sizeId: value?.id}));
          break;
        case FIELD_TYPE.Material:
          handleChange('material', value?.name);
          setInvalid(previousState => ({...previousState, material: false}));
          setItemIds(previousIds => ({...previousIds, materialId: value?.id}));
          break;
        default:
          break;
      }
      setSheetVisible(false);
    },
    [filterType, handleChange, setInvalid],
  );

  const handlePresentModalPress = value => {
    setFilterType(value);
    setSheetVisible(true);
  };

  const handleBrandSelection = React.useCallback(
    (brandValue, isCustomBrand) => {
      setIsBrandCustom(isCustomBrand);
      setInvalid(previousState => ({...previousState, brand: false}));
      handleChange('brand', brandValue?.name);
      setItemIds(previousState => ({
        ...previousState,
        brandId: brandValue?.id,
      }));
    },
    [handleChange],
  );

  React.useEffect(() => {
    if (route?.params?.authType) {
      setAuthType(route?.params?.authType);
    }
  }, [route?.params?.authType]);

  const toggleModal = value => {
    setInfoModal({
      visible: true,
      id: value,
    });
  };

  const togglePostStatus = () => [
    setMakePostPrivate(previousState => !previousState),
  ];

  const handleSubmit = React.useCallback(async () => {
    if (checkIsEmpty[1]) {
      setInvalid(previousState => ({...previousState, ...checkIsEmpty[0]}));
      return;
    }

    if (
      values?.rn_number?.length > 0 &&
      ![5, 6].includes(values?.rn_number?.length)
    ) {
      setInvalidInput(previousState => ({
        ...previousState,
        rn_number: locale.RNNumberMustBeWither5Or6DigitsLong,
      }));
      return;
    }

    if(APICall) {
      return
    }
    console.log("API Started----------->")
    setAPICall(true)
    
    const data = {
      title: values.name,
      post_status_type: makePostPrivate ? 'PRIVATE' : 'PUBLIC',
      ...(values?.rn_number?.toString?.()?.length > 0
        ? {rn_number: +values?.rn_number}
        : {}),
      authentication_type: authType,
      ...(isBrandCustom
        ? {custom_brand_id: +itemIds?.brandId}
        : {brand_id: +itemIds?.brandId}),
      ...(values?.pit_to_pit_measurement?.toString?.()?.length > 0
        ? {pit_to_pit_measurement: +pit_to_pit_measurement}
        : {}),
      ...(values?.collar_to_bottom_measurement?.toString?.()?.length > 0
        ? {collar_to_bottom_measurement: +collar_to_bottom_measurement}
        : {}),
      ...(itemIds.sizeId?.toString?.()?.length > 0
        ? {size_id: +itemIds.sizeId}
        : {}),
      ...(itemIds.materialId?.toString?.()?.length > 0
        ? {material_id: +itemIds.materialId}
        : {}),
      ...(itemIds.stitchingId?.toString?.()?.length > 0
        ? {stitching_id: +itemIds.stitchingId}
        : {}),

      ...(itemIds.categoryId?.toString?.()?.length > 0
        ? {category_id: +itemIds.categoryId}
        : {}),
      ...(itemIds.conditionId?.toString?.()?.length > 0
        ? {condition_id: +itemIds.conditionId}
        : {}),
      ...(itemIds.decadeId?.toString?.()?.length > 0
        ? {from_decade: +itemIds.decadeId}
        : {}),
      ...(values?.condition_description?.toString?.()?.length > 0
        ? {condition_description: condition_description}
        : {}),
      ...(values?.provenance?.toString?.()?.length > 0
        ? {provenance: provenance}
        : {}),

      front_image: imageUploads.front_image,
      back_image: imageUploads.back_image,
      front_tag_image: imageUploads.front_tag_image,
      back_tag_image: imageUploads.back_tag_image,
      closeup_front_image: imageUploads.closeup_front_image,
      closeup_back_image: imageUploads.closeup_back_image,
      copyright_image: imageUploads.copyright_image,
      arm_hem_stitching_image: imageUploads.arm_hem_stitching_image,
      lower_hem_stitching_image: imageUploads.lower_hem_stitching_image,
      extra_image: imageUploads.extra,
      front_image_upload_method: imageUploads.front_image_upload_method
        ? parseInt(imageUploads.front_image_upload_method, 10)
        : null,
      back_image_upload_method: imageUploads.back_image_upload_method
        ? parseInt(imageUploads.back_image_upload_method, 10)
        : null,
      front_tag_image_upload_method: imageUploads.front_tag_image_upload_method
        ? parseInt(imageUploads.front_tag_image_upload_method, 10)
        : null,
      back_tag_image_upload_method: imageUploads.back_tag_image_upload_method
        ? parseInt(imageUploads.back_tag_image_upload_method, 10)
        : null,
      closeup_front_image_upload_method: imageUploads.closeup_front_image_upload_method
        ? parseInt(imageUploads.closeup_front_image_upload_method, 10)
        : null,
      closeup_back_image_upload_method: imageUploads.closeup_back_image_upload_method
        ? parseInt(imageUploads.closeup_back_image_upload_method, 10)
        : null,
      copyright_image_upload_method: imageUploads.copyright_image_upload_method
        ? parseInt(imageUploads.copyright_image_upload_method, 10)
        : null,
      arm_hem_stitching_image_upload_method: imageUploads.arm_hem_stitching_image_upload_method
        ? parseInt(imageUploads.arm_hem_stitching_image_upload_method, 10)
        : null,
      lower_hem_stitching_image_upload_method: imageUploads.lower_hem_stitching_image_upload_method
        ? parseInt(imageUploads.lower_hem_stitching_image_upload_method, 10)
        : null,
      extra_image_upload_method: imageUploads.extra_upload_method
        ? parseInt(imageUploads.extra_upload_method, 10)
        : null,
    };

    try {
      const response = await (isResubmitting
        ? resubmitRejectedAuthentication({
            variables: {
              authenticationId,
              input: data,
            },
          })
        : createAuthentication({
            variables: {
              input: data,
            },
          }));

      const isSuccessful =
        response?.data?.[
          isResubmitting
            ? 'resubmitRejectedAuthentication'
            : 'createAuthentication'
        ]?.success;

      if (isSuccessful) {
        if (isResubmitting) {
          navigation.navigate(screens.AUTH_SUCCESSFUL, {
            otherParams: authType,
            params: isResubmitting
              ? resubmissionData?.authentication_key
              : response?.data?.createAuthentication?.data?.authenticationKey,
            isResubmission: isResubmitting,
            authenticationType: authType,
          });
          return;
        }
        if (authType == 2 || authType == 3 || authType == 5) {
          navigation.navigate(screens.PAYMENT_METHOD, {
            otherParams: authType,
            authenticationId: response?.data?.createAuthentication?.data?.id,
            authenticationType: authType,
          });
        } else {
          navigation.navigate(screens.AUTH_SUCCESSFUL, {
            otherParams: authType,
            params:
              response?.data?.createAuthentication?.data?.authenticationKey,
            authenticationType: authType,
          });
        }
      } else {
        setAPICall(false)
        throw new Error();
      }
    } catch (e) {
      setAPICall(false)
      setError({state: true, text: locale.SomethingWentWrongPleaseTryAgain});
    }
  }, [
    authType,
    navigation,
    checkIsEmpty,
    itemIds,
    values,
    imageUploads,
    createAuthentication,
    resubmitRejectedAuthentication,
    isResubmitting,
    authenticationId,
    resubmissionData?.authentication_key,
    makePostPrivate,
  ]);

  const mapSelected = React.useMemo(() => {
    switch (filterType) {
      case FIELD_TYPE.Condition:
        return itemIds?.conditionId;
      case FIELD_TYPE.Decade:
        return itemIds?.decadeId;
      case FIELD_TYPE.LabelSize:
        return itemIds?.sizeId;
      case FIELD_TYPE.Material:
        return itemIds?.materialId;
      default:
        return null;
    }
  }, [filterType, itemIds]);

  const handleInputChange = React.useCallback(
    (type, value) => {
      if (invalid[type]) {
        setInvalid(preState => ({...preState, [type]: false}));
      }
      handleChange(type, value);
    },
    [invalid, setInvalid, handleChange],
  );

  const renderNameInput = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <View style={styles.conditionField}>
          <Text style={styles.inputLabel}>
            <Text style={{color: colors.warning}}>
              {addDescriptionScreen.ASTERISK}
            </Text>{' '}
            {addDescriptionScreen.NAME_OF_SHIRT}
          </Text>
          <Tooltip
            onPress={() => toggleModal('name')}
            containerStyle={{margin: 5}}
          />
        </View>
        <TextInput
          value={name}
          onChange={value => handleInputChange('name', value)}
          isInvalid={invalidName}
          invalidText={locale.NameIsRequired}
          maxLength={70}
        />
      </View>
    ),
    [name, handleInputChange, invalidName, toggleModal],
  );

  const renderBrand = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <Text style={styles.inputLabel}>
          <Text style={{color: colors.warning}}>
            {addDescriptionScreen.ASTERISK}{' '}
          </Text>
          {addDescriptionScreen.BRAND}
        </Text>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(screens.BRAND_PICKER, {
              checkNFTStatus: authType == 3,
              onBrandSelected: handleBrandSelection,
            })
          }
          style={[
            styles.inputField,
            {
              borderColor: invalidBrand ? colors.warning : colors.grey,
              borderWidth: 1,
              justifyContent: 'center',
              height: 50,
              borderRadius: 2,
            },
          ]}>
          {brand ? (
            <Text style={{color: colors.black}}>{brand}</Text>
          ) : (
            <Text style={{color: colors.darkGrey}}>{locale.Search}</Text>
          )}
        </TouchableOpacity>
        {invalidBrand ? (
          <InvalidTextComponent text={locale.BrandIsRequired} />
        ) : null}
      </View>
    ),
    [navigation, invalidBrand, brand, authType, handleBrandSelection],
  );

  const renderPitToPitMeasurementInput = React.useMemo(
    () =>
      authType !== 4 ? (
        <View style={styles.inputCard}>
          <Text style={[styles.inputLabel, {textTransform: 'none'}]}>
            <Text style={{color: colors.warning}}>
              {addDescriptionScreen.ASTERISK}{' '}
            </Text>
            {addDescriptionScreen.PIT_TO_PIT}
          </Text>
          <TextInput
            value={pit_to_pit_measurement}
            onChange={val => {
              if (
                !/\s/.test(val) &&
                !containsSymbol(val?.trim?.(), {allowedSymbols: ['.']}) &&
                !isNaN(val)
              ) {
                handleInputChange(
                  'pit_to_pit_measurement',
                  normalizeNumberToNDecimalPlaces(val),
                );
              }
            }}
            keyboardType="numeric"
            isInvalid={invalidPitToPitMeasurement}
            invalidText={locale.PitToPitMeasurementIsRequired}
            maxLength={5}
          />
        </View>
      ) : null,
    [
      authType,
      handleInputChange,
      pit_to_pit_measurement,
      invalidPitToPitMeasurement,
    ],
  );

  const renderCollarToBottomMeasurement = React.useMemo(
    () =>
      authType !== 4 ? (
        <View style={styles.inputCard}>
          <Text style={[styles.inputLabel, {textTransform: 'none'}]}>
            <Text style={{color: colors.warning}}>
              {addDescriptionScreen.ASTERISK}{' '}
            </Text>
            {addDescriptionScreen.REAR_TO_BOTTOM}
          </Text>
          <TextInput
            value={collar_to_bottom_measurement}
            onChange={val => {
              if (
                !/\s/.test(val) &&
                !containsSymbol(val?.trim?.(), {allowedSymbols: ['.']}) &&
                !isNaN(val)
              ) {
                handleInputChange(
                  'collar_to_bottom_measurement',
                  normalizeNumberToNDecimalPlaces(val),
                );
              }
            }}
            keyboardType="numeric"
            isInvalid={invalidCollarToBottomMeasurement}
            invalidText={locale.RealCollarToBottomMeasurementIsRequired}
            maxLength={5}
          />
        </View>
      ) : null,
    [
      authType,
      collar_to_bottom_measurement,
      invalidCollarToBottomMeasurement,
      handleInputChange,
    ],
  );

  const renderStitching = React.useMemo(
    () => (
      <StitchingField
        value={itemIds?.stitchingId ?? null}
        onItemPressed={item => {
          handleInputChange('stitching', item?.toString?.());
          setItemIds(previousState => ({
            ...previousState,
            stitchingId: item,
          }));
        }}
        isInvalid={invalidStitching}
      />
    ),
    [stitching, handleInputChange, invalidStitching, itemIds?.stitchingId],
  );

  const renderCategory = React.useMemo(
    () => (
      <CategoryField
        handleSelection={item => {
          handleInputChange('category', item?.name);
          setItemIds(previousState => ({
            ...previousState,
            categoryId: item?.id,
          }));
        }}
        selected={
          category?.length
            ? {
                name: category,
                id: itemIds?.categoryId,
              }
            : null
        }
        isInvalid={invalidCategory}
      />
    ),
    [category, invalidCategory, handleInputChange, itemIds?.categoryId],
  );

  const renderDecade = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <Text style={styles.inputLabel}>
          <Text style={{color: colors.warning}}>
            {addDescriptionScreen.ASTERISK}{' '}
          </Text>
          {addDescriptionScreen.DECADE}
        </Text>
        <DropdownSelectorInput
          onPress={() => {
            handlePresentModalPress(FIELD_TYPE.Decade);
          }}
          value={decade ?? null}
          isInvalid={invalidDecade}
        />
        {invalidDecade ? (
          <InvalidTextComponent text={locale.DecadeIsRequired} />
        ) : null}
      </View>
    ),
    [invalidDecade, handlePresentModalPress, decade],
  );

  const renderCondition = React.useMemo(
    () =>
      authType !== 4 ? (
        <View style={styles.inputCard}>
          <View style={styles.conditionField}>
            <Text style={[styles.inputLabel, styles.conditionFieldLabel]}>
              <Text style={{color: colors.warning}}>
                {addDescriptionScreen.ASTERISK}{' '}
              </Text>
              {addDescriptionScreen.CONDITION}
            </Text>
            <Tooltip
              onPress={() => toggleModal('condition')}
              containerStyle={{margin: 5}}
            />
          </View>
          <DropdownSelectorInput
            onPress={() => handlePresentModalPress(FIELD_TYPE.Condition)}
            value={condition ?? null}
            isInvalid={invalidCondition}
          />
          {invalidCondition ? (
            <InvalidTextComponent text={locale.ConditionIsRequired} />
          ) : null}
        </View>
      ) : null,
    [
      authType,
      condition,
      handlePresentModalPress,
      invalidCondition,
      toggleModal,
    ],
  );

  const renderSize = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <Text style={styles.inputLabel}>
          {authType === 2 || authType === 3 ? (
            <Text style={{color: colors.warning}}>
              {addDescriptionScreen.ASTERISK}{' '}
            </Text>
          ) : null}
          {addDescriptionScreen.LABEL_SIZE}
          <Text style={[{color: colors.darkGrey}, {textTransform: 'none'}]}>
            {authType === 1 || authType === 4
              ? addDescriptionScreen.OPTIONAL
              : null}
          </Text>
        </Text>
        <DropdownSelectorInput
          onPress={() => handlePresentModalPress(FIELD_TYPE.LabelSize)}
          value={size}
          isInvalid={invalidSize}
        />
        {invalidSize ? (
          <InvalidTextComponent text={locale.LabelSizeIsRequired} />
        ) : null}
      </View>
    ),
    [authType, size, handlePresentModalPress, invalidSize],
  );

  const renderMaterial = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <Text style={styles.inputLabel}>
          {authType === 2 || authType === 3 ? (
            <Text style={{color: colors.warning}}>
              {addDescriptionScreen.ASTERISK}{' '}
            </Text>
          ) : null}
          {addDescriptionScreen.MATERIAL}
          <Text style={[{color: colors.darkGrey}, {textTransform: 'none'}]}>
            {authType === 1 || authType === 4
              ? addDescriptionScreen.OPTIONAL
              : null}
          </Text>
        </Text>
        <DropdownSelectorInput
          onPress={() => handlePresentModalPress(FIELD_TYPE.Material)}
          value={material ?? null}
          isInvalid={invalidMaterial}
        />
        {invalidMaterial ? (
          <InvalidTextComponent text={locale.MaterialIsRequired} />
        ) : null}
      </View>
    ),
    [handlePresentModalPress, material, invalidMaterial, authType],
  );

  const renderConditionDescription = React.useMemo(
    () =>
      authType !== 4 ? (
        <View style={styles.inputCard}>
          <View style={styles.conditionField}>
            <Text style={[styles.inputLabel, styles.conditionFieldLabel]}>
              {authType === 2 || authType === 3 ? (
                <Text style={{color: colors.warning}}>
                  {addDescriptionScreen.ASTERISK}{' '}
                </Text>
              ) : null}
              {addDescriptionScreen.CONDITION_DESCRIPTION}
              <Text style={[{color: colors.darkGrey}, {textTransform: 'none'}]}>
                {authType === 1 || authType === 4
                  ? addDescriptionScreen.OPTIONAL
                  : null}
              </Text>
            </Text>
            <Tooltip
              onPress={() => toggleModal('condition_description')}
              containerStyle={{margin: 5}}
            />
          </View>
          <TextInput
            value={condition_description}
            onChange={val => handleInputChange('condition_description', val)}
            isInvalid={invalidConditionDescription}
            invalidText={locale.ConditionDescriptionIsRequired}
            style={{minHeight: 100, textAlignVertical: 'top'}}
            multiline
            numberOfLines={4}
          />
        </View>
      ) : null,
    [
      authType,
      invalidConditionDescription,
      handleInputChange,
      toggleModal,
      condition_description,
    ],
  );
  const renderProvenance = React.useMemo(
    () =>
      authType !== 4 ? (
        <View style={styles.inputCard}>
          <View style={styles.conditionField}>
            <Text style={styles.inputLabel}>
              {authType === 2 || authType === 3 ? (
                <Text style={{color: colors.warning}}>
                  {addDescriptionScreen.ASTERISK}{' '}
                </Text>
              ) : null}
              {addDescriptionScreen.PROVENANCE}
              <Text style={[{color: colors.darkGrey}, {textTransform: 'none'}]}>
                {authType === 1 || authType === 4
                  ? addDescriptionScreen.OPTIONAL
                  : null}
              </Text>
            </Text>
            <Tooltip
              onPress={() => toggleModal('provenance')}
              containerStyle={{margin: 5}}
            />
          </View>
          <TextInput
            value={provenance}
            onChange={val => handleInputChange('provenance', val)}
            isInvalid={invalidProvenance}
            invalidText={locale.ProvenanceIsRequired}
            style={{minHeight: 100, textAlignVertical: 'top'}}
            multiline
            numberOfLines={4}
          />
        </View>
      ) : null,
    [toggleModal, provenance, invalidProvenance, handleInputChange],
  );

  const renderRNNumber = React.useMemo(
    () => (
      <View style={styles.inputCard}>
        <View style={styles.conditionField}>
          <Text style={[styles.inputLabel, {textTransform: 'none'}]}>
            {addDescriptionScreen.RN_NUMBER}
            <Text style={{color: colors.darkGrey}}>
              {addDescriptionScreen.OPTIONAL}
            </Text>
          </Text>
          <Tooltip
            onPress={() => toggleModal('rn_number')}
            containerStyle={{margin: 5}}
          />
        </View>
        <TextInput
          value={values?.rn_number}
          onChange={val => {
            if (val?.toString?.()?.length <= 6) {
              if (invalidInput.rn_number?.length !== 0) {
                setInvalidInput(previousState => ({
                  ...previousState,
                  rn_number: '',
                }));
              }

              handleInputChange('rn_number', val);
            }
          }}
          keyboardType="number-pad"
          maxLength={6}
          isInvalid={invalidInput.rn_number?.length > 0}
          invalidText={invalidInput.rn_number}
        />
      </View>
    ),
    [values?.rn_number, handleInputChange, toggleModal],
  );

  const canShowPrivateToggle = React.useMemo(
    () => authType == 2 || authType == 3,
    [authType],
  );
  return (
    <>
      <Layout>
        <View style={styles.container}>
          <View style={styles.loginContainer}>
            {canShowPrivateToggle ? (
              <View
                style={{
                  ...styles.inputCard,
                  flex: 1,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 0,
                }}>
                <Text
                  style={[
                    styles.inputLabel,
                    {textTransform: 'none', marginBottom: 0, maxWidth: '80%'},
                  ]}>
                  {locale.DoYouWantTheAuthenticationToBePrivate}
                </Text>
                <Switch
                  trackColor={{false: colors.grey, true: colors.green}}
                  thumbColor={colors.lightGrey}
                  ios_backgroundColor={colors.grey}
                  onValueChange={togglePostStatus}
                  value={makePostPrivate}
                />
              </View>
            ) : null}
            {renderNameInput}
            {renderBrand}
            {renderPitToPitMeasurementInput}
            {renderCollarToBottomMeasurement}
            {renderStitching}
            {renderCategory}
            {renderDecade}
            {renderCondition}
            {renderSize}
            {renderMaterial}
            {renderConditionDescription}
            {renderProvenance}
            {renderRNNumber}

            {Object.values(invalid)?.some(item => item) ? (
              <View style={styles.errorWrapper}>
                <Text style={styles.errorText}>
                  {locale.MakeSureToFillAllTheRequiredFields}
                </Text>
              </View>
            ) : null}

            <View style={{width: '100%'}}>
              <Button
                text={addDescriptionScreen.BUTTON_LABEL}
                textStyle={styles.buttonText}
                style={styles.submitButton}
                onPress={handleSubmit}
                isLoading={loading}
                disabled={loading}
              />
            </View>
          </View>
        </View>
      </Layout>
      <TooltipSheet
        visible={infoModal.visible}
        setVisible={() => setInfoModal(INFO_MODAL_INITIAL_STATE)}
        _key={infoModal.id}
      />
      <ListSheet
        visible={sheetVisible}
        setVisible={setSheetVisible}
        _key={filterType}
        handleSelection={handleSelection}
        selected={mapSelected}
      />
      <InfoModal
        setVisible={() => setError(ERROR_INITIAL_STATE)}
        popUp={{
          state: error.state ?? false,
          data: {title: locale.Error, description: error.text},
          isError: true,
        }}
      />
    </>
  );
};

function ListSheet({
  visible = false,
  setVisible = () => null,
  _key = null,
  selected = null,
  handleSelection = () => null,
}) {
  const isFocused = useIsFocused();

  const {data} = useAuthenticationInfo({isFocused});

  const list = React.useMemo(
    () =>
      ({
        [FIELD_TYPE.LabelSize]: data?.sizes,
        [FIELD_TYPE.Material]: data?.materials,
        [FIELD_TYPE.Condition]: data?.conditions,
        [FIELD_TYPE.Decade]: data?.from_decades,
      }?.[_key] ?? []),
    [data, _key],
  );

  return (
    <BottomSheet
      visible={visible}
      setVisible={setVisible}
      automaticallyAdjustHeight
      key={_key}>
      <FlatList
        contentContainerStyle={{paddingBottom: 20}}
        data={list}
        keyExtractor={item => item?.id?.toString()}
        showsVerticalScrollIndicator={false}
        renderItem={({item}) => (
          <TouchableOpacity
            onPress={() => handleSelection(item, _key)}
            key={item?.id}
            style={styles.buttonStyles}>
            <Text style={{color: colors.black}}>{item?.name}</Text>

            <Icon
              name={
                selected == item?.id
                  ? icons.CHECKED_RADIO
                  : icons.UNCHECKED_RADIO
              }
              tint={selected == item?.id ? colors.primary : colors.darkGrey}
              size={18}
            />
          </TouchableOpacity>
        )}
        initialNumToRender={10}
      />
    </BottomSheet>
  );
}

function TooltipSheet({visible = false, setVisible = () => null, _key = null}) {
  const textContent = React.useMemo(() => {
    switch (_key) {
      case 'name':
        return {
          title: 'Name of shirt',
          description: 'Eg: Vintage 1980s Run DMC Adidas T-Shirt',
        };
      case 'condition':
        return {
          title: 'Condition',
          description: `Mint Deadstock
Mint is perfect. It has never been worn or washed and there are no issues with the condition. The tag and print are pristine, crisp, and clean. The item may have been purchased and stored in a dresser for years and never washed or worn or it might be deadstock that remained perfect during storage.

Deadstock
Deadstock items have never been worn or washed, but it may still have some condition issues from storage.

Excellent
Excellent usually describes a t-shirt that’s been worn and washed but has no issues, aside from the expected ones like fading. No stains, holes, rips, tears, or seam issues. 

Great
Great usually means there’s a more serious issue – or a few minor issues. Perhaps there’s a tiny hole, or a small underarm seam, or maybe multiple small marks.

Good
Good indicates the item is in good condition but the issues are adding up. It’s more worn, with a few significant marks and a few small holes.

Poor
Poor describes the point in which it becomes a turn-off to certain wearers. Issues are plentiful.

Distressed
This item is a mess. But that doesn’t mean it won’t work for certain buyers. Distressed means large holes, massive seam issues, and/or a lot of staining. 
            `,
        };
      case 'condition_description':
        return {
          title: 'Condition description',
          description:
            'Enter details about the condition of the item to help give the community a better idea when they are appraising its value, ie: small stains on the lower front, seam separation on rear collar, hole under arm.',
        };
      case 'provenance':
        return {
          title: 'Provenance',
          description:
            'Enter details as to where the shirt came from, ie: I bought it from, or am considering buying it from [seller id] on [marketplace], thrift store, I’m the original owner, I purchased it at a concert, I obtained this from the family member of a particular celebrity or other notable person. Wherever possible you should also include links to the original listing or additional proof.',
        };
      case 'rn_number':
        return {
          title: 'RN Number',
          description:
            'Enter the 5 or 6 digit RN number that may appear on the front or back of a t-shirt tag.',
        };
      default:
        return {
          title: '',
          description: '',
        };
    }
  }, [_key]);

  return (
    <BottomSheet
      visible={visible}
      setVisible={setVisible}
      automaticallyAdjustHeight={_key !== 'condition'}
      key={_key}>
      <View style={styles.visibleSection}>
        <Text style={styles.modalTitle}>{textContent.title}</Text>
        {React.cloneElement(_key !== 'condition' ? <View /> : <ScrollView />, {
          children: <Text style={styles.modal}>{textContent.description}</Text>,
        })}
      </View>
    </BottomSheet>
  );
}

function StitchingField({
  onItemPressed = () => null,
  value = null,
  isInvalid = false,
}) {
  const {data} = useQuery(GET_AUTHENTICATION_INFO);

  const list = React.useMemo(
    () => data?.getAuthenticationInfo?.data?.stitches ?? [],
    [data?.getAuthenticationInfo?.data?.stitches],
  );

  return (
    <View style={styles.inputCard}>
      <Text style={styles.inputLabel}>
        <Text style={{color: colors.warning}}>
          {addDescriptionScreen.ASTERISK}{' '}
        </Text>
        {addDescriptionScreen.STITCHING}
      </Text>
      <View style={styles.radioContainer}>
        {list?.map(item => (
          <TouchableOpacity
            key={item?.id}
            style={styles.radioWrapper}
            onPress={() => onItemPressed(item?.id)}>
            <View
              style={[
                styles.radio,
                value === item?.id
                  ? {borderColor: colors.primary, borderWidth: 4}
                  : {
                      borderColor: colors.darkGrey,
                      borderWidth: 2,
                    },
              ]}
            />
            <Text style={styles.radioLabel}>{item?.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
      {isInvalid ? (
        <InvalidTextComponent
          style={{textAlign: 'left'}}
          text={locale.StitchingIsRequired}
        />
      ) : null}
    </View>
  );
}

function CategoryField({
  handleSelection = () => null,
  selected = null,
  isInvalid = false,
}) {
  const [visible, setVisible] = React.useState(false);

  const {data, refetch} = useQuery(GET_CATEGORIES);

  const handleItemSelection = React.useCallback(
    item => {
      handleSelection(item, FIELD_TYPE.Category);
      setVisible(false);
    },
    [handleSelection],
  );

  useFocusEffect(
    React.useCallback(() => {
      try {
        refetch?.();
      } catch (error) {}
    }, [refetch]),
  );

  return (
    <View style={styles.inputCard}>
      <Text style={styles.inputLabel}>
        <Text style={{color: colors.warning}}>
          {addDescriptionScreen.ASTERISK}{' '}
        </Text>
        {addDescriptionScreen.CATEGORY}
      </Text>
      <DropdownSelectorInput
        onPress={() => setVisible(true)}
        value={selected?.name}
        isInvalid={isInvalid}
      />
      {isInvalid ? (
        <InvalidTextComponent text={locale.CategoryIsRequired} />
      ) : null}
      <BottomSheet
        visible={visible}
        setVisible={setVisible}
        snapPoints={['40%', '80%']}
        index={1}>
        <BottomSheetFlatList
          contentContainerStyle={{paddingBottom: 20}}
          data={data?.getCategories?.data}
          keyExtractor={item => item?.id?.toString()}
          showsVerticalScrollIndicator={false}
          renderItem={({item}) => (
            <TouchableOpacity
              onPress={handleItemSelection.bind(null, item)}
              key={item?.id}
              style={styles.buttonStyles}>
              <Text style={{color: colors.black}}>{item?.name}</Text>
              <Icon
                name={
                  selected?.id == item?.id
                    ? icons.CHECKED_RADIO
                    : icons.UNCHECKED_RADIO
                }
                tint={
                  selected?.id == item?.id ? colors.primary : colors.darkGrey
                }
                size={18}
              />
            </TouchableOpacity>
          )}
          initialNumToRender={10}
        />
      </BottomSheet>
    </View>
  );
}

function DropdownSelectorInput({
  value = '',
  onPress = () => null,
  isInvalid = false,
}) {
  return (
    <TouchableOpacity
      style={[
        styles.pickerButtonStyles,
        {borderColor: isInvalid ? colors.warning : colors.grey},
      ]}
      onPress={onPress}>
      <Text
        style={{
          color: value ? colors.black : colors.darkGrey,
        }}>
        {value || '- select -'}
      </Text>
      <Icon name={icons.CHEVRON_DOWN} tint={colors.black} size={10} />
    </TouchableOpacity>
  );
}

function mapInitialKeyValuePair(data = null) {
  return {
    name: data?.title ?? '',
    brand: data?.brand?.name ?? '',
    pit_to_pit_measurement: data?.pit_to_pit_measurement?.toString?.()?.length
      ? `${data?.pit_to_pit_measurement}`
      : '',
    collar_to_bottom_measurement: data?.collar_to_bottom_measurement?.toString?.()
      ?.length
      ? `${data?.collar_to_bottom_measurement}`
      : '',
    stitching: data?.stitching?.name ?? '',
    category: data?.category?.name ?? '',
    decade: data?.from_decade?.name ?? '',
    condition: data?.condition?.name ?? '',
    size: data?.size?.name ?? '',
    material: data?.material?.name ?? '',
    rn_number: data?.rn_number?.toString?.()?.length
      ? `${data?.rn_number}`
      : '',
    condition_description: data?.condition_description ?? '',
    provenance: data?.provenance ?? '',
  };
}

export default AddDescription;
