import React, {useState, useEffect, useCallback} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Pressable,
  TextInput,
  Platform,
} from 'react-native';
import {useMutation} from '@apollo/client';
import {CardField, useStripe} from '@stripe/stripe-react-native';
import Animated, {FadeInUp} from 'react-native-reanimated';

import {CREATE_CARD} from '../../apollo/mutations';
import {GET_ALL_CARDS} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import {
  authenticationTypeScreen,
  screens,
  editCreditCardScreen,
  icons,
} from '../../assets/strings';
import Icon from '../../components/Icon';
import locale from '../../assets/locale.json';
import Layout from '../../layout';
import InfoModal from '../../components/Modal/InfoModal';
import Button from '../../components/Button';

const POPUP_INITIAL_STATE = {
  state: false,
  isError: true,
  data: {
    title: '',
    description: '',
  },
};

const CARD_ERRORS = {
  CARD_DECLINED: locale.CardDeclined,
};

const isIOS = Platform.OS === 'ios';

const AuthenticationAddCard = ({route, navigation}) => {
  const [authenticationId] = React.useState(route?.params?.params);
  const [intent] = React.useState(route?.params?.intent);
  const [cardDetails, setCardDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState('');
  const [authId, setAuthId] = useState(null);
  const [defaultCard, setDefaultCard] = useState(false);
  const [saveCard, setSaveCard] = useState(false);
  const [getNFT] = React.useState(route?.params?.getNFTDetails);

  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);
  const [canSubmit, setCanSubmit] = React.useState(false);

  const authenticationType = React.useMemo(
    () => route?.params?.authenticationType,
    [route?.params?.authenticationType],
  );

  const {createToken} = useStripe();

  const [createCard] = useMutation(CREATE_CARD, {
    refetchQueries: [{query: GET_ALL_CARDS}],
  });

  useEffect(() => {
    if (authenticationId) {
      setAuthId(authenticationId);
    }
  }, [authenticationId, navigation]);

  const handleStripeToken = async () => {
    if (saveCard) {
      setIsLoading(true);
      try {
        const {token, error} = await createToken({
          type: 'Card',
          name,
        });
        if (error) {
          throw new Error();
        }

        const {data} = await createCard({
          variables: {
            token: token.id,
            setAsDefault: defaultCard,
          },
        });
        const isSuccess = data?.createCard?.success;
        const code = data?.createCard?.code;
        const message = data?.createCard?.message;
        if (isSuccess) {
          setName('');
          setIsLoading(false);
          setCardDetails(null);
          navigation.navigate(screens.PAYMENT_METHOD, {
            cardAdded: true,
            authenticationType,
          });
        } else {
          if (code?.length) {
            setPopup(previousState => ({
              ...previousState,
              state: true,
              isError: true,
              data: {
                title: locale.Error,
                description: CARD_ERRORS[code] || message,
              },
            }));
          } else {
            throw new Error();
          }
        }
      } catch (e) {
        setPopup(previousState => ({
          ...previousState,
          state: true,
          isError: true,
          data: {
            title: locale.Error,
            description: locale.SomethingWentWrongPleaseTryAgain,
          },
        }));
      } finally {
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
      navigation.navigate(screens.CHECKOUT, {
        otherParams: cardDetails,
        params: intent,
        authId: authId,
        name: name,
        expressCheckout: true,
        getNFTDetails: getNFT,
        authenticationType,
      });
    }
  };

  const defaultCardCheckBox = useCallback(() => {
    setDefaultCard(prev => !prev);
  }, []);

  const saveCardCheckBox = useCallback(() => {
    setSaveCard(!saveCard);
  }, [saveCard]);

  const handleCardChange = cardDetails => {
    setCanSubmit(cardDetails?.complete);
    setCardDetails(cardDetails);
  };

  return (
    <Layout style={styles.container}>
      <View style={styles.titleWrapper}>
        <Text style={styles.title}>
          {authenticationTypeScreen.ADD_NEW_CARD}
        </Text>
      </View>
      <View style={styles.modalWrapper}>
        <Text style={styles.label}>
          {authenticationTypeScreen.NAME_ON_CARD}
        </Text>
        <TextInput
          style={styles.fieldInput}
          value={name}
          onChangeText={setName}
          placeholder="Name"
          placeholderTextColor={colors.grey}
          selectionColor={colors.grey}
        />
        <Text style={styles.label}>
          {authenticationTypeScreen.CARD_DETAILS}
        </Text>
        <CardField
          postalCodeEnabled={false}
          placeholder={{
            number: authenticationTypeScreen.CARD_NUMBER_PLACEHOLDER,
          }}
          placeholderTextColor={colors.grey}
          cardStyle={{
            ...styles.cardStyle,
            textColor: colors.black,
            borderRadius: 5,
            fontSize: 14,
          }}
          autofocus={false}
          style={styles.cardFieldStyle}
          color={colors.black}
          onCardChange={handleCardChange}
        />

        <View style={styles.checkBoxWrapper}>
          <Pressable
            style={[
              styles.checkBox,
              {flexDirection: 'row', alignItems: 'center'},
            ]}
            onPress={saveCardCheckBox}>
            {saveCard ? (
              <Icon
                name={icons.CHECKED_RADIO}
                size={fontSize.xxxl}
                tint={colors.primary}
              />
            ) : (
              <Icon
                name={icons.UNCHECKED_RADIO}
                size={fontSize.xxxl}
                tint={colors.grey}
              />
            )}
            <Text style={[styles.defaultCardLabel, {marginLeft: 5}]}>
              {editCreditCardScreen.SAVE_CARD}
            </Text>
          </Pressable>
        </View>
        {saveCard ? (
          <Animated.View
            style={styles.checkBoxWrapper}
            entering={FadeInUp.springify().damping(200).stiffness(450)}>
            <Pressable
              style={[
                styles.checkBox,
                {flexDirection: 'row', alignItems: 'center'},
              ]}
              onPress={defaultCardCheckBox}>
              {defaultCard ? (
                <Icon
                  name={icons.CHECKED_RADIO}
                  size={fontSize.xxxl}
                  tint={colors.primary}
                />
              ) : (
                <Icon
                  name={icons.UNCHECKED_RADIO}
                  size={fontSize.xxxl}
                  tint={colors.grey}
                />
              )}
              <Text style={[styles.defaultCardLabel, {marginLeft: 5}]}>
                {editCreditCardScreen.SET_DEFAULT}
              </Text>
            </Pressable>
          </Animated.View>
        ) : null}
        <Button
          text={authenticationTypeScreen.ADD_CARD}
          isLoading={isLoading}
          disabled={isLoading || !canSubmit}
          onPress={handleStripeToken}
          style={{height: 50, borderRadius: 5, marginTop: 20}}
        />
      </View>
      <InfoModal
        setVisible={() => {
          setPopup(previousState => ({
            ...previousState,
            state: false,
          }));
        }}
        popUp={popup}
      />
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacings.lg,
    backgroundColor: colors.white,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  emptyContainerText: {
    color: colors.black,
    fontSize: fontSize.md,
  },

  cardTitleWrapper: {
    flexDirection: 'row',
  },
  cardTitle: {
    fontSize: fontSize.lg,
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
  defaultCardLabel: {
    color: colors.black,
    fontSize: fontSize.lg,
    fontFamily: 'Inter-Regular',
  },
  defaultLabel: {
    color: colors.white,
    backgroundColor: colors.black,
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    fontSize: fontSize.sm,
    borderRadius: spacings.sm,
    marginLeft: spacings.md,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  leftSection: {
    alignItems: 'flex-start',
  },
  button: {
    marginTop: spacings.md,
    paddingVertical: spacings.sm,
  },
  buttonText: {
    fontSize: fontSize.md,
    color: colors.primary,
  },
  date: {
    fontSize: fontSize.md,
  },
  addCardButton: {
    height: 50,
    marginTop: spacings.lg,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.grey,
    marginBottom: spacings.xxl,
    borderRadius: 4,
  },
  addCardButtonText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  cardFieldStyle: {
    width: '100%',
    height: 50,
    marginBottom: 30,
    borderRadius: 4,
    color: colors.black,
  },
  cardStyle: {
    borderWidth: 1,
    color: colors.black,
    borderColor: colors.grey,
  },
  fieldInput: {
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 4,
    marginBottom: 30,
    paddingLeft: spacings.md,
    color: colors.black,
    ...(isIOS ? {padding: 15} : {}),
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.black,
    marginBottom: spacings.sm,
    fontFamily: 'Inter-Medium',
  },
  titleWrapper: {
    marginVertical: spacings.lg,
  },
  title: {
    fontSize: fontSize.xl,
    color: colors.black,
    marginBottom: spacings.sm,
    fontFamily: 'Gugi-Regular',
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  checkBox: {
    marginRight: spacings.sm,
    padding: spacings.sm,
  },
  checkBoxWrapper: {
    flexDirection: 'row',
    marginBottom: spacings.md,
    alignItems: 'center',
  },
  errorStyles: {
    color: colors.warning,
    fontSize: fontSize.sm,
  },
});
export default AuthenticationAddCard;
