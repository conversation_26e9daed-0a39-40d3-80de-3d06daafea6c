import React from 'react';
import {View} from 'react-native';
import {Svg, Circle, Text as SVGText} from 'react-native-svg';

import colors from '../../assets/colors';

function CircularProgress({
  size = 50,
  strokeWidth = 5,
  text = '',
  progressPercentile = 0,
}) {
  const radius = (size - strokeWidth) / 2;
  const circum = radius * 2 * Math.PI;
  const svgProgress = 100 - progressPercentile;

  return (
    <View style={{margin: 10}}>
      <Svg width={size} height={size}>
        <Circle
          stroke={colors.white}
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          {...{strokeWidth}}
        />

        <Circle
          stroke={colors.primary}
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeDasharray={`${circum} ${circum}`}
          strokeDashoffset={radius * Math.PI * 2 * (svgProgress / 100)}
          strokeLinecap="round"
          transform={`rotate(-90, ${size / 2}, ${size / 2})`}
          {...{strokeWidth}}
        />

        {text?.length > 0 ? (
          <SVGText
            fontSize={10}
            x={size / 2}
            y={size / 2 + 5}
            textAnchor="middle"
            fill={colors.white}>
            {text}
          </SVGText>
        ) : null}
      </Svg>
    </View>
  );
}

export default CircularProgress;
