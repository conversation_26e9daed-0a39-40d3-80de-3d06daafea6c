/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH / 2 - spacings.xl;

const AllBrandsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width="100%"
          marginBottom={20}
          height={40}
          borderRadius={10}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item
              marginBottom={20}
              width={CARD_WIDTH / 3}
              height={10}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item justifyContent="center">
            <SkeletonPlaceholder.Item
              width={CARD_WIDTH}
              marginBottom={6}
              height={CARD_WIDTH / 1.5}
              borderRadius={4}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={20}
                width={CARD_WIDTH / 3}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default AllBrandsLoader;
