import React from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {useLazyQuery} from '@apollo/react-hooks';

import {GET_USER_DATA} from '../../apollo/queries';
import {refreshUser} from '../../redux/actions/auth';

const useUserDefaultOptions = {focused: false};

export function useUser(options = useUserDefaultOptions) {
  const {focused} = {...useUserDefaultOptions, ...options};

  const user = useSelector(state => state?.auth?.user);
  const dispatch = useDispatch();

  const [fetch, {refetch, loading, error}] = useLazyQuery(GET_USER_DATA);

  const refresh = React.useCallback(async () => {
    try {
      const {data: _data} = await (refetch ? refetch?.() : fetch?.());
      if (_data?.me?.success) {
        dispatch(refreshUser({user: _data?.me?.data}));
      }
    } catch (_) {}
  }, [refetch, fetch, dispatch]);

  React.useEffect(() => {
    if (focused) {
      refresh();
    }
  }, [focused, refresh]);

  return {
    user,
    isLoading: loading,
    error,
    refresh,
  };
}

// Credentials are the ones we require right away after login without waiting for complete user API call. EG: To decide UI according to user or expert
export function useCredential() {
  const credential = useSelector(state => state?.auth?.credential);

  const _credential = React.useMemo(() => credential ?? {}, [credential]);

  return {..._credential, isExpert: credential?.role_id == 3};
}

export default useUser;
