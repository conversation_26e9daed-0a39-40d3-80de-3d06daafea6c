export default Object.freeze({
  LO<PERSON><PERSON>_START: '<PERSON>O<PERSON>N_START',
  <PERSON><PERSON><PERSON><PERSON>_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILED: 'LO<PERSON>N_FAILED',
  <PERSON>O<PERSON><PERSON>_E<PERSON>: '<PERSON><PERSON><PERSON><PERSON>_END',
  R<PERSON>ISTER_START: 'REGISTER_START',
  R<PERSON><PERSON>TER_SUCCESS: 'REGISTER_SUCCESS',
  R<PERSON>ISTER_FAILED: 'REGISTER_FAILED',
  R<PERSON>ISTER_END: 'REGISTER_END',
  ACCOUNT_VERIFICATION_START: 'ACCOUNT_VERIFICATION_START',
  ACCOUNT_VERIFICATION_SUCCESS: 'ACCOUNT_VERIFICATION_SUCCESS',
  ACCOUNT_VERIFICATION_FAILED: 'ACCOUNT_VERIFICATION_FAILED',
  ACCOUNT_VERIFICATION_END: 'ACCOUNT_VERIFICATION_END',
  CODE_VALIDATION_START: 'CODE_VALIDATION_START',
  CO<PERSON>_VALIDATION_SUCCESS: 'CODE_VALIDATION_SUCCESS',
  CODE_VALIDATION_FAILED: 'CODE_VALIDATION_FAILED',
  CODE_VALIDATION_END: 'CODE_VALIDATION_END',
  PASSWORD_RESET_START: 'PASSWORD_RESET_START',
  PASSWORD_RESET_SUCCESS: 'PASSWORD_RESET_SUCCESS',
  PASSWORD_RESET_FAILED: 'PASSWORD_RESET_FAILED',
  PASSWORD_RESET_END: 'PASSWORD_RESET_END',
  FORGOT_PASSWORD_START: 'FORGOT_PASSWORD_START',
  FORGOT_PASSWORD_SUCCESS: 'FORGOT_PASSWORD_SUCCESS',
  FORGOT_PASSWORD_FAILED: 'FORGOT_PASSWORD_FAILED',
  FORGOT_PASSWORD_END: 'FORGOT_PASSWORD_END',
  SIGN_OUT: 'SIGN_OUT',
  CLEAN_UP: 'CLEAN_UP',
  CREATE_NOTE: 'CREATE_NOTE',
  REMOVE_NOTE: 'REMOVE_NOTE',
  RESET_REDUCER_GROUP: 'RESET_REDUCER_GROUP',
  ONBOARDING: 'ONBOARDING',
  ADD_PHOTO: 'ADD_PHOTO',
  REMOVE_PHOTO: 'REMOVE_PHOTO',
  UPLOAD_METHOD: 'UPLOAD_METHOD',
  CLEAR_UPLOADS: 'CLEAR_UPLOADS',
  IS_FIRST_TIME_USING_APP: 'IS_FIRST_TIME_USING_APP',
  CHANGE_DP: 'CHANGE_DP',
  FRONT_IMAGE: 'FRONT_IMAGE',
  BACK_IMAGE: 'BACK_IMAGE',
  FRONT_TAG_IMAGE: 'FRONT_TAG_IMAGE',
  BACK_TAG_IMAGE: 'BACK_TAG_IMAGE',
  CLOSEUP_FRONT_IMAGE: 'CLOSEUP_FRONT_IMAGE',
  CLOSEUP_BACK_IMAGE: 'CLOSEUP_BACK_IMAGE',
  COPYRIGHT_IMAGE: 'COPYRIGHT_IMAGE',
  ARM_HEM_STITCHING_IMAGE: 'ARM_HEM_STITCHING_IMAGE',
  LOWER_HEM_STITCHING_IMAGE: 'LOWER_HEM_STITCHING_IMAGE',
  EXTRA_IMAGE: 'EXTRA_IMAGE',
  IS_EXPERT: 'IS_EXPERT',
  SHOW_SCORE: 'SHOW_SCORE',
  CRYPTO_ADDRESS: 'CRYPTO_ADDRESS',
  REFRESH_USER: 'REFRESH_USER',
  SET_AUTHENTICATION_TYPES_INFO: 'SET_AUTHENTICATION_TYPES_INFO',
  REFRESH_USER_PUSH_NOTIFICATION_SETTINGS:
    'REFRESH_USER_PUSH_NOTIFICATION_SETTINGS',
  UPLOAD_AI_IMAGES_REQUEST: 'UPLOAD_IMAGES_REQUEST',
  UPLOAD_AI_IMAGES_SUCCESS: 'UPLOAD_IMAGES_SUCCESS',
  UPLOAD_AI_IMAGES_FAILURE: 'UPLOAD_IMAGES_FAILURE',
});
