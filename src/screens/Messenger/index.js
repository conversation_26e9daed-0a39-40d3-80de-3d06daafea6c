import React, {useState, useCallback} from 'react';
import {FlatList, Text, View, TouchableOpacity, Platform} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';
import {useNavigation} from '@react-navigation/native';

import MessengerLoader from '../../components/Loaders/MessengerLoader';
import {DELETE_A_CHAT, BLOCK_USER} from '../../apollo/mutations';
import {GET_CHAT_LIST} from '../../apollo/queries';
import {icons, screens} from '../../assets/strings';
import colors from '../../assets/colors';
import Icon from '../../components/Icon';
import ProfileImage from '../../components/ProfileImage';
import {dateFromNow} from '../../tools/utils';
import styles from './styles';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import QueryManager from '../../components/QueryManager';
import locale from '../../assets/locale.json';
import RefreshControl from '../../components/RefreshControl';
import BottomSheet from '../../components/BottomSheet';
import ListActionRow from '../../components/ListActionRow';
import InfoModal from '../../components/Modal/InfoModal';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import NavigationService from '../../navigators/NavigationService';
import {useInteractionManager} from '../../tools/hooks';

const LIMIT = 100;

const isIOS = Platform.OS === 'ios';

const POPUP_INITIAL_STATE = {
  state: false,
  data: {
    title: locale.Error,
    description: locale.SomethingWentWrongPleaseTryAgain,
  },
};

const Index = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [toUserId, setToUserId] = useState(null);
  const [userRole, setUserRole] = useState('MEMBER');

  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: ['android'],
  });

  const scrollRef = React.useRef();

  const [refreshing, setRefreshing] = useState(false);

  const {data, loading, error, refetch} = useQuery(GET_CHAT_LIST, {
    variables: {limit: LIMIT},
  });

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];

          refetch?.();
        }
      } catch (error) {}
    }, [refetch]),
  );

  const startDeletion = React.useCallback((userId, _userRole = 'MEMBER') => {
    setToUserId(userId);
    setUserRole(_userRole);
    setModalVisible(previousState => !previousState);
  }, []);

  const closeSheet = () => {
    setToUserId(null);
    setModalVisible(false);
  };

  const list = data?.getChatList?.data?.edges;

  const renderSheetData = React.useMemo(
    () => (
      <BottomSheet
        visible={modalVisible}
        setVisible={setModalVisible}
        automaticallyAdjustHeight
        key={`${toUserId}|${userRole}`}>
        <View style={styles.modalWrapper}>
          <ListAction
            toUserId={toUserId}
            refetch={refetch}
            closeSheet={closeSheet}
            userRole={userRole}
            canBlock={userRole === 'MEMBER'}
          />
        </View>
      </BottomSheet>
    ),
    [closeSheet, refetch, toUserId, modalVisible, userRole],
  );
  const renderItem = React.useCallback(
    ({item}) =>
      isInteractionsComplete ? (
        <ChatRow startDeletion={startDeletion} item={item} />
      ) : null,
    [startDeletion, isInteractionsComplete],
  );

  return (
    <View style={styles.container}>
      <QueryManager
        data={data}
        loading={loading}
        error={error}
        refetch={refetch}>
        <QueryManager.Data>
          <FlatList
            data={list}
            keyExtractor={item => item?.id?.toString?.()}
            contentContainerStyle={
              list == null || list?.length == 0 ? {flex: 1} : {}
            }
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              isInteractionsComplete ? (
                <View style={styles.emptyView}>
                  <DefaultEmptyComponent
                    text={locale.YouHaveNoChat}
                    lottieStyle={{width: 80, height: 80, alignSelf: 'center'}}
                  />
                </View>
              ) : null
            }
            initialNumToRender={10}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            ListFooterComponent={
              !isInteractionsComplete ? <MessengerLoader /> : null
            }
            onScroll={e => {
              scrollRef.current = e?.nativeEvent?.contentOffset?.y;
            }}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <MessengerLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale.CouldNotFetchChatList}
          style={{marginVertical: 10}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
      {renderSheetData}
    </View>
  );
};

const ChatRow = React.memo(({startDeletion = () => null, item = null}) => {
  const navigation = useNavigation();

  const isNewMessage = !(item?.is_read_by_receiver || item?.isFromMe);
  const receiverId = item?.toUser?.id;

  const handleProfileView = () => {
    const role = item?.toUser?.role;
    NavigationService.navigate(
      item?.toUser?.role === 'MEMBER'
        ? screens.PUBLIC_PROFILES
        : screens.EXPERTS_PROFILE,
      {
        ...(role === 'MEMBER'
          ? {params: +receiverId}
          : {expertUserId: +receiverId}),
      },
    );
  };

  return (
    <TouchAwareButton
      scaleAnimationThreshold={0.96}
      style={styles.card}
      onPress={evt => {
        navigation.navigate(screens.CHAT_SCREEN, {params: receiverId});
      }}
      containerStyle={{
        borderBottomWidth: 2,
        borderBottomColor: colors.lightGrey,
      }}
      onLongPress={() => startDeletion(receiverId, item?.toUser?.role)}>
      {React.createElement(isIOS ? TouchableOpacity : TouchAwareButton, {
        onPress: evt => {
          evt?.stopPropagation?.();
          handleProfileView();
        },
      })}
      <TouchAwareButton
        onPress={evt => {
          evt?.stopPropagation?.();
          handleProfileView();
        }}>
        <ProfileImage
          containerStyle={{marginTop: 5}}
          url={item?.toUser?.image}
        />
      </TouchAwareButton>

      <View style={styles.info}>
        <Text style={styles.name} numberOfLines={1}>
          {item?.toUser?.username}
        </Text>
        <View style={styles.descWrapper}>
          <Text
            style={[
              styles.desc,
              {
                ...(!isNewMessage
                  ? {}
                  : {color: colors.black, fontWeight: 'bold'}),
              },
            ]}>
            {dateFromNow(item?.created_at)}{' '}
            {isNewMessage ? (
              <View
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 8 / 2,
                  backgroundColor: colors.secondary,
                }}
              />
            ) : null}
          </Text>
          <Text
            numberOfLines={1}
            style={[
              styles.separator,
              {
                ...(item?.is_read_by_receiver || item?.isFromMe
                  ? {}
                  : {color: colors.black, fontWeight: 'bold'}),
              },
            ]}>
            {item?.text}
          </Text>
        </View>
      </View>
      <TouchAwareButton
        onPress={() => startDeletion(receiverId, item?.toUser?.role)}
        style={{
          padding: 10,
          paddingRight: 0,
          marginRight: -5,
        }}
        containerStyle={{alignSelf: 'center'}}>
        <Icon name={icons.ELIPSIS_VERTICAL} tint={colors.darkGrey} />
      </TouchAwareButton>
    </TouchAwareButton>
  );
});

function ListAction({
  refetch = () => null,
  toUserId = null,
  closeSheet = () => null,
  canBlock = true,
  userRole = 'MEMBER',
}) {
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [isBlockLoading, setIsBlockLoading] = useState(false);

  const [popup, setPopup] = React.useState(POPUP_INITIAL_STATE);

  const [deleteChat] = useMutation(DELETE_A_CHAT);
  const [blockUser] = useMutation(BLOCK_USER);

  const handleMessageDeletion = async () => {
    setIsDeleteLoading(true);
    try {
      await deleteChat({
        variables: {
          userId: +toUserId,
        },
      });
      refetch();
      closeSheet();
    } catch (_) {
      setPopup({
        ...POPUP_INITIAL_STATE,
        state: true,
      });
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const handleUserBlock = async () => {
    setIsBlockLoading(true);
    try {
      const {data} = await blockUser({
        variables: {
          userId: +toUserId,
        },
      });
      const success = data?.blockUser?.success;
      const code = data?.blockUser?.code;
      const message = data?.blockUser?.message;

      if (success || code === 'USER_ALREADY_BLOCKED') {
        refetch();
        closeSheet();
      } else {
        if (code?.length) {
          setPopup(previousState => ({
            ...POPUP_INITIAL_STATE,
            state: true,
            isError: true,
            data: {
              ...previousState.data,
              description: message ?? locale.SomethingWentWrongPleaseTryAgain,
            },
          }));
        } else {
          throw new Error();
        }
      }
    } catch (_) {
      setPopup({
        ...POPUP_INITIAL_STATE,
        state: true,
      });
    } finally {
      setIsBlockLoading(false);
    }
  };

  const handleProfileView = () => {
    closeSheet();
    NavigationService.navigate(
      userRole === 'MEMBER' ? screens.PUBLIC_PROFILES : screens.EXPERTS_PROFILE,
      {
        ...(userRole === 'MEMBER'
          ? {params: +toUserId}
          : {expertUserId: +toUserId}),
      },
    );
  };

  return (
    <>
      <ListActionRow
        onPress={handleProfileView}
        disabled={isDeleteLoading || isBlockLoading}
        actionText={locale.ViewProfile}
        iconName="person_icon"
        iconContainerStyle={{backgroundColor: colors.primary}}
        iconSize={15}
      />
      {canBlock ? (
        <ListActionRow
          onPress={handleUserBlock}
          isLoading={isBlockLoading}
          disabled={isBlockLoading || isDeleteLoading}
          actionText={locale.BlockUser}
          iconName="blockUser"
          iconContainerStyle={{backgroundColor: colors.darkGrey}}
          containerStyle={{marginTop: 20}}
        />
      ) : null}

      <ListActionRow
        onPress={handleMessageDeletion}
        isLoading={isDeleteLoading}
        disabled={isDeleteLoading || isBlockLoading}
        actionText={locale.DeleteChat}
        iconName="delete"
        iconContainerStyle={{backgroundColor: colors.red}}
        containerStyle={{marginTop: 20}}
      />
      <InfoModal
        setVisible={() => setPopup(POPUP_INITIAL_STATE)}
        popUp={{isError: true, state: popup.state, data: popup.data}}
      />
    </>
  );
}

export default Index;
