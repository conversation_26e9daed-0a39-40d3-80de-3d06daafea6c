import React from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import dayjs from 'dayjs';
import ClipBoard from '@react-native-community/clipboard';

import {styles} from './styles';
import {spacings} from '../../assets/spacing';
import {singleListingScreen, screens} from '../../assets/strings';
import ProfileImage from '../../components/ProfileImage';
import {useUser} from '../../tools/hooks';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {showToast} from '../../components/Toast';
import locale from '../../assets/locale.json';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import Favorite from './Favorite';
import Subscribe from './Subscribe';

var relativeTime = require('dayjs/plugin/relativeTime');
var duration = require('dayjs/plugin/duration');

dayjs.extend(relativeTime);
dayjs.extend(duration);

const UserCard = ({data, refetchPost = () => null, renderShare = null}) => {
  const navigation = useNavigation();

  const {user: userData} = useUser();

  const authenticationData = data?.getPost?.data;

  const isPostByMe = userData?.id == data?.getPost?.data?.user?.id;

  const handleDecision = React.useCallback(() => {
    switch (
      authenticationData?.authentication_result?.community_result?.decision
    ) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
        return 'UNDETERMINED';
      default:
        return 'PENDING';
    }
  }, [authenticationData?.authentication_result?.community_result?.decision]);

  const handleNavigation = () => {
    if (data?.getPost?.data?.role === 'EXPERT') {
      navigation.navigate(screens.EXPERTS_PROFILE, {
        params: data?.getPost?.data?.id,
      });
    } else if (isPostByMe) {
      navigation.navigate(screens.USER_PROFILE);
    } else {
      navigation.navigate(screens.PUBLIC_PROFILES, {
        params: data?.getPost?.data?.user?.id,
      });
    }
  };

  const isAuthenticationPending = React.useMemo(
    () => data?.getPost?.data?.authentication_status === 'PENDING',
    [data?.getPost?.data?.authentication_status],
  );

  const isVotingClosed = React.useMemo(
    () => authenticationData?.is_voting_closed,
    [authenticationData?.is_voting_closed],
  );

  const isRevealing = React.useMemo(
    () => handleDecision() !== 'PENDING' && !isVotingClosed,
    [handleDecision, isVotingClosed],
  );

  const handleCopyToClipboard = React.useCallback(() => {
    ClipBoard.setString(
      `${data?.getPost?.data?.authentication_key?.toUpperCase?.() ?? ''}`,
    );
    showToast({type: 'success', message: locale.IDCopiedToClipboard});
  }, [data?.getPost?.data?.authentication_key]);

  return (
    <View style={[styles.userContainer]}>
      <View style={styles.userContainerTopSection}>
        <TouchableOpacity style={styles.userId} onPress={handleNavigation}>
          <Text style={[styles.sm, {marginRight: spacings.sm}]}>
            {singleListingScreen.BY}
          </Text>
          <ProfileImage url={data?.getPost?.data?.user?.image} size={25} />

          <Text style={[styles.sm, {marginLeft: spacings.sm}]}>
            {data?.getPost?.data?.user?.username}
          </Text>
        </TouchableOpacity>
        <Text style={styles.sm}>
          {singleListingScreen.UPLOADED}
          {dayjs(
            data?.getPost?.data?.approved_at ?? data?.getPost?.data?.created_at,
          ).fromNow()}
        </Text>
      </View>
      <View style={[styles.userContainerBottomSection]}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
          }}>
          <Text numberOfLines={3} style={[styles.userCardTitleText]}>
            {data?.getPost?.data?.title}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TouchAwareButton
              onPress={handleCopyToClipboard}
              style={styles.authKey}>
              <Text style={styles.postId}>
                {singleListingScreen.ID}{' '}
                {data?.getPost?.data?.authentication_key ?? ''}
              </Text>
              <Icon name="copyToClipboard" tint={colors.primary} size={15} />
            </TouchAwareButton>
          </View>
        </View>

        {!isAuthenticationPending ? (
          <View
            style={[
              styles.authKey,
              {
                flexWrap: 'wrap',
                alignItems: 'center',
                flexDirection: 'row',
              },
            ]}>
            <Favorite
              postId={authenticationData?.id}
              isLiked={authenticationData?.is_liked_by_me}
              numberOfLikes={authenticationData?.number_of_likes}
            />
            <Subscribe
              postId={authenticationData?.id}
              isPostByMe={isPostByMe}
              postUpdateSubscription={
                authenticationData?.post_updates_subscription
              }
              refetchPost={refetchPost}
            />
            {renderShare}
          </View>
        ) : null}

        {isRevealing ? (
          <CountDownTimer startDate={authenticationData?.voting_close_date} />
        ) : null}
      </View>
    </View>
  );
};

function CountDownTimer({startDate}) {
  const [string, setString] = React.useState('');

  React.useEffect(() => {
    let timerId;

    if (dayjs(startDate).isValid()) {
      timerId = setInterval(() => {
        const difference = +new Date(startDate) - +new Date();
        if (difference >= 0) {
          if (difference >= 24 * 60 * 60 * 1000) {
            setString(dayjs(startDate).fromNow(true));
          } else {
            const start = dayjs(new Date());
            const end = dayjs(startDate);
            const diff = dayjs.duration(end.diff(start));
            const diffFormatted = diff.format('HH:mm');
            const [hours, minutes] = diffFormatted?.split(':');
            setString(`${hours} hours, ${minutes} minutes`);
          }
        } else {
          setString('');
          clearInterval(timerId);
        }
      }, 1000);
    } else {
      setString('');
      clearInterval(timerId);
    }

    return () => {
      clearInterval(timerId);
    };
  }, [startDate]);

  return string?.length > 0 ? (
    <Text
      style={{
        fontWeight: '700',
        fontFamily: 'Inter-Medium',
        fontSize: 16,
        color: colors.secondaryRed,
        marginTop: 10,
      }}>
      Voting closes in {string}
    </Text>
  ) : null;
}

export default UserCard;
