/* eslint-disable prettier/prettier */
import React from 'react';
import {Button, ActivityIndicator, View, Text} from 'react-native';

import {ImagePicker} from '../../components';
import useImageUpload from '../../tools/hooks/useImageUpload';
import colors from '../../assets/colors';

export default function Home() {
  const [uploadStatus, upload] = useImageUpload();
  const {isUploading, success, error} = uploadStatus;
  const [visible, setVisible] = React.useState(false);

  const onImageSelected = React.useCallback(
    image => {
      // pass image path and mime to useImageUpload custom hook
      try {
        upload({
          type: image.mime,
          path: image.path,
        });
      } catch (err) {
        if (__DEV__) {
          console.log(err);
        }
      }
    },
    [upload],
  );

  const handleImagePick = React.useCallback(() => {
    setVisible(true);
  }, [setVisible]);

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <Button title="Upload" onPress={handleImagePick} />

      <ImagePicker {...{visible, setVisible, onImageSelected}} />

      <View>
        {isUploading ? (
          <ActivityIndicator
            color={colors.primary}
            style={{marginVertical: 20}}
          />
        ) : null}
        <Text style={{fontSize: 20, textAlign: 'center'}}>
          {success ? 'Uploaded' : error ? 'Upload Error' : null}
        </Text>
      </View>
    </View>
  );
}
