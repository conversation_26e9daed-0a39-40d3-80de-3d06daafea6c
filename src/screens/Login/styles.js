import {StyleSheet, Dimensions, Platform} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

const {width: WIDTH} = Dimensions.get('window');

const isIOS = Platform.OS == 'ios';

export const styles = StyleSheet.create({
  scrollViewWrapper: {
    flexGrow: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
  },
  loginContainer: {
    width: WIDTH * 0.9,
    backgroundColor: colors.white,
    flex: 1,
    alignItems: 'center',
    marginVertical: 20,
  },
  submitButton: {
    backgroundColor: colors.primary,
    height: spacings.xxxl,
    marginTop: spacings.lg,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacings.sm,
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSize.md,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  titleWrapper: {
    width: '100%',
    marginBottom: spacings.lg,
  },
  titleStyle: {
    fontSize: 25,
    fontWeight: 'bold',
    color: colors.black,
  },
  inputCard: {
    marginVertical: spacings.md,
  },
  inputLabel: {
    width: '100%',
    fontWeight: 'bold',
    fontSize: fontSize.md,
    color: colors.black,
    marginBottom: 5,
  },
  inputField: {
    width: '100%',
    paddingLeft: spacings.md,
    color: colors.black,
    backgroundColor: colors.white,
    borderRadius: spacings.sm,
    fontSize: fontSize.md,
    justifyContent: 'flex-end',
    // ADDED
    ...(isIOS ? {padding: 15} : {}),
  },
  passwordWrapper: {
    width: '100%',
    justifyContent: 'center',
  },
  togglePassword: {
    position: 'absolute',
    right: 0,
    height: spacings.xxxl,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacings.md,
  },
  togglePasswordText: {
    fontSize: fontSize.sm,
    color: colors.black,
  },
  forgotPasswordText: {
    fontSize: fontSize.md,
    color: colors.primary,
    fontWeight: 'bold',
  },
  forgotPasswordSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacings.md,
    width: '100%',
  },
  errorText: {
    fontSize: 9,
    color: colors.warning,
    width: '100%',
    position: 'absolute',
  },
  errorWrapper: {
    width: '100%',
    marginTop: 5,
  },
});
