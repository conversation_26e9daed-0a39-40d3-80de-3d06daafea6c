<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.auth8">

<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.RECORD_AUDIO"/>
<uses-permission android:name="android.permission.VIBRATE" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/BootTheme"
      android:requestLegacyExternalStorage="true"
      android:largeHeap="true"
      android:hardwareAccelerated="true"
>
      <meta-data 
        android:name="com.google.firebase.messaging.default_notification_icon"
        android:resource="@drawable/ic_primary" />
      <meta-data
        android:name="com.google.firebase.messaging.default_notification_color"
        android:resource="@color/primary" />
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true"
        android:screenOrientation="portrait">
        <intent-filter >
          <action android:name="android.intent.action.MAIN" />
          <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        <intent-filter android:autoVerify="true">
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:host="auth8" android:scheme="post" />
          <data android:scheme="https" android:host="dev.legiteem8.app" />
          <data android:scheme="http" android:host="dev.legiteem8.app" />
        </intent-filter>
        
      </activity>
    </application>
</manifest>
