/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const CARD_WIDTH = WIDTH - spacings.xl;

const CommentsPageLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={SCREEN_HEIGHT / 3}
          borderRadius={4}
          marginHorizontal={20}
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={60}
          borderRadius={4}
          marginHorizontal={20}
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={60}
          borderRadius={4}
          marginHorizontal={20}
        />
        <SkeletonPlaceholder.Item
          width={CARD_WIDTH}
          marginBottom={15}
          height={60}
          borderRadius={4}
          marginHorizontal={20}
        />
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingTop: 20,
    alignItems: 'center',
  },
});

export default CommentsPageLoader;
