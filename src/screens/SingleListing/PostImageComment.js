import React, {useLayoutEffect, useEffect, useCallback, useState} from 'react';
import {
  StyleSheet,
  TextInput,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Keyboard,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {useQuery, useMutation} from '@apollo/client';
import dayjs from 'dayjs';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withDelay,
  FadeInDown,
} from 'react-native-reanimated';
import {useFocusEffect} from '@react-navigation/native';

import CommentPageLoader from '../../components/Loaders/CommentPageLoader';
import CommentsLoader from '../../components/Loaders/CommentsLoader';
import {ADD_COMMENT_TO_POST_IMAGE} from '../../apollo/mutations';
import Icon from '../../components/Icon';
import {GET_POST_IMAGE_COMMENTS, GET_POST_IMAGES} from '../../apollo/queries';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ImageViewer from '../../components/ImageViewer';
import FastImage from '../../components/FastImage';
import ProfileImage from '../../components/ProfileImage';
import {useCredential} from '../../tools/hooks/useUser';
import RefreshControl from '../../components/RefreshControl';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import locale from '../../assets/locale.json';
import TextAutoLink from '../../components/TextAutoLink';
import ActivityIndicator from '../../components/ActivityIndicator';
import {screens} from '../../assets/strings';
import {IMAGE_TYPES} from '../../assets/strings';
import UsernameAutocompleteSheet from './UsernameAutocompleteSheet';

const LIMIT = 100;
const isIOS = Platform.OS == 'ios';

const {width: WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('screen');

const PostImageComment = ({route, navigation}) => {
  const authenticationImageUrl = route?.params?.image_url;
  const authenticationImageName = route?.params?.image_name;
  const postId = route?.params?.postId;
  const showPostNavigationLink = route?.params?.showPostNavigationLink ?? false;

  const scrollRef = React.useRef();

  const [refreshing, setRefreshing] = useState(false);

  const credential = useCredential();

  const isMember = credential?.role_id == 2;

  const [sendViewHeight, setSendViewHeight] = React.useState(0);
  const {bottom: bottomOffset} = useSafeAreaInsets();

  const [height, setHeight] = useState(40);
  const [comment, setComment] = useState(null);

  const {data, refetch, loading, error} = useQuery(GET_POST_IMAGE_COMMENTS, {
    variables: {
      postId: postId,
      authenticationImage: authenticationImageName?.toUpperCase?.(),
      limit: LIMIT,
    },
  });

  const {data: postData} = useQuery(GET_POST_IMAGES, {
    variables: {
      id: postId,
    },
  });

  useEffect(() => {
    return () => {
      Keyboard.dismiss();
    };
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          refetch?.();
        }
      } catch (_) {}
    }, [refetch]),
  );

  const handleImageName = useCallback(
    value => {
      switch (authenticationImageName) {
        case 'front':
          return 'Front image';
        case 'back':
          return 'Back image';
        case 'front_tag':
          return 'Front tag image';
        case 'back_tag':
          return 'Back tag image';
        case 'closeup_front':
          return 'Closeup front image';
        case 'closeup_back':
          return 'Closeup back image';
        case 'copyright':
          return 'Copyright image';
        case 'arm_hem_stitching':
          return 'Arm hem stitching image';
        case 'lower_hem_stitching':
          return 'Lower hem stitching image';
        default:
          return '';
      }
    },
    [authenticationImageName],
  );

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: handleImageName(authenticationImageName),
    });
  }, [authenticationImageName, handleImageName, navigation]);

  const [
    addCommentToPostImage,
    {loading: isAddCommentMutationLoading},
  ] = useMutation(ADD_COMMENT_TO_POST_IMAGE, {
    onCompleted: refetch,
  });

  const commentOnPostImage = useCallback(async () => {
    try {
      await addCommentToPostImage({
        variables: {
          postId: parseInt(postId, 10),
          type: 'TEXT',
          message: comment,
          authenticationImageType: authenticationImageName.toUpperCase(),
        },
      });
      Keyboard.dismiss();
      setComment(null);
      setHeight(40);
      Keyboard.dismiss();
    } catch (e) {}
  }, [addCommentToPostImage, authenticationImageName, comment, postId]);

  const canComment = React.useMemo(
    () => isMember && postData?.getPost?.data?.authentication_type?.id == 1,
    [isMember, postData?.getPost?.data?.authentication_type?.id],
  );
  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );
  const handleProfileView = (userId, isExpert = false) => {
    navigation.navigate(
      isExpert ? screens.EXPERTS_PROFILE : screens.PUBLIC_PROFILES,
      {
        ...(isExpert ? {expertUserId: userId} : {params: userId}),
      },
    );
  };

  const renderItem = useCallback(
    ({item}) => {
      return (
        <>
          <View style={styles.commentCard}>
            <TouchableOpacity
              onPress={handleProfileView.bind(
                null,
                item?.user?.id,
                item?.user?.role === 'EXPERT',
              )}>
              <ProfileImage url={item?.user?.image} style={{marginRight: 10}} />
            </TouchableOpacity>
            <View style={styles.textWrapper}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  flexWrap: 'wrap',
                  marginBottom: 5,
                }}>
                <Text style={styles.username}>{item?.user?.username}</Text>
                <Text style={styles.date}>
                  {dayjs(item?.created_at).fromNow()}
                </Text>
              </View>
              <Text style={styles.commentText}>
                <TextAutoLink text={item?.message} />
              </Text>
            </View>
          </View>
        </>
      );
    },
    [handleProfileView],
  );

  const parseAllImages = React.useMemo(() => {
    const images = postData?.getPost?.data?.images;
    const _images = [];
    if (!(images == null) && typeof images === 'object') {
      for (const [key, value] of Object.entries(images)) {
        if (key in IMAGE_TYPES && value?.length) {
          _images.push({id: key, url: value});
        }
      }
    }
    return _images;
  }, [postData]);

  const handleViewPost = React.useCallback(() => {
    navigation.push(screens.SINGLE_LISTING, {
      params: postId,
    });
  }, [navigation.push, postId]);

  const renderComments = React.useMemo(
    () => (
      <FlatList
        data={data?.getPostImageComments?.data?.edges}
        keyExtractor={item => item.id}
        renderItem={renderItem}
        scrollEnabled={true}
        horizontal={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.commentWrapper}
        ListHeaderComponent={
          <>
            <Header
              currentImage={{
                uri:
                  authenticationImageUrl ??
                  data?.getPostImageComments?.data?.edges?.[0]?.post?.images?.[
                    authenticationImageName?.toLowerCase?.()
                  ],
                id: authenticationImageName,
              }}
              allImages={parseAllImages ?? []}
            />
            {showPostNavigationLink ? (
              <TouchableOpacity
                onPress={handleViewPost}
                activeOpacity={0.5}
                style={{
                  alignSelf: 'flex-end',
                  paddingHorizontal: 20,
                  paddingTop: 10,
                }}>
                <Text
                  style={{
                    fontFamily: 'Inter-Medium',
                    fontSize: 16,
                    color: colors.primary,
                  }}>
                  View Full Post
                </Text>
              </TouchableOpacity>
            ) : null}
          </>
        }
        ListEmptyComponent={
          loading ? (
            <CommentsLoader />
          ) : (
            <View style={styles.emptyContainer}>
              <DefaultEmptyComponent
                lottieStyle={{width: 80, height: 80, alignSelf: 'center'}}
                text={locale.ThereAreNoCommentsAtTheMoment}
                textStyle={{fontSize: 14}}
              />
            </View>
          )
        }
        ListFooterComponent={
          loading && data == null ? <CommentsLoader /> : null
        }
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        initialNumToRender={10}
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
      />
    ),
    [
      data?.getPostImageComments?.data?.edges,
      renderItem,
      authenticationImageUrl,
      handleViewPost,
    ],
  );

  if (error) {
    return <CommentPageLoader />;
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, canComment ? {marginBottom: bottomOffset} : {}]}
      {...(isIOS
        ? {
            behavior: isIOS ? 'position' : 'height',
            keyboardVerticalOffset: sendViewHeight,
          }
        : {})}
      contentContainerStyle={{flex: 1}}>
      {renderComments}
      {canComment ? (
        <CommentInput
          key="comment-input"
          comment={comment}
          setComment={setComment}
          height={height}
          setHeight={setHeight}
          sendViewHeight={sendViewHeight}
          setSendViewHeight={setSendViewHeight}
          onSubmit={commentOnPostImage}
          renderRightComponent={
            <TouchableOpacity
              style={styles.button}
              onPress={commentOnPostImage}
              disabled={
                comment == null ||
                comment?.length == 0 ||
                isAddCommentMutationLoading
              }>
              {isAddCommentMutationLoading ? (
                <ActivityIndicator color={colors.white} />
              ) : (
                <Icon name="send" size={20} tint={colors.white} />
              )}
            </TouchableOpacity>
          }
        />
      ) : null}
    </KeyboardAvoidingView>
  );
};

function Header({currentImage, allImages = []}) {
  const [imageViewerVisible, setImageViewerVisible] = React.useState(false);

  const opacity = useSharedValue(1);
  const transformStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const handleImageViewing = React.useCallback(() => {
    setImageViewerVisible(true);
  }, []);

  React.useEffect(() => {
    opacity.value = withDelay(1000, withSpring(0, {damping: 20}));
  }, [opacity.value]);

  const findIndex = React.useMemo(
    () => allImages?.findIndex(item => currentImage?.id == item?.id) ?? 0,
    [],
  );

  return (
    <>
      <TouchAwareButton
        disableLongPressAnimation
        disableTapAnimation
        onPress={handleImageViewing}
        style={{justifyContent: 'center', alignItems: 'center'}}>
        <FastImage
          source={{
            uri: currentImage?.uri,
          }}
          style={styles.imageStyles}
        />
        <Animated.View
          style={[
            transformStyle,
            {
              position: 'absolute',
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(1,1,1,0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            },
          ]}
        />
        <Animated.Text
          style={[
            transformStyle,
            {
              color: colors.white,
              position: 'absolute',
              fontFamily: 'Inter-Medium',
            },
          ]}>
          {locale.TapToSeeFullImage}
        </Animated.Text>
      </TouchAwareButton>

      <ImageViewer
        visible={imageViewerVisible}
        setVisible={setImageViewerVisible}
        images={allImages}
        index={findIndex}
      />
    </>
  );
}

const SELECTION_INITIAL_STATE = {
  isMatched: false,
  cursorEnd: 0,
  text: '',
};
function CommentInput({
  setSendViewHeight = () => null,
  comment = '',
  setComment = () => null,
  height = 0,
  setHeight = () => null,
  renderRightComponent = null,
  onSubmit = () => null,
}) {
  const inputRef = React.useRef();
  const firstMountRef = React.useRef(true);
  const selectionTimerRef = React.useRef();

  const [nativeSelection, setNativeSelection] = React.useState({
    start: 0,
    end: 0,
  });

  const [selection, setSelection] = React.useState({
    ...SELECTION_INITIAL_STATE,
  });

  const handleSelectionChange = React.useCallback((text, end) => {
    if (text?.length > 0) {
      const selection = text?.slice(0, end);
      const split = selection?.split(' ');
      const currentWord = split?.length > 0 ? split[split.length - 1] : null;
      const isMatched =
        !(currentWord == null) &&
        /[@][A-Za-z][A-Za-z0-9]*(?:[.|_][A-Za-z0-9]+)*/g.test(currentWord);
      setSelection(() => ({
        isMatched,
        cursorEnd: end,
        text: isMatched ? currentWord : '',
      }));
    } else {
      setSelection({...SELECTION_INITIAL_STATE});
    }
  }, []);

  React.useEffect(() => {
    if (!firstMountRef.current) {
      if (selectionTimerRef.current) {
        clearTimeout(selectionTimerRef.current);
      }
      selectionTimerRef.current = setTimeout(() => {
        handleSelectionChange(comment, nativeSelection.end);
      }, 100);
    } else {
      firstMountRef.current = false;
    }
    return () => {
      clearTimeout(selectionTimerRef.current);
    };
  }, [nativeSelection, comment]);

  const handleUserItemPress = React.useCallback(
    user => {
      if (selection.isMatched) {
        const replacedComment = `${comment?.slice(
          0,
          selection.cursorEnd - selection.text.length,
        )}@${user?.username} ${comment.slice(selection.cursorEnd)}`;
        setComment(replacedComment);
        setSelection({...SELECTION_INITIAL_STATE});
      }
    },
    [selection, comment],
  );

  const handleSubmit = () => {
    inputRef.current?.blur();
    setSelection({...SELECTION_INITIAL_STATE});
    onSubmit();
  };

  const handleOnBlur = () => {
    setSelection({...SELECTION_INITIAL_STATE});
  };

  return (
    <View
      style={{
        flex: 1,
        ...(selection.isMatched
          ? {
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              opacity: 1,
            }
          : {}),
      }}>
      <Animated.View
        entering={FadeInDown.delay(500).springify().stiffness(500).damping(50)}
        style={[styles.commentSection, {zIndex: 1}]}
        onLayout={evt => {
          setSendViewHeight(evt?.nativeEvent?.layout?.height);
        }}>
        <TextInput
          ref={inputRef}
          placeholder="Write comment..."
          value={comment}
          onChangeText={setComment}
          multiline={true}
          placeholderTextColor={colors.grey}
          selectionColor={colors.darkGrey}
          style={[styles.inputStyle, {height: height}]}
          onContentSizeChange={e => setHeight(e.nativeEvent.contentSize.height)}
          maxLength={256}
          onSelectionChange={({nativeEvent: {selection}}) => {
            setNativeSelection(selection);
          }}
          onBlur={handleOnBlur}
        />
        {React.cloneElement(renderRightComponent, {
          onPress: handleSubmit,
        })}
      </Animated.View>
      <UsernameAutocompleteSheet
        term={selection.isMatched ? selection.text : ''}
        onItemPress={handleUserItemPress}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGrey,
  },
  imageStyles: {
    height: WIDTH / 1.5,
    width: WIDTH,
  },
  commentSection: {
    backgroundColor: colors.black,
    flexDirection: 'row',
    paddingVertical: spacings.lg,
    alignItems: 'center',
    position: 'absolute',
    bottom: 0,
  },
  commentWrapper: {
    paddingBottom: 100,
  },
  commentCard: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    marginTop: 15,
    marginHorizontal: spacings.lg,
    padding: spacings.md,
    borderRadius: 4,
  },
  commentText: {
    color: colors.black,
    fontSize: fontSize.md,
  },
  textWrapper: {
    marginLeft: spacings.sm,
    flex: 1,
  },
  date: {
    color: '#828282',
    fontSize: fontSize.sm,
  },
  username: {
    fontWeight: 'bold',
    color: colors.black,
    fontSize: fontSize.md,
  },
  inputStyle: {
    flex: 1,
    backgroundColor: '#222',
    marginRight: spacings.sm,
    borderRadius: 4,
    paddingLeft: spacings.md,
    marginLeft: spacings.lg,
    color: colors.white,
    minHeight: 45,
  },
  dpStyles: {
    height: 40,
    width: 40,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: colors.lightGrey,
  },
  button: {
    padding: spacings.md,
    marginRight: spacings.md,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    height: SCREEN_HEIGHT / 2.8,
    justifyContent: 'center',
  },
  emptyContainerText: {
    color: colors.black,
    fontSize: fontSize.md,
  },
});

export default PostImageComment;
