import React, {Suspense} from 'react';
import {Text, View, ActivityIndicator} from 'react-native';

import locale from '../../assets/locale.json';
import Icon from '../Icon';
import colors from '../../assets/colors';

export class ErrorBoundary extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {error: null};
  }

  componentDidCatch(error, errorInfo) {
    if (process.env.NODE_ENV === 'development') {
      console.log('Error boundary', {error}, {errorInfo});
    }
    this.setState({error});
  }

  render() {
    const {error} = this.state;
    const {children, parentContainer} = this.props;

    const resetError = () => {
      this.setState({error: null});
    };

    const handleClick = () => {
      // cache.delete(`err@${endpoint}`);
      const {error} = this.state;
      resetError();
    };

    if (error) {
      return React.createElement(
        parentContainer,
        null,
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <Icon size={25} name="reload" tint={colors.black} clickable />
          <Text>{locale['SomethingWentWrong.']}</Text>
        </View>,
      );
    }
    return children({error, resetError});
  }
}

function ErrorBoundaryRPC({
  children,
  defaultFallBack = false,
  fallBackComponent = null,
  parentContainer = View,
}) {
  return (
    <ErrorBoundary {...{parentContainer}}>
      {({...props}) => (
        <Suspense
          fallback={
            defaultFallBack
              ? React.createElement(
                  parentContainer,
                  null,
                  <ActivityIndicator size="large" color="red" />,
                )
              : fallBackComponent
          }>
          {children({...props})}
        </Suspense>
      )}
    </ErrorBoundary>
  );
}

export default ErrorBoundaryRPC;
