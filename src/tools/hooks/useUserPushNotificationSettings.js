import React from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {useLazyQuery} from '@apollo/react-hooks';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {GET_PUSH_NOTIFICATION_SETTING} from '../../apollo/queries';
import {refreshUserPushNotificationSettings} from '../../redux/actions/auth';
import {persistent} from '../../assets/strings';

const useUserDefaultOptions = {focused: false};

function syncNotificationToStorage(data) {
  const keyValuePairs = [];
  Object.entries(data)?.forEach(([key, value]) => {
    switch (key) {
      case 'allow_direct_message':
        keyValuePairs.push([
          persistent.ALLOW_DIRECT_MESSAGE_NOTIFICATION,
          value ? '1' : '0',
        ]);
      case 'allow_post_reaction':
        keyValuePairs.push([
          persistent.ALLOW_POST_REACTION_NOTIFICATION,
          value ? '1' : '0',
          ,
        ]);
      case 'allow_authentication_submission_status':
        keyValuePairs.push([
          persistent.ALLOW_POST_REACTION_NOTIFICATION,
          value ? '1' : '0',
          ,
        ]);
      case 'allow_follow_status':
        keyValuePairs.push([
          persistent.ALLOW_FOLLOW_STATUS_NOTIFICATION,
          value ? '1' : '0',
          ,
        ]);
      case 'allow_in_app_direct_message':
        keyValuePairs.push([
          persistent.ALLOW_IN_APP_DIRECT_MESSAGE_NOTIFICATION,
          value ? '1' : '0',
          ,
        ]);
      case 'allow_in_app_post_reaction':
        keyValuePairs.push([
          persistent.ALLOW_IN_APP_POST_REACTION_NOTIFICATION,
          value ? '1' : '0',
          ,
        ]);
      case 'allow_in_app_authentication_submission_status':
        keyValuePairs.push([
          persistent.ALLOW_IN_APP_AUTHENTICATION_SUBMISSION_STATUS_NOTIFICATION,
          value ? '1' : '0',
          ,
        ]);
      case 'allow_in_app_follow_status':
        keyValuePairs.push([
          persistent.ALLOW_IN_APP_FOLLOW_STATUS_NOTIFICATION,
          value ? '1' : '0',
          ,
        ]);
    }
  });

  return Promise.all(
    keyValuePairs.map(([key, value]) => AsyncStorage.setItem(key, value)),
  );
}

export function useUserPushNotificationSettings(
  options = useUserDefaultOptions,
) {
  const {focused} = {...useUserDefaultOptions, ...options};

  const userPushNotificationSetting = useSelector(
    state => state?.auth?.notification,
  );
  const dispatch = useDispatch();

  const [fetch, {refetch, loading, error}] = useLazyQuery(
    GET_PUSH_NOTIFICATION_SETTING,
  );

  const refresh = React.useCallback(async () => {
    try {
      const {data: _data} = await (refetch ? refetch?.() : fetch?.());
      if (
        _data?.getPushNotificationSetting?.success &&
        !(_data?.getPushNotificationSetting?.data == null)
      ) {
        dispatch(
          refreshUserPushNotificationSettings(
            _data?.getPushNotificationSetting?.data,
          ),
        );
        await syncNotificationToStorage(
          _data?.getPushNotificationSetting?.data,
        );
      }
    } catch (_) {}
  }, [refetch, fetch, dispatch]);

  React.useEffect(() => {
    if (focused) {
      refresh();
    }
  }, [focused, refresh]);

  return {
    userPushNotificationSetting,
    isLoading: loading,
    error,
    refresh,
    syncNotificationToStorage,
  };
}

export default useUserPushNotificationSettings;
