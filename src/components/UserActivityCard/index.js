import React from 'react';
import {StyleSheet, Text, View, Dimensions} from 'react-native';
import dayjs from 'dayjs';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {images} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import FastImage from '../FastImage';

const {width} = Dimensions.get('window');
const WIDTH = width / 2;
const CARD_WIDTH = WIDTH - spacings.xl;

const UserActivityCard = ({item}) => {
  const handleBackgroundColor = decision => {
    switch (decision) {
      case 'REAL':
        return colors.greenBadge;
      case 'FAKE':
        return colors.redBadge;
      case 'UNDECIDED':
        return colors.darkBadge;
      case 'UNDETERMINED':
        return colors.amber;
      default:
        colors.darkBadge;
    }
  };

  return (
    <>
      <View style={styles.cardsGroup}>
        <View>
          <FastImage
            source={
              item?.post?.images?.featured || item?.post?.images?.closeup_front
                ? {
                    uri:
                      item?.post?.images?.featured ||
                      item?.post?.images?.closeup_front,
                  }
                : images.PLACEHOLDER
            }
            style={styles.imageStyle}
          />

          {item?.__typename === 'FEED_ACTIVITY_NEW_POST' ? (
            <View
              style={[
                styles.badgeStyle,
                {
                  backgroundColor: colors.darkBadge,
                },
              ]}>
              <Text style={styles.badgeText}>Posted</Text>
            </View>
          ) : null}

          {item?.__typename === 'FEED_ACTIVITY_NEW_VOTE' ||
          item?.__typename === 'FEED_ACTIVITY_NEW_EXPERT_VOTE' ? (
            <View
              style={[
                styles.badgeStyle,
                {
                  backgroundColor: handleBackgroundColor(item?.vote?.decision),
                },
              ]}>
              <Text style={styles.badgeText}>
                Voted {item?.vote?.decision === 'REAL' ? 'Pass' : 'Fail'}
              </Text>
            </View>
          ) : null}

          {item?.vote?.appraisal_value && item?.vote?.decision !== 'FAKE' ? (
            <View style={styles.appraisalBadge}>
              <Text style={styles.badgeText}>
                ${item?.vote?.appraisal_value}
              </Text>
            </View>
          ) : null}
        </View>
        <View style={styles.textBar}>
          <Text numberOfLines={1} style={styles.titleStyle}>
            {item?.post?.title}
          </Text>
          {item?.__typename === 'FEED_ACTIVITY_NEW_VOTE' ? (
            <Text style={styles.timeStyle}>
              {dayjs(item?.vote?.created_at).fromNow()}
            </Text>
          ) : (
            <Text style={styles.timeStyle}>
              {dayjs(
                item?.post?.approved_at ?? item?.post?.created_at,
              ).fromNow()}
            </Text>
          )}
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  cardsGroup: {
    width: CARD_WIDTH,
    marginBottom: spacings.lg,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: colors.lightGrey,
  },
  imageStyle: {
    width: '100%',
    height: CARD_WIDTH / 1.4,
    borderRadius: 2,
  },
  profileImageStyle: {
    height: 20,
    width: 20,
    borderRadius: 20,
  },
  userBar: {
    flexDirection: 'row',
    paddingVertical: spacings.sm,
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
  },
  textBar: {
    width: '100%',
    paddingBottom: spacings.md,
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  userStyle: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
    marginLeft: spacings.sm,
    textTransform: 'lowercase',
  },
  timeStyle: {
    fontSize: fontSize.sm,
    color: colors.darkGrey,
  },
  voteBadge: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  appraisalBadge: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
    backgroundColor: colors.darkBadge,
  },
  badgeStyle: {
    position: 'absolute',
    right: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  badgeText: {
    fontSize: fontSize.xs,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: spacings.sm,
    textTransform: 'capitalize',
  },
});

export default UserActivityCard;
