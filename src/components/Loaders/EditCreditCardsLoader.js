/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const EditCreditCardsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.3}
          height={10}
          marginBottom={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.9}
          marginBottom={20}
          height={50}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          width={WIDTH}
          height={3}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item marginBottom={20}>
          <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
            <SkeletonPlaceholder.Item
              width={15}
              height={15}
              marginBottom={10}
              marginRight={5}
              borderRadius={15}
            />
            <SkeletonPlaceholder.Item
              width={WIDTH * 0.3}
              height={10}
              marginBottom={10}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            width={WIDTH}
            height={3}
            borderRadius={4}
            marginVertical={20}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            height={10}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={10}
            marginBottom={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            height={10}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={10}
            marginBottom={20}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.4}
            height={10}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.3}
            height={10}
            marginBottom={10}
            borderRadius={4}
          />
          <SkeletonPlaceholder.Item
            width={WIDTH}
            height={3}
            borderRadius={4}
            marginVertical={20}
          />
        </SkeletonPlaceholder.Item>

        <SkeletonPlaceholder.Item marginBottom={20}>
          <SkeletonPlaceholder.Item
            width={WIDTH * 0.9}
            height={50}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default EditCreditCardsLoader;
