import React, {useState, useCallback} from 'react';
import {Text, View, Keyboard} from 'react-native';
import {useLazyQuery} from '@apollo/client';
import dayjs from 'dayjs';
import {SafeAreaView} from 'react-native-safe-area-context';
import {FlatList} from 'react-native-gesture-handler';
import {useIsFocused} from '@react-navigation/native';

import styles from './styles';
import Icon from '../../components/Icon';
import SearchLoader from '../../components/Loaders/SearchLoader';
import {GET_ALL_POSTS} from '../../apollo/queries';
import {fontSize} from '../../assets/font';
import {icons, images, screens} from '../../assets/strings';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import FilterButtons from './FilterButtons';
import {useUser} from '../../tools/hooks';
import locale from '../../assets/locale.json';
import RefreshControl from '../../components/RefreshControl';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import ImageViewer from '../../components/ImageViewer';
import Image from '../../components/FastImage';
import {fixedNumberTo} from '../../tools/utils';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import Users from './Users';

var relativeTime = require('dayjs/plugin/relativeTime');
dayjs.extend(relativeTime);

export const FILTER_TYPE = Object.freeze({
  AuthenticationType: 'AuthenticationType',
  Material: 'Material',
  Stitch: 'Stitch',
  Decade: 'Decade',
  Verdict: 'Verdict',
  Brand: 'Brand',
  Size: 'Size',
  Category: 'Category',
});

const TShirts = ({navigation, term = ''}) => {
  const [selectedBrand, setSelectedBrand] = useState([]);
  const [selectedVerdict, setSelectedVerdict] = useState(null);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedStitching, setSelectedStitching] = useState(null);
  const [selectedMaterial, setSelectedMaterial] = useState(null);
  const [selectedDecade, setSelectedDecade] = useState(null);
  const [selectedSize, setSelectedSize] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [searchInput, setSearchInput] = React.useState(term ?? '');
  const [modalVisible, setModalVisible] = useState(false);
  const [tagImages, setTagImages] = useState(null);
  const [refreshing, setRefreshing] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const isFocused = useIsFocused();
  const {user: userData} = useUser({focused: isFocused});

  const debounceId = React.useRef();
  const firstRef = React.useRef(true);

  const [startSearch, {data: postsData, refetch}] = useLazyQuery(
    GET_ALL_POSTS,
    {
      variables: {
        limit: 100, // TODO: Pagination
        filters: {
          ...(searchInput?.length > 0 ? {term: searchInput} : {}),
          ...(selectedBrand?.length > 0
            ? {brand_ids: selectedBrand?.map(brand => +brand?.id)}
            : {}),
          ...(selectedStitching == null
            ? {}
            : {stitching_ids: [selectedStitching?.id]}),
          ...(selectedType == null
            ? {include_tag_authentications: true}
            : {authenticate_type: selectedType?.id}),
          ...(selectedDecade == null ? {} : {from_decades: selectedDecade?.id}),
          ...(selectedMaterial == null
            ? {}
            : {material_ids: [selectedMaterial?.id]}),
          ...(selectedSize == null ? {} : {size_ids: [selectedSize?.id]}),
          ...(selectedCategory == null
            ? {}
            : {category_ids: [selectedCategory?.id]}),
          ...(selectedVerdict == null ? {} : {decision: selectedVerdict?.id}),
        },
        cacheId: APOLLO_CACHE_IDS.getPosts_SEARCH,
      },
      fetchPolicy: 'network-only',
    },
  );

  const handleTextChange = React.useCallback(
    async (value, forceUpdate = false) => {
      if (forceUpdate || isFocused) {
        setLoading(true);
        if (!(value == null)) {
          setSearchInput(value);
        }
        if (debounceId.current) {
          clearTimeout(debounceId.current);
        }
        debounceId.current = setTimeout(async () => {
          try {
            await startSearch();
          } catch (_) {
          } finally {
            setLoading(false);
          }
        }, 300);
      }
    },
    [startSearch, isFocused],
  );

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  React.useEffect(() => {
    if (!firstRef.current) {
      handleTextChange(term);
    }
    if (firstRef.current) {
      firstRef.current = false;
    }
    return () => {
      clearTimeout(debounceId.current);
    };
  }, [term, handleTextChange]);

  const isSomeFilterApplied = React.useMemo(() => {
    const _isSomeFilterApplied =
      [
        selectedVerdict,
        selectedType,
        selectedStitching,
        selectedMaterial,
        selectedDecade,
        selectedSize,
        selectedCategory,
      ].some(item => !(item == null)) || selectedBrand?.length > 0;
    (async () => {
      if (_isSomeFilterApplied) {
        handleTextChange(null, true);
      }
    })();
    return _isSomeFilterApplied;
  }, [
    selectedBrand,
    selectedVerdict,
    selectedType,
    selectedStitching,
    selectedMaterial,
    selectedDecade,
    selectedSize,
    selectedCategory,
    handleTextChange,
  ]);

  const filterComposer = (item, _filterType) => {
    if (_filterType !== FILTER_TYPE.Brand) {
      if (item?.id == 'All') {
        item = null;
      }
    }

    switch (_filterType) {
      case FILTER_TYPE.Brand:
        setSelectedBrand(item);
        break;

      case FILTER_TYPE.Category:
        setSelectedCategory(previousState =>
          previousState == null
            ? item
            : previousState?.id == item?.id
            ? null
            : item,
        );
        break;
      case FILTER_TYPE.Material:
        setSelectedMaterial(previousState =>
          previousState == null
            ? item
            : previousState?.id == item?.id
            ? null
            : item,
        );
        break;
      case FILTER_TYPE.Decade:
        setSelectedDecade(previousState =>
          previousState == null
            ? item
            : previousState?.id == item?.id
            ? null
            : item,
        );
        break;
      case FILTER_TYPE.Stitch:
        setSelectedStitching(previousState =>
          previousState == null
            ? item
            : previousState?.id == item?.id
            ? null
            : item,
        );
        break;
      case FILTER_TYPE.AuthenticationType:
        setSelectedType(previousState =>
          previousState == null
            ? item
            : previousState?.id == item?.id
            ? null
            : item,
        );
        break;
      case FILTER_TYPE.Verdict:
        setSelectedVerdict(previousState =>
          previousState == null
            ? item
            : previousState?.id == item?.id
            ? null
            : item,
        );
        break;
      case FILTER_TYPE.Size:
        setSelectedSize(previousState =>
          previousState == null
            ? item
            : previousState?.id == item?.id
            ? null
            : item,
        );
        break;
      default:
        break;
    }
    searchInput?.length > 0 && handleTextChange(searchInput, true);
  };

  const handleNavigation = value => {
    if (value?.authentication_type?.id === 4) {
      let images = galleryImages(value?.images);
      setTagImages(images);
      setModalVisible(true);
      return;
    } else {
      navigation.navigate(screens.SINGLE_LISTING, {params: value?.id});
    }
  };

  const galleryImages = useCallback(imageItem => {
    let objs = [];
    let loopedItems = [];
    let index = 0;
    if (imageItem) {
      for (const [key, value] of Object.entries(imageItem)) {
        if (
          !loopedItems.includes(key) &&
          !(value == null) &&
          value?.length > 0 &&
          ['front_tag', 'back_tag'].includes(key)
        ) {
          objs.push({
            url: value,
            key: key,
            id: index,
          });
          loopedItems.push(key);
          index += 1;
        }
      }
    }
    return objs;
  }, []);

  const handleColor = useCallback(
    item => {
      switch (item) {
        case 'Brand':
          return selectedBrand;
        case 'Material':
          return selectedMaterial;
        case 'Decade':
          return selectedDecade;
        case 'Stitch':
          return selectedStitching;
        case 'AuthenticationType':
          return selectedType;
        case 'Size':
          return selectedSize;
        case 'Category':
          return selectedCategory;
        default:
          return;
      }
    },
    [
      selectedBrand,
      selectedDecade,
      selectedMaterial,
      selectedStitching,
      selectedType,
      selectedSize,
      selectedCategory,
    ],
  );

  const handleIconColor = useCallback(item => {
    switch (item) {
      case 'REAL':
        return colors.greenBadge;
      case 'FAKE':
        return colors.redBadge;
      case 'UNDECIDED':
        return colors.darkBadge;
      case 'UNDETERMINED':
        return colors.amber;
      default:
        return colors.darkBadge;
    }
  }, []);

  const handleDecision = useCallback(item => {
    switch (item) {
      case 'REAL':
        return 'Pass';
      case 'FAKE':
        return 'Fail';
      case 'UNDECIDED':
        return 'Pending';
      case 'UNDETERMINED':
        return 'Undetermined';
      default:
        return 'Pending';
    }
  }, []);

  const renderItem = ({item}) => {
    return (
      <TouchAwareButton
        style={[
          styles.resultsCard,
          {flexDirection: 'row', justifyContent: 'space-between'},
        ]}
        onPress={() => handleNavigation(item)}>
        <View style={styles.textSection}>
          <Text
            style={[styles.titleStyles, {marginRight: 5}]}
            numberOfLines={2}>
            {item?.title}
          </Text>
          {item?.authentication_type?.id === 3 ||
          item?.authentication_type?.id === 2 ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: spacings.md,
              }}>
              <Icon
                name={icons.CHEVRON_CIRCLE}
                size={15}
                tint={colors.secondary}
              />
              <Text
                numberOfLines={3}
                style={{
                  color: handleIconColor(
                    item?.authentication_result?.expert_result?.decision,
                  ),
                  fontSize: fontSize.sm,
                  alignItems: 'center',
                  fontWeight: 'bold',
                }}>
                <Text
                  style={{
                    color: colors.primary,
                    fontWeight: 'bold',
                  }}>
                  Experts voted:{' '}
                </Text>
                {item?.authentication_result?.decision
                  ? handleDecision(
                      item?.authentication_result?.expert_result?.decision,
                    )
                  : 'Pending'}
              </Text>
            </View>
          ) : null}
          {item?.authentication_result?.expert_result?.decision === 'REAL' ? (
            <Text
              style={{
                color: colors.black,
                fontSize: fontSize.sm,
                marginTop: spacings.sm,
              }}>
              Appraisal:{' '}
              <Text
                style={{
                  color: colors.black,
                  fontSize: fontSize.sm,
                  fontWeight: 'bold',
                }}>
                $
                {fixedNumberTo(
                  item?.authentication_result?.expert_result?.appraisal_value,
                )}
              </Text>
            </Text>
          ) : null}

          {item?.authentication_type?.id === 1 ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginVertical: spacings.md,
              }}>
              <Icon name={icons.CHEVRON_CIRCLE} size={15} tint={colors.grey} />
              <Text
                style={{
                  color: colors.black,
                  fontSize: fontSize.sm,
                  alignItems: 'center',
                  fontFamily: 'Inter-Medium',
                }}>
                No Experts Voted
              </Text>
            </View>
          ) : item?.authentication_type?.id === 4 ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: spacings.md,
              }}>
              <Icon
                name={icons.CHEVRON_CIRCLE}
                size={15}
                tint={colors.secondary}
              />
              <Text
                numberOfLines={3}
                style={{
                  color: handleIconColor(item?.authentication_result?.decision),
                  fontSize: fontSize.sm,
                  alignItems: 'center',
                  fontWeight: 'bold',
                }}>
                <Text
                  style={{
                    color: colors.primary,
                    fontWeight: 'bold',
                  }}>
                  Experts voted:{' '}
                </Text>
                {item?.authentication_result?.decision !== 'UNDECIDED'
                  ? handleDecision(item?.authentication_result?.decision)
                  : 'Pending'}
              </Text>
            </View>
          ) : null}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flexWrap: 'wrap',
              marginVertical: 5,
            }}>
            <Text
              style={{
                color: colors.darkGrey,
                textTransform: 'capitalize',
                fontSize: 10,
                marginRight: spacings.sm,
              }}>
              {dayjs(item?.created_at).fromNow()}
            </Text>
            {userData?.id === item?.user?.id ? (
              <Text
                style={{
                  backgroundColor: colors.black,
                  fontSize: 9,
                  paddingHorizontal: spacings.sm,
                  borderRadius: 2,
                  paddingVertical: spacings.sm,
                  color: colors.white,
                  textTransform: 'uppercase',
                  fontFamily: 'Inter-Regular',
                }}>
                Your{'  '}item
              </Text>
            ) : null}
          </View>
        </View>
        <View style={styles.imageSection}>
          <Image
            style={styles.searchImage}
            source={
              item?.images?.closeup_front ||
              item?.images?.featured ||
              item?.images?.front_tag
                ? {
                    uri:
                      item?.images?.featured ||
                      item?.images?.closeup_front ||
                      item?.images?.front_tag,
                  }
                : images.PLACEHOLDER
            }
            resizeMode="cover"
          />
          {item?.authentication_type?.id === 1 ? (
            <>
              <View
                style={{
                  position: 'absolute',
                  flexDirection: 'row',
                  alignItems: 'center',
                  top: 0,
                  right: 0,
                  padding: spacings.sm,
                  backgroundColor: handleIconColor(
                    handleColor(
                      item?.authentication_result?.community_result?.decision,
                    ),
                  ),
                }}>
                <Icon
                  name={icons.PEOPLE_OUTLINE}
                  size={12}
                  tint={colors.white}
                />
                <Text style={[styles.voteBadge, {textTransform: 'capitalize'}]}>
                  {handleDecision(
                    item?.authentication_result?.community_result?.decision,
                  )}{' '}
                  {item?.authentication_result?.community_result?.score > 0
                    ? `(${fixedNumberTo(
                        item?.authentication_result?.community_result?.score,
                      )})%`
                    : null}
                </Text>
              </View>
              {item?.authentication_result?.community_result?.decision ==
              'REAL' ? (
                <View
                  style={{
                    position: 'absolute',
                    flexDirection: 'row',
                    alignItems: 'center',
                    left: 0,
                    bottom: 0,
                    padding: spacings.sm,
                    backgroundColor: handleIconColor(
                      item?.authentication_result?.community_result?.decision,
                    ),
                  }}>
                  <Text
                    style={[styles.voteBadge, {fontFamily: 'Inter-Medium'}]}>
                    Avg est:{' '}
                    {item?.appraisal_value
                      ? `$${fixedNumberTo(item?.appraisal_value)}`
                      : 'Pending'}
                  </Text>
                </View>
              ) : null}
            </>
          ) : null}

          {item?.my_vote ? (
            <View
              style={{
                position: 'absolute',
                flexDirection: 'row',
                alignItems: 'center',
                bottom: 0,
                right: 0,
                padding: spacings.sm,
                backgroundColor: colors.darkBadge,
              }}>
              <Icon
                name={
                  item?.my_vote?.decision === 'REAL'
                    ? icons.PASS_ICON
                    : icons.CANCEL_X
                }
                size={13}
                tint={
                  item?.my_vote?.decision === 'REAL'
                    ? colors.green
                    : colors.warning
                }
              />
            </View>
          ) : null}
        </View>
      </TouchAwareButton>
    );
  };

  const renderInlineUsers = React.useMemo(
    () => <Users term={term} inlineMode />,
    [term],
  );

  const posts = postsData?.getPosts?.data?.edges;

  return (
    <View style={[styles.container, {backgroundColor: colors.black}]}>
      <ImageViewer
        visible={modalVisible}
        setVisible={setModalVisible}
        images={tagImages?.map(image => ({...image, uri: image?.url}))}
      />
      <FilterButtons
        selectedType={selectedType}
        selectedMaterial={selectedMaterial}
        selectedDecade={selectedDecade}
        selectedStitching={selectedStitching}
        selectedBrand={selectedBrand}
        handleRowSelection={filterComposer}
        selectedVerdict={selectedVerdict}
        selectedSize={selectedSize}
        selectedCategory={selectedCategory}
      />
      <FlatList
        keyboardShouldPersistTaps="always"
        data={searchInput?.length > 0 || isSomeFilterApplied ? posts : []}
        keyExtractor={item => item?.id?.toString?.()}
        renderItem={renderItem}
        ListHeaderComponent={
          <>
            {renderInlineUsers}
            {posts?.length > 0 ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 20,
                  paddingTop: 10,
                }}>
                <Text
                  style={[
                    styles.titleStyles,
                    {
                      fontSize: 20,
                      lineHeight: undefined,
                    },
                  ]}>
                  {locale.Shirts}
                </Text>
              </View>
            ) : null}
          </>
        }
        ListEmptyComponent={
          <View style={{flex: 1}}>
            {!loading ? (
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: colors.white,
                  flex: 1,
                }}>
                {searchInput?.length > 0 || isSomeFilterApplied ? (
                  Array.isArray(posts) && posts?.length == 0 ? (
                    <DefaultEmptyComponent
                      text={locale.NoPostsFound}
                      lottieStyle={{
                        width: 100,
                        height: 100,
                        alignSelf: 'center',
                      }}
                      textStyle={{marginTop: -10}}
                    />
                  ) : null
                ) : (
                  <Text
                    style={{color: colors.black, fontFamily: 'Inter-Medium'}}>
                    {locale.TypeSomething}
                  </Text>
                )}
              </View>
            ) : (
              <SearchLoader />
            )}
          </View>
        }
        style={{backgroundColor: colors.white}}
        contentContainerStyle={
          (searchInput?.length > 0 || isSomeFilterApplied) &&
          Array.isArray(posts) &&
          posts?.length > 0
            ? {}
            : {flex: 1}
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={
              searchInput?.length > 0 || isSomeFilterApplied
                ? onRefresh
                : () => null
            }
          />
        }
        onScroll={() => {
          Keyboard.dismiss();
        }}
      />
    </View>
  );
};

export default TShirts;
