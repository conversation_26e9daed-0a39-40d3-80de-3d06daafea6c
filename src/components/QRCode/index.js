import React from 'react';
import {View, Platform, TouchableOpacity, Text} from 'react-native';
import QR from 'react-native-qrcode-svg';
import RNFetchBlob from 'rn-fetch-blob';
import Share from 'react-native-share';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';

import Button from '../Button';
import colors from '../../assets/colors';
import {showToast} from '../Toast';
import locale from '../../assets/locale.json';
import checkPermission from '../../tools/permissions';
import { permissionStorage } from '../../tools/utils';
import RNFS from 'react-native-fs';
import ActivityIndicator from '../ActivityIndicator';
import styles from './styles';

const isIOS = Platform.OS === 'ios';

export default function QRCode({url, size = 150, download = true, name = ''}) {
  const permissionRef = React.useRef(false);
  //const svgRef = React.useRef();
  const [permissionGranted, setPermissionGranted] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false); // Manage loading state

  async function downloadImage(qrCodeURL, filename) {
    
    const isIOS = Platform.OS == 'ios';
    // const downloadDest = isIOS ? `${RNFS.DocumentDirectoryPath}/${filename}` 
    // : `file://${RNFS.DocumentDirectoryPath}/${filename}`;
    // console.log(downloadDest);

    try {
      const response = await fetch(qrCodeURL);
      const blob = await response.blob();
      const base64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]); // Remove the data URL prefix
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
  
      //await RNFS.writeFile(downloadDest, base64, 'base64');
      return base64;
    } catch (error) {
      console.error('Error downloading image:', error);
      throw error;
    }
  }

  const handleDownload = React.useCallback(async () => {
    
    const qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data='+url;

    
    try {
      setIsLoading(true); // Start loading
      if (!permissionGranted) {
        permissionRef.current = true;
        await checkPermission(
          permissionStorage,
          setPermissionGranted,
          true,
          true,
          false,
        );
      } else {
        const filename = `image_code.jpg`;
        const base64Data = await downloadImage(qrCodeUrl, filename);
        console.log("URL======ssss>", base64Data);

        if (isIOS) {
          await Share.open({
            //url: `data:image/png;base64,${svgRef.current}`,            
            url: `data:image/png;base64,${base64Data}`,            
            failOnCancel: false,
          });
        } else {
          const fs = RNFetchBlob.fs;
          const PictureDir = fs.dirs.CacheDir;
          const path = `${PictureDir}/image_${+new Date()}.png`;
          //await fs.writeFile(path, svgRef.current, 'base64');
          await fs.writeFile(path, base64Data, 'base64');
          await CameraRoll.save(path, 'photo');
          showToast({type: 'success', message: locale.QrCodeSaved});
        }
      }
    } catch (_) {
      showToast({message: locale['SomethingWentWrong.']});
    } finally {
      setIsLoading(false); // Stop loading

    }
  }, [name, permissionGranted]);

  React.useEffect(() => {
    if (permissionGranted && permissionRef.current) {
      handleDownload();
      permissionRef.current = false;
    }
  }, [permissionGranted]);

  const handleQrCodeSVGUrl = React.useCallback(base64URL => {
    svgRef.current = base64URL;
  }, []);

  return (
    <View>
      <QR
        quietZone={20}
        value={url}
        size={size}
        getRef={svg => {
          if (download) {
            //svg?.toDataURL?.(handleQrCodeSVGUrl);
          }
        }}
      />
      {download ? (
        // <Button
        //   text="Save"
        //   style={{marginTop: 10, backgroundColor: colors.black}}
        //   onPress={handleDownload}
        // />
        <TouchableOpacity style={styles.button} onPress={handleDownload}>

          {isLoading ? (

            <ActivityIndicator color={colors.white} />

          ) : (

            <Text style={styles.buttonText}>
              {"Save"}
            </Text>

          )}

        </TouchableOpacity>    
      ) : null}
    </View>
  );
}
