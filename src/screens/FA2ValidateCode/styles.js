/* eslint-disable prettier/prettier */
import {StyleSheet, Dimensions, Platform} from 'react-native';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

const {width: WIDTH} = Dimensions.get('window');

const isIOS = Platform.OS == 'ios';

const styles = StyleSheet.create({
  container: {
    width: WIDTH,
    backgroundColor: colors.white,
    flex: 1,
    alignItems: 'center',
  },
  otpField: {
    width: WIDTH * 0.9,
    height: 20,
  },
  underlineStyleBase: {
    width: 45,
    height: 45,
    borderWidth: 1,
    borderColor: colors.grey,
    color: colors.black,
  },
  underlineStyleHighLighted: {
    borderColor: colors.black,
  },
  titleWrapper: {
    width: WIDTH * 0.9,
    marginTop: spacings.lg,
  },
  titleStyle: {
    fontSize: fontSize.lg,
    fontFamily: 'Gugi-Regular',
    color: colors.black,
  },
  inputLabel: {
    width: '100%',
    fontSize: fontSize.md,
    color: colors.black,
    marginBottom: 5,
  },
  errorText: {
    fontSize: 9,
    color: colors.warning,
    width: '100%',
    position: 'absolute',
  },
  errorWrapper: {
    width: WIDTH * 0.9,
    marginTop: 5,
  },
  inputStyles: {
    borderWidth: 1,
    width: WIDTH * 0.9,
    borderRadius: 4,
    borderColor: colors.grey,
    color: colors.black,
    paddingLeft: spacings.md,
    // ADDED
    ...(isIOS ? {padding: 15} : {}),
  },
  label: {
    fontSize: fontSize.md,
    color: colors.black,
    marginBottom: 5,
  },
  inputWrapper: {},
  button: {
    marginTop: spacings.xl,
    height: 50,
    backgroundColor: colors.primary,
    width: WIDTH * 0.9,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
});
export default styles;
