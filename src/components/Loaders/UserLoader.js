/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
const Loader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item flexDirection="row">
          <SkeletonPlaceholder.Item
            width={35}
            height={35}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item justifyContent="space-around">
            <SkeletonPlaceholder.Item width={60} height={7} borderRadius={4} />
            <SkeletonPlaceholder.Item width={80} height={7} borderRadius={4} />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.black,
    paddingTop: spacings.md,
  },
});

export default Loader;
