/* eslint-disable prettier/prettier */
import {
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Text,
  View,
} from 'react-native';
import React, {useCallback, useState} from 'react';
import {BlurView} from '@react-native-community/blur';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import Icon from '../Icon';
import {icons} from '../../assets/strings';
import {fontSize} from '../../assets/font';

const {width, height} = Dimensions.get('screen');

const Picker = ({items, setValue, value}) => {
  const [pickerVisible, setPickerVisible] = useState(false);
  const [blurBackground, setBlurBackground] = useState(false);

  const handleOptionSelection = useCallback(
    item => {
      setValue(item);
      setBlurBackground(false);
      setPickerVisible(false);
    },
    [setValue],
  );

  const togglePicker = useCallback(() => {
    setBlurBackground(!blurBackground);
    setPickerVisible(!pickerVisible);
  }, [blurBackground, pickerVisible]);

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.pickerInput} onPress={togglePicker}>
        <Text style={{color: value ? colors.black : colors.darkGrey}}>
          {value ? value?.name : '- select -'}
        </Text>
        <Icon
          name={icons.CHEVRON_DOWN}
          tint={colors.darkGrey}
          size={fontSize.sm}
        />
      </TouchableOpacity>
      <TouchableOpacity
        activeOpacity={1}
        style={{flex: 1}}
        onPress={togglePicker}>
        {blurBackground && (
          <BlurView
            blurType="dark"
            blurAmount={1}
            reducedTransparencyFallbackColor="white"
            style={StyleSheet.absoluteFillObject}
          />
        )}
        {pickerVisible ? (
          <View style={styles.modalStyles}>
            <FlatList
              data={items}
              keyExtractor={item => item?.id?.toString()}
              showsVerticalScrollIndicator={false}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.buttonStyles}
                  onPress={() => handleOptionSelection(item)}>
                  <Text style={styles.pickerText}>{item?.name}</Text>
                  {value?.id === item.id ? (
                    <Icon
                      name={icons.CHECKED_RADIO}
                      tint={colors.primary}
                      size={fontSize.xxxl}
                    />
                  ) : (
                    <Icon
                      name={icons.UNCHECKED_RADIO}
                      tint={colors.grey}
                      size={fontSize.xxxl}
                    />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        ) : null}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: width,
    backgroundColor: colors.white,
  },
  pickerText: {
    color: colors.black,
    fontSize: fontSize.lg,
  },
  modalStyles: {
    position: 'absolute',
    bottom: 0,
    width: width,
    flex: 1,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    backgroundColor: colors.white,
    height: height / 2.8,
    paddingTop: spacings.lg,
  },
  buttonStyles: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacings.md,
    marginBottom: spacings.md,
    paddingHorizontal: spacings.lg,
  },
  pickerInput: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    borderWidth: 1,
    borderColor: colors.grey,
    marginTop: spacings.lg,
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.md,
    borderRadius: 4,
  },
});

export default Picker;
