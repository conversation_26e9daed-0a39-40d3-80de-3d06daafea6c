import React, { useState, useCallback } from 'react';
import {Text, View} from 'react-native';

import Icon from '../../components/Icon';
import {singleListingScreen} from '../../assets/strings';
import {styles} from './styles';
import {fontSize} from '../../assets/font';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import locale from '../../assets/locale.json';
import {fixedNumberTo} from '../../tools/utils';

const DescriptionCard = ({data, provenanceTaggingComponent = null}) => {

  const [textShown, setTextShown] = useState(false); //To show ur remaining Text
  const [lengthMore, setLengthMore] = useState(false); //to show the "Read more & Less Line"

  const toggleNumberOfLines = () => { //To toggle the show text or hide it
    setTextShown(!textShown);
  }

  const onTextLayout = useCallback(e => {
    setLengthMore(e.nativeEvent.lines.length >= 11); //to check the text is more than 4 lines or not
    // console.log(e.nativeEvent);
  }, []);

  const isAuthenticationPending = React.useMemo(
    () => data?.getPost?.data?.authentication_status === 'PENDING',
    [data?.getPost?.data?.authentication_status],
  );

  const isPass = React.useMemo(
    () =>
      data?.getPost?.data?.authentication_result?.expert_result?.decision ===
      'REAL',
    [data?.getPost?.data?.authentication_result?.expert_result?.decision],
  );

  return (
    <View style={styles.descriptionContainer}>
      <View
        style={[
          styles.descTitleWrapper,
          {flexDirection: 'row', alignItems: 'center'},
        ]}>
        <Text style={[styles.descTitle, {marginRight: 10}]}>
          {singleListingScreen.DESCRIPTION_HEADER}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>{singleListingScreen.BRAND}</Text>
        </View>
        <Text style={styles.descValue}>{data?.getPost?.data?.brand?.name}</Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>{singleListingScreen.PIT_TO_PIT}</Text>
        </View>
        <Text style={styles.descValue}>
          {data?.getPost?.data?.pit_to_pit_measurement?.toString?.()?.length
            ? `${fixedNumberTo(data?.getPost?.data?.pit_to_pit_measurement)}''`
            : 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>
            {singleListingScreen.REAR_COLLAR}
          </Text>
        </View>

        <Text style={styles.descValue}>
          {data?.getPost?.data?.collar_to_bottom_measurement?.toString?.()
            ?.length
            ? `${fixedNumberTo(
              data?.getPost?.data?.collar_to_bottom_measurement,
            )}''`
            : 'N/A'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={[styles.descLabel]}>
            {singleListingScreen.LABEL_SIZE}
          </Text>
        </View>

        <Text style={[styles.descValue]}>
          {data?.getPost.data?.size?.name ?? ''}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>{singleListingScreen.MATERIAL}</Text>
        </View>

        <Text style={styles.descValue}>
          {data?.getPost?.data?.material?.name
            ? data?.getPost?.data?.material?.name
            : 'Cotton'}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>{singleListingScreen.STITCHING}</Text>
        </View>

        {data?.getPost?.data?.stitching ? (
          <Text style={styles.descValue}>
            {data?.getPost?.data?.stitching?.name}
          </Text>
        ) : null}
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>{singleListingScreen.CATEGORY}</Text>
        </View>

        <Text style={styles.descValue}>
          {data?.getPost?.data?.category?.name}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>{singleListingScreen.DECADE}</Text>
        </View>

        <Text style={[styles.descValue, {textTransform: 'none'}]}>
          {data?.getPost?.data?.from_decade?.name}
        </Text>
      </View>
      <View style={styles.descCardWrapper}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {isPass ? (
            <Icon
              name="checkmark"
              size={20}
              tint={isAuthenticationPending ? colors.grey : colors.greenBadge}
              style={{marginRight: 10}}
            />
          ) : null}
          <Text style={styles.descLabel}>{singleListingScreen.CONDITION}</Text>
        </View>

        <Text style={styles.descValue}>
          {data?.getPost?.data?.condition?.name}
        </Text>
      </View>

      <View style={styles.altDescCardWrapper}>
        <Text style={styles.altDescLabel}>{locale.RNNumber}</Text>
        <Text style={styles.descLabel}>
          {!(data?.getPost?.data?.rn_number == null)
            ? data?.getPost?.data?.rn_number
            : 'N/A'}
        </Text>
      </View>

      <View style={styles.altDescCardWrapper}>
        <Text style={styles.altDescLabel}>
          {singleListingScreen.CONDITION_DESCRIPTION}
        </Text>
        <Text style={styles.descLabel}>
          {data?.getPost?.data?.condition_description?.length
            ? data?.getPost?.data?.condition_description
            : 'N/A'}
        </Text>
      </View>
      <View style={styles.altDescCardWrapper}>
        <Text style={styles.altDescLabel}>
          {singleListingScreen.PROVENANCE}
        </Text>
        <Text style={styles.descLabel}>
          {data?.getPost?.data?.provenance?.length
            ? data?.getPost?.data?.provenance
            : 'N/A'}
        </Text>
        {provenanceTaggingComponent}
      </View>
      {data?.getPost?.data?.authentication_result?.expert_result?.decision !==
        'UNDECIDED' && !isAuthenticationPending ? (
        <View style={styles.altDescCardWrapper}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingTop: spacings.lg,
              paddingBottom: spacings.sm,
              borderTopWidth: 3,
              borderTopColor: colors.lightGrey,
            }}>
            <Text
              style={{
                marginRight: spacings.md,
                color: colors.black,
                fontSize: fontSize.lg,
                fontFamily: 'Gugi-Regular',
              }}>
              Expert's Summary
            </Text>
            {isPass ? (
              <Icon name="expertCertified" size={20} tint={colors.secondary} />
            ) : null}
          </View>
          <Text
            onTextLayout={onTextLayout}
            numberOfLines={textShown ? undefined : 11}
            style={styles.descLabel}
          >
            {data?.getPost?.data?.authentication_result?.expert_result?.comment}
          </Text>
          {
            lengthMore ? <Text
              onPress={toggleNumberOfLines}
              style={{ 
                color: colors.primary,
                fontSize: 16,
                fontFamily: 'Inter-Bold',
                alignItems: 'center', marginTop: 10 }}>{textShown ? 'see less...' : 'see more...'}</Text>
              : null
          }
        </View>
      ) : null}
    </View>
  );
};

export default DescriptionCard;
