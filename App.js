import 'react-native-gesture-handler';
import React, {useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ApolloProvider} from '@apollo/client';
import {ApolloProvider as ApolloProviderClient} from '@apollo/react-hooks';
import {Provider} from 'react-redux';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {PersistGate} from 'redux-persist/integration/react';
import Toast from 'react-native-toast-message';
import {NavigationContainer} from '@react-navigation/native';
import notifee, {EventType} from '@notifee/react-native';
import {ToastProvider} from 'react-native-toast-notifications';
import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {enableScreens} from 'react-native-screens';

import AppNavigator from './src/navigators/AppNavigator';
import {persistent} from './src/assets/strings';
import {getNotificationDynamicScreensKeys} from './src/tools/services/NotificationNavigationService';
import {localNotificationService} from './src/tools/services/LocalNotificationService';
import {fcmService} from './src/tools/services/FCMService';
import {navigationRef, isReadyRef} from './src/navigators/NavigationService';
import configureStore from './src/redux';
import {useApolloClient} from './src/apollo';
import toastConfig from './src/tools/toastConfig';
import CustomBootSplash from './src/components/CustomBootsplash';
import colors from './src/assets/colors';
import linking from './src/navigators/deepLinks/linking';
import {withAppDetails} from './src/components/HOC';

enableScreens(true);

const {store, persistor} = configureStore();

function App() {
  const {client} = useApolloClient();

  useEffect(() => {
    try {
      fcmService.configure({
        onRegister: localNotificationService.onRegisterListener.bind(
          localNotificationService,
        ),
        onNotification: localNotificationService.onNotificationListener.bind(
          localNotificationService,
        ),
        onOpenNotification: localNotificationService.onNotificationOpenedListener.bind(
          localNotificationService,
        ),
      });
    } catch (error) {}
  }, []);

  useEffect(() => {
    const unsubscribe = notifee.onForegroundEvent(({type, detail}) => {
      switch (type) {
        case EventType.PRESS:
          localNotificationService.onNotificationOpenedListener(
            detail?.notification,
          );
          break;
      }
    });
    return () => {
      unsubscribe();
    };
  }, []);

  const navigationReady = React.useCallback(async () => {
    // In Debug mode when the app is refreshed or hot reload works, this might be called from previous notification from dead state. So that it will again navigate to previous notification mapped screen. This wont occur in release since there's no such thing as refresh or hot reload in release
    try {
      AsyncStorage.multiRemove(
        getNotificationDynamicScreensKeys().map(item => item),
      );

      const value = await AsyncStorage.getItem(
        persistent.TEMPORARY_NAVIGATION_STORE,
      );

      if (value?.length) {
        await AsyncStorage.removeItem(persistent.TEMPORARY_NAVIGATION_STORE);
        const parsed = JSON.parse(value);
        if (parsed?.name?.length) {
          navigationRef.current?.navigate?.(parsed);
        }
      }
    } catch (error) {
      // Log to server
    }
  }, []);

  // Cleanup on every app start
  React.useEffect(() => {
    (async function () {
      try {
        await AsyncStorage.setItem(persistent.CAN_FORCE_LOGOUT, '1');
      } catch (_) {}
    })();
  }, []);

  if (!client) {
    return null;
  }

  return (
    <ApolloProvider client={client}>
      <ApolloProviderClient client={client}>
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <SafeAreaProvider style={{backgroundColor: colors.black}}>
              <ToastProvider
                successColor="green"
                dangerColor="red"
                warningColor="orange"
                normalColor="gray"
                swipeEnabled={true}>
                <GestureHandlerRootView style={{flex: 1}}>
                  <CustomBootSplash>
                    {({handleWhenNavigationIsReady = () => null}) => (
                      <>
                        <BottomSheetModalProvider>
                          <NavigationContainer
                            ref={navigationRef}
                            onReady={() => {
                              handleWhenNavigationIsReady();
                              isReadyRef.current = true;
                              navigationReady();
                            }}
                            theme={{
                              colors: {
                                background: colors.black,
                              },
                            }}
                            linking={linking}>
                            <AppNavigator />
                            {React.createElement(withAppDetails(() => null))}
                          </NavigationContainer>
                        </BottomSheetModalProvider>
                        <Toast
                          ref={ref => Toast.setRef(ref)}
                          config={toastConfig}
                        />
                      </>
                    )}
                  </CustomBootSplash>
                </GestureHandlerRootView>
              </ToastProvider>
            </SafeAreaProvider>
          </PersistGate>
        </Provider>
      </ApolloProviderClient>
    </ApolloProvider>
  );
}

export default App;
