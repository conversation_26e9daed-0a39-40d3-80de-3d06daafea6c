import React, {useCallback} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Dimensions,
  TouchableOpacity,
  Platform,
} from 'react-native';
import dayjs from 'dayjs';
import {useNavigation} from '@react-navigation/native';

import Icon from '../Icon';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {icons, followingFeedScreen, images} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import TouchAwareButton from '../Button/TouchAwareButton';
import ProfileImage from '../../components/ProfileImage';
import FastImage from '../FastImage';
import {screens} from '../../assets/strings';

const {width} = Dimensions.get('window');
const WIDTH = width;
const CARD_WIDTH = WIDTH - spacings.xxl;

const isIOS = Platform.OS === 'ios';

const FeedCard = ({item, onMenuPressed = () => null}) => {
  const navigation = useNavigation();
  const handleDecisionLayout = useCallback(type => {
    switch (type) {
      case 'REAL':
        return {
          backgroundColor: colors.greenBadge,
        };
      case 'FAKE':
        return {
          backgroundColor: colors.redBadge,
        };
      case 'UNDECIDED':
        return {
          backgroundColor: colors.darkBadge,
        };
      case 'UNDETERMINED':
        return {
          backgroundColor: colors.amber,
        };
    }
  }, []);

  const handleDecision = useCallback(decision => {
    switch (decision) {
      case 'REAL':
        return 'PASS';
      case 'FAKE':
        return 'FAIL';
      case 'UNDECIDED':
        return 'PENDING';
      case 'UNDETERMINED':
        return 'UNDETERMINED';

      default:
        return 'PENDING';
    }
  }, []);

  const handleProfileView = (userId, isExpert = false) => {
    navigation.navigate(
      isExpert ? screens.EXPERTS_PROFILE : screens.PUBLIC_PROFILES,
      {
        ...(isExpert ? {expertUserId: userId} : {params: userId}),
      },
    );
  };

  return (
    <View style={styles.cardsGroup}>
      {item?.__typename === 'FEED_ACTIVITY_NEW_VOTE' ? (
        <View style={styles.topBar}>
          {React.createElement(
            isIOS ? TouchableOpacity : TouchAwareButton,
            {
              style: styles.userBar,
              onPress: handleProfileView.bind(
                null,
                item?.vote?.user?.id,
                item?.vote?.user?.role === 'EXPERT',
              ),
            },
            <>
              <ProfileImage url={item?.vote?.user?.image} size={25} />
              <Text style={styles.userStyle}>{item?.vote?.user?.username}</Text>
              <Text style={styles.activityNotification}>
                {followingFeedScreen.VOTED_ON_A_SHIRT}
              </Text>
            </>,
          )}
          <TouchAwareButton
            style={{
              paddingLeft: spacings.lg,
            }}
            onPress={onMenuPressed.bind(null, {
              userId: item?.vote?.user?.id,
              username: item?.vote?.user?.username,
            })}>
            <Icon
              name={icons.ELIPSIS_HORIZONTAL}
              size={25}
              tint={colors.darkGrey}
            />
          </TouchAwareButton>
        </View>
      ) : (
        <View style={styles.topBar}>
          {React.createElement(
            isIOS ? TouchableOpacity : TouchAwareButton,
            {
              style: styles.userBar,
              onPress: handleProfileView.bind(
                null,
                item?.post?.user?.id,
                item?.post?.user?.role === 'EXPERT',
              ),
            },
            <>
              <ProfileImage url={item?.post?.user?.image} size={25} />
              <Text style={styles.userStyle}>{item?.post?.user?.username}</Text>
              <Text style={styles.activityNotification}>
                {followingFeedScreen.UPLOADED_A_SHIRT}
              </Text>
            </>,
          )}

          <TouchAwareButton
            style={{paddingLeft: spacings.lg}}
            onPress={onMenuPressed.bind(null, {
              userId: item?.post?.user?.id,
              username: item?.post?.user?.username,
            })}>
            <Icon
              name={icons.ELIPSIS_HORIZONTAL}
              size={25}
              tint={colors.darkGrey}
            />
          </TouchAwareButton>
        </View>
      )}

      {item?.__typename === 'FEED_ACTIVITY_NEW_VOTE' ? (
        <Text style={styles.activityDecisionNotification}>
          Voted{' '}
          <Text
            style={{
              color:
                item?.vote?.decision === 'REAL' ? colors.green : colors.warning,
              fontWeight: 'bold',
            }}>
            {handleDecision(item?.vote?.decision)}
          </Text>
        </Text>
      ) : null}
      <View>
        <FastImage
          source={
            item?.post?.images?.featured || item?.post?.images?.closeup_front
              ? {
                  uri:
                    item?.post?.images?.featured ||
                    item?.post?.images?.closeup_front,
                }
              : images.PLACEHOLDER
          }
          style={styles.imageStyle}
        />
        <View
          style={[
            styles.badgeStyle,
            handleDecisionLayout(item?.post?.authentication_result?.decision),
          ]}>
          <Icon
            name={icons.PEOPLE_OUTLINE}
            size={fontSize.md}
            tint={colors.white}
          />
          <Text style={styles.badgeText}>
            {handleDecision(item?.post?.authentication_result?.decision)}
          </Text>
          {item?.post?.authentication_result?.decision === 'UNDECIDED' ||
          item?.post?.authentication_type?.id !== 1 ? null : (
            <Text style={[styles.badgeText, {marginRight: spacings.sm}]}>
              ({item?.post?.authentication_result?.community_result?.score})%
            </Text>
          )}
        </View>
        <View
          style={[
            styles.appraisalWrapper,
            {backgroundColor: colors.darkBadge},
          ]}>
          <Text style={styles.appraisalText}>
            {item?.post?.authentication_type?.id == 1
              ? followingFeedScreen.COMMUNITY_APPRAISAL
              : followingFeedScreen.EXPERT_APPRAISAL}
            {item?.post?.authentication_type?.id == 1
              ? item?.post?.appraisal_value
              : item?.post?.authentication_result?.expert_result
                  ?.appraisal_value}
          </Text>
        </View>
        {item?.post?.my_vote ? (
          <View style={styles.voteBadge}>
            <Icon
              name={
                handleDecision(item?.post?.my_vote?.decision) === 'PASS'
                  ? icons.CHECKMARK
                  : icons.CLOSE_SHARP
              }
              size={fontSize.md}
              tint={
                handleDecision(item?.post?.my_vote?.decision) === 'PASS'
                  ? colors.green
                  : colors.warning
              }
            />
            <Text style={styles.badgeText}>
              You voted {handleDecision(item?.post?.my_vote?.decision)}
            </Text>
          </View>
        ) : null}
      </View>
      <View style={styles.bottomBar}>
        <Text numberOfLines={1} style={[styles.titleStyle]}>
          {item?.post?.title}
        </Text>
        <Text style={styles.timeStyle}>
          {item?.vote
            ? dayjs(item?.vote?.created_at).fromNow()
            : dayjs(
                item?.post?.approved_at ?? item?.post?.created_at,
              ).fromNow()}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardsGroup: {
    width: CARD_WIDTH,
    marginBottom: spacings.lg,
    borderRadius: 2,
    borderBottomColor: colors.lightGrey,
    paddingBottom: spacings.xl,
    borderBottomWidth: 3,
  },
  imageStyle: {
    width: '100%',
    height: CARD_WIDTH / 2,
    borderRadius: 2,
  },
  userBar: {
    flexDirection: 'row',
    paddingVertical: spacings.sm,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  textBar: {
    width: '100%',
    paddingBottom: spacings.md,
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    fontFamily: 'Inter-SemiBold',
    textTransform: 'capitalize',
    maxWidth: '85%',
  },
  vote: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badgeStyle: {
    position: 'absolute',
    right: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  badgeText: {
    fontSize: fontSize.sm,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: spacings.sm,
    textTransform: 'capitalize',
  },
  appraisalWrapper: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.md,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
  },
  appraisalText: {
    fontSize: fontSize.sm,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: spacings.sm,
  },
  profileImageStyle: {
    height: 20,
    width: 20,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: colors.grey,
  },
  userStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    marginLeft: spacings.sm,
    fontFamily: 'Inter-Bold',
  },
  activityNotification: {
    fontSize: fontSize.md,
    color: colors.darkGrey,
    marginLeft: spacings.sm,
    fontFamily: 'Inter-Medium',
  },
  activityDecisionNotification: {
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
    marginBottom: spacings.md,
    fontFamily: 'Inter-Medium',
    color: colors.darkGrey,
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeStyle: {
    fontSize: 10,
    color: colors.darkGrey,
  },
  bottomBar: {
    marginTop: spacings.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  voteBadge: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    paddingHorizontal: spacings.sm,
    paddingVertical: spacings.sm,
    borderTopRightRadius: 2,
    backgroundColor: colors.darkBadge,
  },
  modalContainer: {
    width: '100%',
  },
  modalTitle: {
    fontSize: fontSize.xxl,
    color: colors.black,
    fontWeight: 'bold',
    marginBottom: spacings.md,
  },
  modalText: {
    marginLeft: spacings.md,
    fontSize: fontSize.md,
    color: colors.warning,
  },
  optionWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default FeedCard;
