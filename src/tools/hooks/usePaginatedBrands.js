import React from 'react';
import {useQuery} from '@apollo/client';

import {GET_ALL_BRANDS} from '../../apollo/queries';
import {APOLLO_CACHE_IDS} from '../../assets/strings';

const defaultOptions = {
  limit: 50,
  searchTerm: '',
  isFocused: true,
  notifyOnNetworkStatusChange: true,
};
export default function usePaginatedBrands(options = defaultOptions) {
  options = {...defaultOptions, ...options};
  const [isFetchingMore, setIsFetchingMore] = React.useState(false);
  const [searching, setSearching] = React.useState(false);

  const textTimeoutIdRef = React.useRef();
  const hasSearchStarted = React.useRef(false);

  const {data, fetchMore, error, refetch, loading} = useQuery(GET_ALL_BRANDS, {
    variables: {
      limit: options.limit,
      cacheId: APOLLO_CACHE_IDS.getBrands_FILTER_BUTTONS,
    },
    notifyOnNetworkStatusChange: options.notifyOnNetworkStatusChange,
  });
  const cursorRef = React.useRef([]);

  const cursor = React.useMemo(
    () => data?.getBrands?.data?.pageInfo?.endCursor,
    [data?.getBrands?.data?.pageInfo?.endCursor],
  );
  const hasMore = React.useMemo(
    () => data?.getBrands?.data?.pageInfo?.hasMore,
    [data?.getBrands?.data?.pageInfo?.hasMore],
  );

  const handleFetchMore = React.useCallback(async () => {
    try {
      if (cursor !== null && hasMore && !cursorRef.current?.includes(cursor)) {
        cursorRef.current?.push(cursor);
        setIsFetchingMore(true);

        await fetchMore?.({
          query: GET_ALL_BRANDS,
          variables: {
            after: cursor,
            limit: options.limit,
            cacheId: APOLLO_CACHE_IDS.getBrands_FILTER_BUTTONS,
          },
          updateQuery(previousResult, {fetchMoreResult}) {
            setIsFetchingMore(false);

            const newCursor =
              fetchMoreResult?.getBrands?.data?.pageInfo?.endCursor;

            return {
              getBrands: {
                id: APOLLO_CACHE_IDS.getBrands_FILTER_BUTTONS,
                data: {
                  edges: [
                    ...previousResult?.getBrands?.data?.edges,
                    ...fetchMoreResult?.getBrands?.data?.edges,
                  ],
                  pageInfo: {
                    endCursor: newCursor,
                    hasMore:
                      fetchMoreResult?.getBrands?.data?.pageInfo?.hasMore,
                  },
                  __typename: previousResult?.getBrands?.__typename,
                },
                code: fetchMoreResult?.getBrands?.code,
                errors: fetchMoreResult?.getBrands?.errors,
                message: fetchMoreResult?.getBrands?.message,
                success: fetchMoreResult?.getBrands?.success,
              },
            };
          },
        });
        setIsFetchingMore(false);
      }
    } catch (error) {
      setIsFetchingMore(false);
    }
  }, [cursor, fetchMore, hasMore, options.limit]);

  const resetCursorRef = React.useCallback(() => {
    cursorRef.current = [];
  }, []);

  React.useEffect(() => {
    if (options.searchTerm?.length > 0) {
      hasSearchStarted.current = true;
      setSearching(true);
      if (textTimeoutIdRef.current) {
        clearTimeout(textTimeoutIdRef.current);
      }
      textTimeoutIdRef.current = setTimeout(async () => {
        try {
          resetCursorRef();
          await refetch({term: options.searchTerm, limit: options.limit});
        } catch (_) {
        } finally {
          setSearching(false);
        }
      }, 300);
    } else {
      setSearching(false);
    }
    return () => {
      clearTimeout(textTimeoutIdRef.current);
    };
  }, [
    options.searchTerm,
    refetch,
    options.searchTerm,
    options.limit,
    resetCursorRef,
  ]);

  React.useEffect(() => {
    if (hasSearchStarted.current && options.searchTerm.length == 0) {
      refetch({limit: options.limit, term: ''});
      resetCursorRef();
    }
  }, [options.searchTerm.length, refetch, options.limit, resetCursorRef]);

  React.useEffect(() => {
    if (options.isFocused) {
      refetch?.();
      resetCursorRef();
    }
  }, [options.isFocused, refetch, resetCursorRef]);

  return {
    data,
    brands: data?.getBrands?.data?.edges,
    loading,
    error,
    refetch,
    isFetchingMore,
    handleFetchMore,
    resetCursorRef,
    isSearchLoading: searching,
  };
}
