import React from 'react';
import {RefreshControl as NativeRefreshControl} from 'react-native';

import colors from '../../assets/colors';

export default function RefreshControl({
  refreshing = false,
  onRefresh = () => null,
  ...props // This one is required for RefreshControl
}) {
  return (
    <NativeRefreshControl
      refreshing={refreshing}
      progressBackgroundColor={colors.lightGrey}
      colors={[colors.primary, colors.black]}
      tintColor={colors.primary}
      onRefresh={onRefresh}
      enabled
      {...props}
    />
  );
}
