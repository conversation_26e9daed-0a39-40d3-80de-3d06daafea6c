/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const SearchLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={30}>
          <SkeletonPlaceholder.Item justifyContent="space-between">
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.45}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.35}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                alignSelf="baseline"
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              marginBottom={10}
              marginRight={10}
              width={WIDTH * 0.45}
              height={WIDTH * 0.3}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={30}>
          <SkeletonPlaceholder.Item justifyContent="space-between">
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.45}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.35}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                alignSelf="baseline"
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              marginBottom={10}
              marginRight={10}
              width={WIDTH * 0.45}
              height={WIDTH * 0.3}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={30}>
          <SkeletonPlaceholder.Item justifyContent="space-between">
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.45}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.35}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                alignSelf="baseline"
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              marginBottom={10}
              marginRight={10}
              width={WIDTH * 0.45}
              height={WIDTH * 0.3}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={30}>
          <SkeletonPlaceholder.Item justifyContent="space-between">
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.45}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.35}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                alignSelf="baseline"
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              marginBottom={10}
              marginRight={10}
              width={WIDTH * 0.45}
              height={WIDTH * 0.3}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          marginBottom={30}>
          <SkeletonPlaceholder.Item justifyContent="space-between">
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.45}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.35}
                height={10}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                alignSelf="baseline"
                marginBottom={10}
                marginRight={10}
                width={WIDTH * 0.25}
                height={10}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              marginBottom={10}
              marginRight={10}
              width={WIDTH * 0.45}
              height={WIDTH * 0.3}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default SearchLoader;
