import React from 'react';

import LottieView from 'lottie-react-native';
import lottie from '../../assets/lottie';
import colorFiltersPresets from './colorFilters';

const Lottie = React.forwardRef(
  (
    {
      name,
      style = {},
      autoPlay = true, // Should be false for manual using ref
      loop = true,
      colorFilters = [],
      colorFilterPresetName = null,
      delayInMs = 0, // Ref cannot be used when there is delay
      onAnimationFinish = () => null,
      onLayout = () => null,
    },
    ref,
  ) => {
    const [visible, setVisible] = React.useState(delayInMs === 0);

    React.useEffect(() => {
      let timerId;
      if (delayInMs > 0) {
        timerId = setTimeout(() => {
          setVisible(true);
        }, delayInMs);
      }
      return () => {
        clearTimeout(timerId);
      };
    }, [delayInMs]);

    return visible ? (
      <LottieView
        ref={ref}
        cacheStrategy="strong"
        onAnimationFinish={onAnimationFinish}
        hardwareAccelerationAndroid
        source={lottie[name]}
        autoPlay={autoPlay}
        loop={loop}
        style={style}
        colorFilters={
          colorFilterPresetName
            ? colorFiltersPresets[name]?.[colorFilterPresetName] || []
            : colorFilters
        }
        onLayout={onLayout}
      />
    ) : null;
  },
);

export default Lottie;
