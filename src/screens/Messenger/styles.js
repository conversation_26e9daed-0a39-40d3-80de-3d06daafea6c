import {StyleSheet} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  card: {
    flexDirection: 'row',
    marginVertical: 10,
  },
  descWrapper: {
    alignItems: 'flex-start',
  },
  name: {
    fontSize: 19,
    color: colors.black,
    fontFamily: 'Inter-SemiBold',
  },
  desc: {
    color: colors.darkGrey,
    fontSize: 10,
    marginBottom: 3,
    textTransform: 'capitalize',
  },
  separator: {
    marginRight: spacings.sm,
    color: colors.darkGrey,
    fontSize: 16,
  },
  info: {
    flex: 1,
    marginLeft: 10,
    justifyContent: 'center',
  },

  modalButton: {
    justifyContent: 'flex-start',
  },
  prompt: {
    color: colors.black,
    fontSize: fontSize.lg,
    marginBottom: spacings.md,
    fontWeight: 'bold',
  },
  label: {
    color: colors.primary,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: spacings.lg,
  },
  emptyView: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  buttonLabelWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalWrapper: {
    padding: 20,
  },
});
