/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const NotificationsLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            height={50}
            width={50}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item marginRight={20}>
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 1.5}
              borderRadius={4}
              marginBottom={10}
            />
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 4}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item height={30} width={10} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          height={3}
          width={WIDTH}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            height={50}
            width={50}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item marginRight={20}>
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 1.5}
              borderRadius={4}
              marginBottom={10}
            />
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 4}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item height={30} width={10} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          height={3}
          width={WIDTH}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            height={50}
            width={50}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item marginRight={20}>
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 1.5}
              borderRadius={4}
              marginBottom={10}
            />
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 4}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item height={30} width={10} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          height={3}
          width={WIDTH}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            height={50}
            width={50}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item marginRight={20}>
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 1.5}
              borderRadius={4}
              marginBottom={10}
            />
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 4}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item height={30} width={10} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          height={3}
          width={WIDTH}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            height={50}
            width={50}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item marginRight={20}>
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 1.5}
              borderRadius={4}
              marginBottom={10}
            />
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 4}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item height={30} width={10} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          height={3}
          width={WIDTH}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            height={50}
            width={50}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item marginRight={20}>
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 1.5}
              borderRadius={4}
              marginBottom={10}
            />
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 4}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item height={30} width={10} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          height={3}
          width={WIDTH}
          borderRadius={4}
          marginVertical={20}
        />
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between">
          <SkeletonPlaceholder.Item
            height={50}
            width={50}
            borderRadius={50}
            marginRight={10}
          />
          <SkeletonPlaceholder.Item marginRight={20}>
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 1.5}
              borderRadius={4}
              marginBottom={10}
            />
            <SkeletonPlaceholder.Item
              height={10}
              width={WIDTH / 4}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item height={30} width={10} borderRadius={4} />
        </SkeletonPlaceholder.Item>
        <SkeletonPlaceholder.Item
          height={3}
          width={WIDTH}
          borderRadius={4}
          marginVertical={20}
        />
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingVertical: spacings.lg,
  },
});

export default NotificationsLoader;
