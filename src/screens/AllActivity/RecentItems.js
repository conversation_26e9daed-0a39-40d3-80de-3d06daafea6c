import React from 'react';
import {Text, View, TouchableOpacity, FlatList} from 'react-native';
import {useNavigation} from '@react-navigation/core';
import {useQuery} from '@apollo/client';

import {spacings} from '../../assets/spacing';
import {allActivityScreenHeaders, images, screens} from '../../assets/strings';
import {recentItemsStyles as styles} from './styles';
import {GET_RECENT_CATEGORIES, GET_RECENT_BRANDS} from '../../apollo/queries';
import RecentBrandsLoader from '../../components/Loaders/RecentBrandsLoader';
import QueryManager from '../../components/QueryManager';
import locale from '../../assets/locale.json';
import FastImage from '../../components/FastImage';
import TouchAwareButton from '../../components/Button/TouchAwareButton';

function RecentItems({refreshing = false}) {
  return (
    <View style={styles.container}>
      <RecentBrands refreshing={refreshing} />
      <RecentCategories refreshing={refreshing} />
    </View>
  );
}

function RecentBrands({refreshing = false}) {
  const navigation = useNavigation();

  const {data, loading, error, refetch} = useQuery(GET_RECENT_BRANDS, {
    variables: {
      limit: 10,
    },
  });

  const renderItem = React.useCallback(
    ({item}) => (
      <TouchAwareButton
        ke={item?.id?.toString()}
        style={{marginRight: spacings.md}}
        onPress={() =>
          navigation.navigate(screens.SINGLE_BRAND_SCREEN, {
            params: item,
          })
        }>
        <TouchAwareButton.View style={styles.brandWrapper}>
          <FastImage
            source={item?.image_url ? {uri: item?.image_url} : images.logo1}
            style={styles.brandLogo}
          />
        </TouchAwareButton.View>
      </TouchAwareButton>
    ),
    [],
  );

  React.useEffect(() => {
    if (refreshing) {
      try {
        refetch?.();
      } catch (error) {}
    }
  }, [refreshing, refetch]);

  return (
    <>
      <View style={styles.labelBar}>
        <Text style={styles.label}>
          {allActivityScreenHeaders.RECENT_BRANDS}
        </Text>
        <TouchableOpacity
          onPress={() => navigation.navigate(screens.ALLBRAND_CATEGORIES)}>
          <Text
            style={[
              styles.label,
              {
                textDecorationLine: 'underline',
                fontFamily: 'Inter-Medium',
                paddingLeft: 30,
              },
            ]}>
            {locale.All}
          </Text>
        </TouchableOpacity>
      </View>
      <QueryManager
        data={data}
        loading={loading}
        error={error}
        refetch={refetch}>
        <QueryManager.Data>
          <View style={styles.cardsGroup}>
            <FlatList
              data={data?.getRecentBrands?.data}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.contentContainer}
              renderItem={renderItem}
              keyExtractor={item => item.id.toString()}
            />
          </View>
        </QueryManager.Data>
        <QueryManager.Loading>
          <RecentBrandsLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale.CouldNotFetchRecentBrands}
          style={{marginVertical: 10}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
    </>
  );
}

function RecentCategories({refreshing = false}) {
  const navigation = useNavigation();

  const {data, loading, error, refetch} = useQuery(GET_RECENT_CATEGORIES, {
    variables: {
      limit: 10,
    },
  });

  const renderItem = React.useCallback(
    ({item}) => (
      <TouchAwareButton
        onPress={() =>
          navigation.navigate(screens.SINGLE_CATEGORY_SCREEN, {
            params: item,
          })
        }
        style={{
          marginRight: spacings.md,
        }}>
        <View style={styles.brandWrapper}>
          <FastImage
            source={item?.image_url ? {uri: item?.image_url} : images.logo1}
            style={[styles.brandLogo]}
          />
        </View>
      </TouchAwareButton>
    ),
    [],
  );

  React.useEffect(() => {
    if (refreshing) {
      try {
        refetch?.();
      } catch (error) {}
    }
  }, [refreshing, refetch]);

  return (
    <>
      <View
        style={[
          styles.labelBar,
          {
            marginTop: spacings.lg,
            alignItems: 'center',
          },
        ]}>
        <Text style={styles.label}>
          {allActivityScreenHeaders.POPULAR_CATEGORIES}
        </Text>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(screens.ALLBRAND_CATEGORIES, {
              screen: screens.ALL_CATEGORIES,
            })
          }>
          <Text
            style={[
              styles.label,
              {
                textDecorationLine: 'underline',
                fontFamily: 'Inter-Medium',
                paddingLeft: 30,
              },
            ]}>
            {locale.All}
          </Text>
        </TouchableOpacity>
      </View>
      <QueryManager
        data={data}
        loading={loading}
        error={error}
        refetch={refetch}>
        <QueryManager.Data>
          <View>
            <FlatList
              data={data?.getRecentCategories?.data}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={[
                styles.contentContainer,
                {paddingEnd: spacings.md},
              ]}
              renderItem={renderItem}
              keyExtractor={item => item.id.toString()}
            />
          </View>
        </QueryManager.Data>
        <QueryManager.Loading>
          <RecentBrandsLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale.CouldNotFetchRecentCategories}
          style={{marginVertical: 10}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
    </>
  );
}

export default RecentItems;
