import messaging from '@react-native-firebase/messaging';
import {Platform} from 'react-native';

class FCMService {
  configure = ({
    onRegister = () => null,
    onNotification = () => null,
    onOpenNotification = () => null,
  }) => {
    this.requestPermission(onRegister);
    this.createNotificationListeners(
      onRegister,
      onNotification,
      onOpenNotification,
    );
  };

  // @Deprecated: Register will handle automatically now
  registerAppWithFCM = async () => {
    if (Platform.OS === 'ios') {
      await messaging().registerDeviceForRemoteMessages();
      await messaging().setAutoInitEnabled(true);
    }
  };

  checkPermission = onRegister => {
    messaging()
      .hasPermission()
      .then(enabled => {
        if (__DEV__) {
          console.log('[FCM] checkPermission  is enabled', enabled);
        }
        if (enabled) {
          // User has permissions
          this.getToken(onRegister);
        } else {
          // User doesn't have permission
          this.requestPermission(onRegister);
        }
      })
      .catch(error => {
        if (__DEV__) {
          console.log('[FCMService] Permission rejected ', error);
        }
      });
  };

  getToken = onRegister => {
    messaging()
      .getToken()
      .then(async fcmToken => {
        if (fcmToken) {
          if (__DEV__) {
            console.log('[FCMService] getToken', fcmToken);
          }
          onRegister(fcmToken);
        } else {
          if (__DEV__) {
            console.log('[FCMService] User does not have a device token');
          }
        }
      })
      .catch(error => {
        if (__DEV__) {
          console.log('[FCMService] getToken rejected ', error);
        }
      });
  };

  requestPermission = onRegister => {
    messaging()
      .requestPermission()
      .then(authStatus => {
        if (
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL
        ) {
          this.getToken(onRegister);
        } else {
          if (__DEV__) {
            console.log('Request permission denied');
          }
        }
      })
      .catch(error => {
        if (__DEV__) {
          console.log('[FCMService] Request Permission rejected ', error);
        }
      });
  };

  deleteToken = () => {
    if (__DEV__) {
      console.log('[FCMService] deleteToken');
    }
    messaging()
      .deleteToken()
      .catch(error => {
        if (__DEV__) {
          console.log('[FCMService] Delete token error ', error);
        }
      });
  };

  createNotificationListeners = (
    onRegister,
    onNotification,
    onOpenNotification,
  ) => {
    // Foreground state messages
    this.messageListener = messaging().onMessage(async remoteMessage => {
      if (__DEV__) {
        console.log('[FCMService] A new FCM message arrived!', remoteMessage);
      }
      if (remoteMessage) {
        onNotification(remoteMessage);
      }
    });

    // When the application is running, but in the background
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log("Kpailll=====FCM")
      if (__DEV__) {
        console.log(
          '[FCMService] onNotificationOpenedApp Notification caused app to open from background state:',
          remoteMessage,
        );
      }
      if (remoteMessage) {
        onOpenNotification(remoteMessage);
        // this.removeDeliveredNotification(notification.notificationId)
      }
    });

    // When the application is opened from a quit state.
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          if (__DEV__) {
            console.log(
              '[FCMService] getInitialNotification Notification caused app to open from quit state:',
              remoteMessage,
            );
          }

          if (remoteMessage) {
            onOpenNotification(remoteMessage);
            //  this.removeDeliveredNotification(notification.notificationId)
          }
        }
      });

    // Triggered when have new token
    messaging().onTokenRefresh(fcmToken => {
      if (__DEV__) {
        console.log('[FCMService] New token refresh: ', fcmToken);
      }
      onRegister(fcmToken);
    });
  };

  unRegister = () => {
    this.messageListener();
  };
}

export const fcmService = new FCMService();
