import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Linking,
  Platform,
} from 'react-native';
import {useIsFocused} from '@react-navigation/native';

import PersonalInformationLoader from '../../components/Loaders/PersonalInformationLoader';
import ChangeProfilePic from './ChangeProfilePic';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import {profileSettingsScreen, screens} from '../../assets/strings';
import {useUser} from '../../tools/hooks';
import QueryManager from '../../components/QueryManager/index';
import locale from '../../assets/locale.json';
import {stripHTTPSFromURL, formatURL} from '../../tools/utils';

const isIOS = Platform.OS == 'ios';

const PersonalInformation = ({navigation}) => {
  const isFocused = useIsFocused();

  const {user, isLoading, error, refresh} = useUser({focused: isFocused});

  const socialMediaUrl = user?.social_media?.[0]?.url;

  const handleLinking = async () => {
    try {
      await Linking.openURL(formatURL(socialMediaUrl));
    } catch (_) {}
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={{flex: 1}}>
      <QueryManager data={user} loading={isLoading} error={error}>
        <QueryManager.Data>
          <ChangeProfilePic user={user} refetch={refresh} />
          <View style={styles.editButtonWrapper}>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => navigation.navigate(screens.EDIT_INFORMATION)}>
              <Text style={styles.buttonLabel}>
                {profileSettingsScreen.EDIT}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inforCard}>
            <View style={styles.singleCard}>
              <Text style={[styles.label]}>
                {profileSettingsScreen.FIRST_NAME}
              </Text>
              <Text
                style={[styles.value, {maxWidth: '60%'}]}
                numberOfLines={1}
                ellipsizeMode="middle">
                {user?.first_name}
              </Text>
            </View>
            <View style={styles.singleCard}>
              <Text style={styles.label}>{profileSettingsScreen.USERNAME}</Text>
              <Text style={styles.value}>{user?.username}</Text>
            </View>
            <View style={styles.singleCard}>
              <Text style={styles.label}>{profileSettingsScreen.EMAIL}</Text>
              <Text
                numberOfLines={1}
                ellipsizeMode="middle"
                style={[styles.value, {maxWidth: '60%'}]}>
                {user?.email}
              </Text>
            </View>

            <View style={[styles.singleCard, {flexDirection: 'column'}]}>
              <Text style={styles.label}>Bio</Text>
              <Text numberOfLines={5} style={[styles.value, {fontSize: 12}]}>
                {user?.bio}
              </Text>
            </View>
            <View style={styles.singleCard}>
              <Text style={styles.label}>{locale.Website}</Text>
              {socialMediaUrl ? (
                <TouchableOpacity
                  style={[styles.value, {maxWidth: '60%'}]}
                  onPress={handleLinking}>
                  <Text
                    ellipsizeMode="middle"
                    numberOfLines={1}
                    style={[
                      styles.value,
                      {
                        color: colors.primary,
                        borderBottomWidth: 1,
                        borderBottomColor: colors.primary,
                      },
                    ]}>
                    {stripHTTPSFromURL(socialMediaUrl)}
                  </Text>
                </TouchableOpacity>
              ) : null}
            </View>
          </View>
        </QueryManager.Data>
        <QueryManager.Loading>
          <PersonalInformationLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale.SomethingWentWrongPleaseTryAgain}
          style={{marginVertical: 10, flex: 1}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },

  buttonLabel: {
    color: colors.primary,
    fontSize: fontSize.lg,
  },
  editButtonWrapper: {
    width: '100%',
    alignItems: 'flex-end',
  },
  editButton: {
    paddingLeft: spacings.md,
    paddingVertical: spacings.md,
  },
  inforCard: {
    width: '100%',
  },
  singleCard: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: spacings.lg,
  },
  label: {
    fontSize: fontSize.lg,
    color: colors.black,
    fontFamily: 'Inter-Medium',
  },
  value: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  inputField: {
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: colors.grey,
    borderRadius: 4,
    marginTop: spacings.sm,
    color: colors.black,
    paddingLeft: spacings.md,
    // placeholderTextColor: colors.grey,
    // ADDED
    ...(isIOS ? {padding: 15} : {}),
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
  },
  inputCard: {
    marginTop: spacings.lg,
  },
  submitButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    backgroundColor: colors.primary,
    height: 50,
    borderRadius: 4,
    marginVertical: spacings.xxl,
  },
  submitButtonText: {
    fontSize: fontSize.lg,
    color: colors.white,
    fontWeight: 'bold',
  },
  headerBar: {
    backgroundColor: colors.black,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: colors.white,
    fontSize: fontSize.xl,
    fontWeight: 'bold',
  },
  closeModalButton: {
    position: 'absolute',
    right: 0,
    padding: spacings.lg,
  },
  closeModalButtonText: {
    color: colors.primary,
    fontSize: fontSize.xl,
    fontWeight: 'bold',
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});

export default PersonalInformation;
