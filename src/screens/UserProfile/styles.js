import {StyleSheet, Dimensions} from 'react-native';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';

const {width: WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('screen');

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  headerContainer: {
    paddingVertical: spacings.md,
    paddingHorizontal: spacings.lg,
    backgroundColor: colors.almostWhite,
    marginBottom: 20,
  },
  imageWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacings.md,
  },
  profilePhoto: {
    height: 72,
    width: 72,
    borderRadius: 72,
    borderWidth: 2,
    borderColor: colors.grey,
  },
  levelsWrapper: {
    flexDirection: 'row',
    marginTop: spacings.sm,
    alignItems: 'center',
  },
  popularity: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: spacings.md,
    marginBottom: spacings.md,
  },
  popularityWrapper: {
    alignItems: 'center',
  },
  popularityStat: {
    color: colors.black,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
  },
  socialButtonsWrapper: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginVertical: spacings.md,
  },
  socialButtons: {
    backgroundColor: colors.black,
    borderRadius: 25,
    width: WIDTH / 2.5,
    paddingVertical: spacings.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  socialButtonsText: {
    color: colors.white,
    textAlign: 'center',
    fontSize: fontSize.md,
    marginLeft: spacings.sm,
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  username: {
    color: colors.black,
    fontSize: 18,
    fontFamily: 'Inter-Bold',
  },
  textSection: {
    marginLeft: spacings.md,
    flexDirection: 'row',
  },
  levelsButton: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 5,
    borderWidth: 1,
    borderColor: colors.grey,
    borderRadius: 5,
  },
  levelsText: {
    color: colors.black,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Medium',
    fontSize: 12,
  },
  descWrapper: {
    // marginTop: spacings.md,
  },
  desc: {
    color: colors.darkFont,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
  },
  link: {
    color: colors.primary,
    fontFamily: 'Inter-Medium',
  },
  badge: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    backgroundColor: colors.almostWhite,
  },
  userActivityTitle: {
    color: colors.black,
    fontSize: fontSize.lg,
    fontWeight: 'bold',
  },
  profileFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
    backgroundColor: colors.lightGrey,
    paddingVertical: spacings.md,
    borderRadius: 4,
  },
  profileFilterButtonText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginRight: spacings.md,
  },
  userActivityTitleWrapper: {
    paddingHorizontal: spacings.lg,
    paddingBottom: spacings.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emptyViewText: {
    color: colors.black,
    fontSize: fontSize.md,
    marginTop: 20,
  },
  emptyView: {
    height: SCREEN_HEIGHT / 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
});
