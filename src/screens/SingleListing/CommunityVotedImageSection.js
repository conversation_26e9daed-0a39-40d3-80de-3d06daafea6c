import React, {useCallback, useMemo} from 'react';
import {Text, View, TouchableOpacity, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import Icon from '../../components/Icon';
import {styles} from './styles';
import {icons, images, screens, IMAGE_TYPES} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import colors from '../../assets/colors';
import {singleListingScreen} from '../../assets/strings';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ImageViewer from '../../components/ImageViewer';
import FastImage from '../../components/FastImage';
import ToolTip from '../../components/Tooltip';
import locale from '../../assets/locale.json';
import {fixedNumberTo} from '../../tools/utils';

const isIOS = Platform.OS === 'ios';

const ImageSection = ({data}) => {
  const navigation = useNavigation();
  const [imageViewerVisible, setImageViewerVisible] = React.useState(false);

  const authenticationData = data?.getPost?.data;
  const image = data?.getPost?.data?.images?.front;
  const imageUrls = data?.getPost?.data?.images;
  const decision = React.useMemo(
    () =>
      data?.getPost?.data?.authentication_result?.community_result?.decision,
    [data?.getPost?.data?.authentication_result?.community_result?.decision],
  );

  const isAuthenticationPending = React.useMemo(
    () => data?.getPost?.data?.authentication_status === 'PENDING',
    [data?.getPost?.data?.authentication_status],
  );

  const galleryImages = useMemo(() => {
    let objs = [];
    if (!(imageUrls == null) && typeof imageUrls === 'object') {
      for (const [key, value] of Object.entries(imageUrls)) {
        if (key in IMAGE_TYPES && value?.length > 0) {
          objs.push({image: value, key: key});
        }
      }
    }
    return objs;
  }, [imageUrls]);

  const handleDecision = useCallback(
    () => {
      if (isAuthenticationPending) {
        return 'PENDING';
      }
      switch (decision) {
        case 'REAL':
          return 'PASS';
        case 'FAKE':
          return 'FAIL';
        case 'UNDETERMINED':
          return 'UNDETERMINED';
        default:
          return 'UNDECIDED';
      }
    },
    [decision],
    isAuthenticationPending,
  );

  const handleColor = useCallback(() => {
    switch (
      data?.getPost.data?.authentication_result?.community_result?.decision
    ) {
      case 'REAL':
        return {
          backgroundColor: colors.greenBadge,
        };
      case 'FAKE':
        return {
          backgroundColor: colors.redBadge,
        };
      default:
        return {
          backgroundColor: colors.darkBadge,
        };
    }
  }, [decision]);

  const handleImageViewing = React.useCallback(() => {
    setImageViewerVisible(true);
  }, []);

  const isVotingClosed = React.useMemo(
    () => authenticationData?.is_voting_closed,
    [authenticationData?.is_voting_closed],
  );

  const isRevealing = React.useMemo(
    () =>
      !isAuthenticationPending &&
      handleDecision() !== 'UNDECIDED' &&
      !isVotingClosed,
    [handleDecision, isVotingClosed, isAuthenticationPending],
  );

  return (
    <>
      <View style={styles.mainImageContainer}>
        <TouchAwareButton
          disableLongPressAnimation
          disableTapAnimation
          onPress={handleImageViewing}>
          <FastImage
            source={image ? {uri: image} : images.PLACEHOLDER}
            style={styles.mainImage}
          />
        </TouchAwareButton>

        {!isAuthenticationPending ? (
          <View style={styles.topImageSection}>
            <TouchableOpacity
              style={[styles.button, {margin: 20}]}
              onPress={() =>
                navigation.navigate(screens.POST_GALLERY, {
                  postId: data?.getPost?.data?.id,
                })
              }>
              <Text style={styles.buttonText}>
                {singleListingScreen.VIEW_GALLERY}({galleryImages?.length ?? 0})
              </Text>
            </TouchableOpacity>
          </View>
        ) : null}
        <View style={[styles.bottomImageSection, {padding: 20}]}>
          <View style={[{flexDirection: 'row', alignItems: 'center'}]}>
            <View style={[styles.button, handleColor()]}>
              <Icon
                name={icons.PEOPLE_OUTLINE}
                tint={colors.white}
                size={fontSize.md}
              />
              <Text style={[styles.buttonText, {marginLeft: 5}]}>
                {`${handleDecision()}${isRevealing ? 'ing' : ''}`}{' '}
                {decision === 'REAL' || decision === 'FAKE'
                  ? `(${fixedNumberTo(
                      data?.getPost?.data?.authentication_result
                        ?.community_result?.score ?? 0,
                    )}%)`
                  : null}
              </Text>
            </View>
            {decision === 'UNDECIDED' || isAuthenticationPending ? (
              <ToolTip
                tint={colors.white}
                containerStyle={{
                  marginLeft: 10,
                  borderRadius: 25,
                  padding: 5,
                  ...(isIOS ? {} : {backgroundColor: 'rgba(0, 0, 0, 0.3)'}),
                }}
                enableSheetContent
                title={
                  isAuthenticationPending ? locale.Pending : locale.Undecided
                }
                description={
                  isAuthenticationPending
                    ? "This submission has yet to be approved for visibility in our feed. You'll receive a notification when it has."
                    : 'Before a score is displayed, submissions must receive a minimum amount of community votes. Please check back shortly for the initial results.'
                }
                size={23}
              />
            ) : null}
          </View>

          {!isAuthenticationPending ? (
            <TouchableOpacity
              style={styles.button}
              onPress={() =>
                navigation.navigate(screens.COMMENTS, {
                  postId: data?.getPost?.data?.id,
                })
              }>
              <Icon
                name={icons.COMMENT_ICON}
                tint={colors.white}
                size={fontSize.md}
                style={{marginTop: 4}}
              />
              <Text style={[styles.buttonText, {marginHorizontal: 5}]}>
                {data?.getPost?.data?.number_of_post_comments}
              </Text>

              <Text style={[styles.buttonText]}>Comments</Text>
            </TouchableOpacity>
          ) : null}
        </View>
        <ImageViewer
          visible={imageViewerVisible}
          setVisible={setImageViewerVisible}
          images={galleryImages?.map(img => ({url: img?.image, id: img?.key}))}
          keyExtractor={img => img?.key}
        />
      </View>
    </>
  );
};

export default ImageSection;
