import React from 'react';
import {
  View,
  Text,
  Keyboard,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {useLazyQuery} from '@apollo/client';
import {useIsFocused} from '@react-navigation/native';
import {FlatList} from 'react-native-gesture-handler';
import {useNavigation} from '@react-navigation/native';

import {GET_USERS} from '../../apollo/queries';
import {useUser} from '../../tools/hooks';
import {formatDigits} from '../../tools/utils';
import colors from '../../assets/colors';
import ProfileImage from '../../components/ProfileImage';
import styles from './styles';
import {screens} from '../../assets/strings';
import UsersSkeleton from './UsersSkeleton';
import locale from '../../assets/locale.json';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import {APOLLO_CACHE_IDS} from '../../assets/strings';

const LIMIT = 100;

export default function Users({
  term = '',
  inlineMode = false,
  handleItemPress,
  roleIds,
  userIdsToBeIgnored,
}) {
  const navigation = useNavigation();

  const lastInputRef = React.useRef('');

  const [searchInput, setSearchInput] = React.useState(term ?? '');
  const [refreshing, setRefreshing] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const isFocused = useIsFocused();
  const {user: userData} = useUser({focused: isFocused});

  const debounceId = React.useRef();
  const firstRef = React.useRef(true);

  const [getUsers, {data, refetch}] = useLazyQuery(GET_USERS, {
    variables: {
      limit: inlineMode ? 3 : LIMIT,
      filters: {
        ...(searchInput?.length > 0 ? {term: searchInput} : {}),
        ...(roleIds == null ? {} : {roleIds}),
        ...(userIdsToBeIgnored == null ? {} : {userIdsToBeIgnored}),
      },
      cacheId: APOLLO_CACHE_IDS.getUsers_SEARCH,
    },
    fetchPolicy: 'network-only',
  });

  const handleTextChange = React.useCallback(
    async value => {
      if (isFocused && value !== lastInputRef.current) {
        if (!(value == null)) {
          setSearchInput(value);
          lastInputRef.current = value;
        }
        setLoading(true);
        if (debounceId.current) {
          clearTimeout(debounceId.current);
        }
        debounceId.current = setTimeout(async () => {
          try {
            await getUsers();
          } catch (_) {
          } finally {
            setLoading(false);
          }
        }, 300);
      }
    },
    [getUsers, isFocused],
  );

  React.useEffect(() => {
    if (!firstRef.current) {
      handleTextChange(term);
    }
    if (firstRef.current) {
      firstRef.current = false;
    }
    return () => {
      clearTimeout(debounceId.current);
    };
  }, [term, handleTextChange]);

  const users = data?.getUsers?.data?.edges;

  const loggedInUserId = userData?.id;

  const handleUserPress = React.useCallback(
    item => {
      if (handleItemPress == null) {
        const isMember = item?.role === 'MEMBER';
        const isMyself = loggedInUserId == item?.id;
        navigation.navigate(
          item?.role === 'MEMBER'
            ? isMyself
              ? screens.USER_PROFILE
              : screens.PUBLIC_PROFILES
            : screens.EXPERTS_PROFILE,
          {
            ...(isMember ? {params: +item?.id} : {expertUserId: +item?.id}),
          },
        );
      } else {
        handleItemPress(item);
      }
    },
    [navigation.navigate, loggedInUserId, handleItemPress],
  );

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      if (searchInput?.length > 0)
        try {
          setRefreshing(enableRefreshControl);
          await refetch?.();
        } catch (error) {
        } finally {
          setRefreshing(false);
        }
    },
    [refetch, searchInput?.length],
  );

  const renderUserRow = React.useCallback(
    ({item}) => (
      <TouchableOpacity
        activeOpacity={0.5}
        style={styles.userProfileContainer}
        onPress={handleUserPress.bind(null, item)}>
        <ProfileImage size={50} canViewImage={false} url={item?.image} />
        <View style={styles.userInfoContainer}>
          <View>
            <Text style={styles.titleStyle}>@{item?.username}</Text>
            {item?.role === 'EXPERT' ? (
              <Text style={styles.subtitleStyle}>
                {item?.badge?.name ?? ''}
              </Text>
            ) : (
              <Text style={styles.subtitleStyle}>
                {formatDigits(item?.accuracy, 2) ?? 0}% Accuracy |{' '}
                {item?.badge?.name ?? ''}
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    ),
    [handleUserPress],
  );

  const renderHeader = React.useMemo(
    () => (
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 20,
          paddingVertical: 10,
        }}>
        <Text
          style={[
            styles.titleStyles,
            {
              fontSize: 20,
              lineHeight: undefined,
            },
          ]}>
          {locale.Users}
        </Text>
        <TouchableOpacity
          onPress={() => {
            navigation.navigate(screens.USER_SEARCH);
          }}>
          <Text
            style={{
              fontFamily: 'Inter-Medium',
              color: colors.secondary,
              paddingLeft: 10,
              fontSize: 15,
            }}>{`See All (${
            data?.getUsers?.data?.pageInfo?.totalCount ?? 0
          })`}</Text>
        </TouchableOpacity>
      </View>
    ),
    [navigation.navigate, data?.getUsers?.data?.pageInfo?.totalCount],
  );

  return (
    <View
      style={{backgroundColor: colors.white, ...(inlineMode ? {} : {flex: 1})}}>
      <FlatList
        keyboardShouldPersistTaps="always"
        data={searchInput?.length > 0 ? users : []}
        keyExtractor={item => item?.id?.toString?.()}
        renderItem={renderUserRow}
        ListHeaderComponent={() => {
          return inlineMode && users?.length > 0 ? renderHeader : null;
        }}
        ListEmptyComponent={
          inlineMode ? null : (
            <View style={{flex: 1}}>
              {!loading ? (
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: colors.white,
                    flex: 1,
                  }}>
                  {searchInput?.length > 0 ? (
                    Array.isArray(users) && users?.length == 0 ? (
                      <DefaultEmptyComponent
                        text={locale.NoUsersFound}
                        lottieStyle={{
                          width: 100,
                          height: 100,
                          alignSelf: 'center',
                        }}
                        textStyle={{marginTop: -10}}
                      />
                    ) : null
                  ) : (
                    <Text
                      style={{color: colors.black, fontFamily: 'Inter-Medium'}}>
                      {locale.TypeToSearch}...
                    </Text>
                  )}
                </View>
              ) : (
                <UsersSkeleton />
              )}
            </View>
          )
        }
        style={{
          backgroundColor: colors.white,
          paddingVertical: inlineMode ? 0 : 10,
        }}
        {...(inlineMode
          ? {}
          : {
              contentContainerStyle:
                searchInput?.length > 0 &&
                Array.isArray(users) &&
                users?.length > 0
                  ? {}
                  : {flex: 1},
            })}
        refreshControl={
          inlineMode ? null : (
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          )
        }
        onScroll={() => {
          Keyboard.dismiss();
        }}
      />
    </View>
  );
}
