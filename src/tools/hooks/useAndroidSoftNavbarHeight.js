// @ts-check
import React from 'react';

import {StatusBar, Platform} from 'react-native';

import useWindowDimensions from './useWindowDimensions';

export default function useAndroidSoftNavbarHeight() {
  const {
    screen: {height: screenHeight},
    window: {height: windowHeight},
  } = useWindowDimensions();

  const androidNavbarHeight = React.useMemo(
    () => screenHeight - (windowHeight + StatusBar.currentHeight),
    [screenHeight, windowHeight],
  );

  return Platform.OS === 'android' ? androidNavbarHeight : 0;
}
