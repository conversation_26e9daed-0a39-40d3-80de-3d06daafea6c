import React from 'react';
import {Image, StyleSheet, Dimensions, Text, View} from 'react-native';

import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import {icons} from '../../assets/strings';
import Icon from '../../components/Icon';
import Layout from '../../layout';

const {width: WIDTH} = Dimensions.get('screen');

const Examples = () => {
  return (
    <Layout contentContainerStyle={styles.container}>
      <Text style={[styles.headerStyle, {fontFamily: 'Gugi-Regular'}]}>
        Examples of photos
      </Text>
      <Text style={styles.desc}>
        Please ensure the photos you use during the submission process are
        high-quality images.{'\n'}
        {'\n'}For community-voted submissions, you may upload photos from your
        phone's gallery, or take them using the camera.
        {'\n'}
        {'\n'}However, our expert certifications require that you have the shirt
        in your possession and take the photos with your phone.{'\n'}
        {'\n'}Our authenticators view photos on a desktop computer in order to
        best assess submissions. Poor quality photos provided to any type of
        submission may cause it to be rejected.{'\n'}
        {'\n'}Our app also has photo editing functions that allow you to crop,
        rotate and scale your photos.
      </Text>

      <View style={styles.bottomWrapper}>
        <Text style={styles.headerStyle}>1. Make sure the image is sharp.</Text>
        <View style={styles.imageSectionOne}>
          <View style={styles.imageWrapper}>
            <Image
              style={styles.imageStyles}
              source={require('../../assets/Images/example_four.jpg')}
            />
            <Icon
              containerStyle={styles.iconStyles}
              name={icons.RIGHT_PHOTO_ICON}
              size={20}
              tint={colors.green}
            />
          </View>
          <View style={styles.imageWrapper}>
            <Image
              style={styles.imageStyles}
              source={require('../../assets/Images/example_three.jpg')}
            />
            <Icon
              containerStyle={styles.iconStyles}
              name={icons.WRONG_PHOTO_ICON}
              size={20}
              tint={colors.warning}
            />
          </View>
        </View>
      </View>

      <View style={styles.bottomWrapper}>
        <Text style={styles.headerStyle}>
          2. Make sure the image is bright enough.
        </Text>
        <View style={styles.imageSectionOne}>
          <View style={styles.imageWrapper}>
            <Image
              style={styles.imageStyles}
              source={require('../../assets/Images/example_two.jpg')}
            />
            <Icon
              containerStyle={styles.iconStyles}
              name={icons.RIGHT_PHOTO_ICON}
              size={20}
              tint={colors.green}
            />
          </View>
          <View style={styles.imageWrapper}>
            <Image
              style={styles.imageStyles}
              source={require('../../assets/Images/example_one.jpg')}
            />
            <Icon
              containerStyle={styles.iconStyles}
              name={icons.WRONG_PHOTO_ICON}
              size={20}
              tint={colors.warning}
            />
          </View>
        </View>
      </View>
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
  headerStyle: {
    color: colors.black,
    fontSize: fontSize.xl,
    marginBottom: spacings.md,
    fontFamily: 'Inter-Medium',
  },
  desc: {
    color: colors.darkGrey,
    fontFamily: 'Inter-Regular',
  },
  imageSectionOne: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  imageStyles: {
    width: '100%',
    height: WIDTH / 3.5,
  },
  bottomWrapper: {
    marginVertical: spacings.lg,
  },
  imageWrapper: {
    width: '48%',
    position: 'relative',
  },
  iconStyles: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: colors.white,
    padding: 3,
    borderRadius: 5,
  },
});
export default Examples;
