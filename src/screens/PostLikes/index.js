import React, {useCallback} from 'react';
import {FlatList, Text, View} from 'react-native';
import {useQuery} from '@apollo/react-hooks';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';

import VotesLoader from '../../components/Loaders/VotesLoader';
import {GET_POST_LIKED_BY_USERS} from '../../apollo/queries';
import {screens} from '../../assets/strings';
import ProfileImage from '../../components/ProfileImage';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import RefreshControl from '../../components/RefreshControl';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import {useWindowDimensions} from '../../tools/hooks';
import styles from './styles';

const LIMIT = 100;

function PostLikes() {
  const route = useRoute();
  const navigation = useNavigation();

  const {postId, numberOfLikes = 0} = route?.params;
  const scrollRef = React.useRef();

  const {
    window: {height},
  } = useWindowDimensions();

  const [refreshing, setRefreshing] = React.useState(false);

  const {data, loading, refetch} = useQuery(GET_POST_LIKED_BY_USERS, {
    variables: {
      postId: +postId,
      limit: LIMIT,
    },
  });

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch],
  );

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          // cursorRef.current = [];
          refetch?.();
        }
      } catch (_) {}
    }, [refetch]),
  );

  React.useLayoutEffect(() => {
    navigation.setOptions?.({
      title: `Likes${numberOfLikes > 0 ? `(${numberOfLikes})` : ''}`,
    });
  }, [numberOfLikes, navigation.setOptions]);

  const renderItem = useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          style={styles.card}
          onPress={() => {
            navigation.push(screens.PUBLIC_PROFILES, {
              params: item?.id,
            });
          }}>
          <View style={styles.rightSection}>
            <ProfileImage url={item?.image} size={45} />
            <Text style={styles.name}>{item?.username}</Text>
          </View>
        </TouchAwareButton>
      );
    },
    [navigation.push],
  );

  const list = data?.getPostLikedByUsers?.data?.edges;

  return (
    <View style={styles.container}>
      <FlatList
        contentContainerStyle={{paddingTop: 10, paddingBottom: 30}}
        data={list}
        keyExtractor={item => item?.id?.toString()}
        renderItem={renderItem}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        initialNumToRender={10}
        onScroll={e => {
          scrollRef.current = e?.nativeEvent?.contentOffset?.y;
        }}
        ListFooterComponent={loading && data == null ? <VotesLoader /> : null}
        ListEmptyComponent={
          !loading ? (
            <View
              style={{
                flex: 1,
                alignItems: 'center',
                height: height / 1.2,
                justifyContent: 'center',
              }}>
              <DefaultEmptyComponent lottieStyle={{width: 100, height: 100}} />
            </View>
          ) : null
        }
      />
    </View>
  );
}

export default PostLikes;
