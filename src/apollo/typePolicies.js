export default Object.freeze({
  PostsResponse: {
    fields: {
      id: {
        read(_, {variables}) {
          if (variables?.cacheId == null) {
            if (__DEV__) {
              console.warn(
                '[TypePolicies] Make sure to pass a cacheId variable for cache to work properly for PostsResponse ',
                __filename,
              );
            }
          }
          return variables?.cacheId;
        },
      },
    },
  },
  BrandsResponse: {
    fields: {
      id: {
        read(_, {variables}) {
          if (variables?.cacheId == null) {
            if (__DEV__) {
              console.warn(
                '[TypePolicies] Make sure to pass a cacheId variable for cache to work properly for BrandsResponse ',
                __filename,
              );
            }
          }
          return variables?.cacheId;
        },
      },
    },
  },
  UsersResponse: {
    fields: {
      id: {
        read(_, {variables}) {
          if (variables?.cacheId == null) {
            if (__DEV__) {
              console.warn(
                '[TypePolicies] Make sure to pass a cacheId variable for cache to work properly for UsersResponse ',
                __filename,
              );
            }
          }
          return variables?.cacheId;
        },
      },
    },
  },
});
