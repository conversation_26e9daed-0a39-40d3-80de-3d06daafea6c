import React from 'react';
import {StyleSheet, Platform} from 'react-native';

import {spacings} from '../../assets/spacing';
import colors from '../../assets/colors';

export default styles = StyleSheet.create({
  listRow: {
    padding: spacings.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  listRowText: {
    fontFamily: 'Inter-Regular',
  },
  radioButton: {
    height: 30,
    width: 30,
    borderRadius: 2,
  },
  filterUtilityButton: {
    minHeight: 20,
    marginLeft: 'auto',
    padding: 0,
    paddingVertical: 5,
    paddingHorizontal: 15,
    minWidth: 10,
    marginRight: 10,
  },
  searchInput: {
    backgroundColor: 'transparent',
    flex: 1,
    color: colors.black,
    borderRadius: 10,
    borderWidth: 0,
    paddingHorizontal: 10,
    ...(Platform.OS === 'ios' ? {padding: 7} : {paddingVertical: 7}),
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.lightGrey,
    borderRadius: 4,
    paddingLeft: spacings.md,
    marginHorizontal: 20,
    paddingVertical: 5,
    minHeight: 40,
  },
});
