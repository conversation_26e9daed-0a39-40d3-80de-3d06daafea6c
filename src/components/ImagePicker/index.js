import React from 'react';
import {Text, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
import {useDispatch} from 'react-redux';

import {uploadMethod} from '../../redux/actions/uploadMedia';
import locale from '../../assets/locale.json';
import checkPermission from '../../tools/permissions';
import {withStoragePermission, withCameraPermission} from '../HOC';
import CommonModal from '../Modal';
import colors from '../../assets/colors';
import {icons} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import Icon from '../Icon';
import InfoModal from '../Modal/InfoModal';
import { permissionStorage } from '../../tools/utils';

const PickImageDialog = ({
  visible,
  setVisible,
  onImageSelected,
  storagePermission,
  setStorageGranted: setStoragePermission,
  cameraPermission,
  setCameraPermission,
  name,
  canSelectFromGallery = true,
  canSelectFromCamera = true,
  maxFileSizeInBytes = 15 * 1024 * 1024,
  defaultCropWidth = 1080,
  defaultCropHeight = 1080,
}) => {
  const navigation = useNavigation();

  const [isPickPending, setIsPickPending] = React.useState({
    gallery: false,
    camera: false,
  });
  const [errorPopup, setErrorPopup] = React.useState({
    state: false,
    description: '',
  });
  const dispatch = useDispatch();

  const hide = React.useCallback(() => {
    setVisible(false);
  }, [setVisible]);

  const isImageSizeValid = React.useCallback(
    size => {
      if (size > maxFileSizeInBytes) {
        setErrorPopup({
          state: true,
          description: `${locale.Max} ${Number(
            maxFileSizeInBytes / (1024 * 1024),
          ).toFixed(2)}MB ${locale.fileSizeAllowed}`,
        });
        return false;
      }
      return true;
    },
    [maxFileSizeInBytes, hide],
  );

  const selectFromAlbum = React.useCallback(() => {
    dispatch(
      uploadMethod({
        name: name,
        value: 2,
      }),
    );

    if (storagePermission) {
      ImagePicker.openPicker({
        freeStyleCropEnabled: true,
        cropping: true,
        mediaType: 'photo',
        width: defaultCropWidth,
        height: defaultCropHeight,
      })
        .then(image => {
          hide();
          console.log("Kapil testing......", image)
          if (isImageSizeValid(image?.size)) {
            onImageSelected(image);
          }
        })
        .catch(error => console.log('Canceled by user'));
    } else {
      setIsPickPending(preState => ({...preState, gallery: true}));
      checkPermission(
        permissionStorage,
        setStoragePermission,
        true,
        true,
        false,
        navigation,
      );
    }
  }, [
    dispatch,
    name,
    storagePermission,
    onImageSelected,
    hide,
    setStoragePermission,
    navigation,
    isImageSizeValid,
    defaultCropWidth,
    defaultCropHeight,
  ]);

  const selectFromCamera = React.useCallback(() => {
    dispatch(
      uploadMethod({
        name: name,
        value: 1,
      }),
    );
    if (cameraPermission) {
      if (storagePermission) {
        ImagePicker.openCamera({
          freeStyleCropEnabled: true,
          cropping: true,
          mediaType: 'photo',
          width: defaultCropWidth,
          height: defaultCropHeight,
        })
          .then(image => {
            hide();

            if (isImageSizeValid(image?.size)) {
              onImageSelected(image);
            }
          })
          .catch(error => console.log('Canceled by user'));
      } else {
        setIsPickPending(preState => ({...preState, gallery: true}));
        checkPermission(
          permissionStorage,
          setStoragePermission,
          true,
          true,
          false,
          navigation,
        );
      }
    } else {
      setIsPickPending(preState => ({...preState, camera: true}));
      checkPermission(
        'camera',
        setCameraPermission,
        true,
        true,
        false,
        navigation,
      );
    }
  }, [
    dispatch,
    name,
    cameraPermission,
    storagePermission,
    onImageSelected,
    hide,
    setStoragePermission,
    navigation,
    setCameraPermission,
    isImageSizeValid,
    defaultCropWidth,
    defaultCropHeight,
  ]);

  React.useEffect(() => {
    if (storagePermission) {
      if (isPickPending.gallery) {
        selectFromAlbum();
        setIsPickPending(preState => ({...preState, gallery: false}));
      }
      if (cameraPermission) {
        if (isPickPending.camera) {
          selectFromCamera();
          setIsPickPending(preState => ({...preState, camera: false}));
        }
      }
    }
    if (cameraPermission && isPickPending.camera) {
      if (storagePermission) {
        selectFromCamera();
        setIsPickPending(preState => ({...preState, camera: false}));
      } else {
        checkPermission(
          permissionStorage,
          setStoragePermission,
          true,
          true,
          false,
          navigation,
        );
      }
    }
  }, [
    isPickPending,
    setIsPickPending,
    storagePermission,
    cameraPermission,
    selectFromAlbum,
    selectFromCamera,
    setStoragePermission,
    navigation,
  ]);

  return (
    <>
      <CommonModal
        width={280}
        visible={visible}
        setVisible={setVisible}
        height={100}>
        <View style={styles.modalTitle}>
          <Text
            style={{
              ...styles.itemText,
              color: colors.black,
              fontSize: fontSize.xl,
              textAlign: 'center',
              fontFamily: 'Gugi-Regular',
            }}>
            {locale.SelectFrom}:
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-evenly',
            alignItems: 'center',
            width: '100%',
            flexWrap: 'wrap',
          }}>
          {canSelectFromCamera ? (
            <TouchableOpacity
              onPress={selectFromCamera}
              style={styles.selectButton}>
              <Icon
                style={styles.iconStyle}
                name={icons.TAKE_PHOTO}
                tint={colors.primary}
                size={45}
              />
              <Text style={styles.itemText}>{locale.camera}</Text>
            </TouchableOpacity>
          ) : null}
          {canSelectFromGallery ? (
            <TouchableOpacity
              onPress={selectFromAlbum}
              style={styles.selectButton}>
              <Icon
                style={styles.iconStyle}
                name={icons.UPLOAD_PHOTO}
                tint={colors.primary}
                size={45}
              />
              <Text style={styles.itemText}>{locale.gallery}</Text>
            </TouchableOpacity>
          ) : null}
        </View>
      </CommonModal>
      <InfoModal
        delayInMs={150}
        setVisible={() => setErrorPopup({state: false, description: ''})}
        popUp={{
          isError: true,
          state: errorPopup.state,
          data: {title: locale.Error, description: errorPopup.description},
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  dialogTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  dialogLink: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',

    marginVertical: 5,
    borderRadius: 2,
  },
  itemText: {
    fontSize: 15,
    color: colors.primary,
  },
  closeButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    padding: spacings.lg,
  },
  iconStyle: {
    marginRight: spacings.md,
  },
  modalTitle: {
    width: '100%',
    marginVertical: 10,
  },
  selectButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default withCameraPermission(
  withStoragePermission(PickImageDialog, true, false),
  true,
  false,
  true,
);
