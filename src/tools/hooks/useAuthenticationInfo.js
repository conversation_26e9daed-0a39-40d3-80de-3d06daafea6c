import React from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {useQuery} from '@apollo/client';

import {GET_AUTHENTICATION_INFO} from '../../apollo/queries';
import {setAuthenticationTypesInfo} from '../../redux/actions/authentication';

const DEFAULT_OPTIONS = {
  isFocused: false,
};

export default function useAuthenticationInfo(options = DEFAULT_OPTIONS) {
  options = {...DEFAULT_OPTIONS, ...options};

  const {data, loading, error, refetch} = useQuery(GET_AUTHENTICATION_INFO);

  const typesFromReducer = useSelector(state => state?.authentication?.types);
  const dispatch = useDispatch();

  React.useEffect(() => {
    if (options.isFocused) {
      refetch?.();
    }
  }, [options.isFocused, refetch]);

  const types = data?.getAuthenticationInfo?.data?.type;

  React.useEffect(() => {
    if (Array.isArray(types)) {
      dispatch(setAuthenticationTypesInfo(types));
    }
  }, [types, dispatch]);

  return {
    authenticationTypes: typesFromReducer,
    data: data?.getAuthenticationInfo?.data,
    isLoading: loading,
    error,
  };
}
