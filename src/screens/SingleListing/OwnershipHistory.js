import {View, Text} from 'react-native';
import React from 'react';
import {useQuery} from '@apollo/client';
import dayjs from 'dayjs';
import {useNavigation, useFocusEffect} from '@react-navigation/native';

import {GET_AUTHENTICATION_OWNERSHIP_HISTORY} from '../../apollo/queries';
import ProfileImage from '../../components/ProfileImage';
import colors from '../../assets/colors';
import {styles} from './styles';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {useCredential} from '../../tools/hooks/useUser';
import {screens} from '../../assets/strings';

const LIMIT = 100;

export default function OwnershipHistory({postId, shouldRefresh = false}) {
  const {user_id: loggedInUserId} = useCredential();
  const navigation = useNavigation();

  const {data, refetch} = useQuery(GET_AUTHENTICATION_OWNERSHIP_HISTORY, {
    variables: {
      postId,
      limit: LIMIT,
    },
  });

  const ownershipHistory = React.useMemo(
    () => data?.getAuthenticationOwnershipHistory?.data?.edges ?? [],
    [data],
  );

  const onUserPressed = React.useCallback(
    user => {
      const isMember = user?.role === 'MEMBER';
      const isMyself = loggedInUserId == user?.id;
      navigation.push(
        user?.role === 'MEMBER'
          ? isMyself
            ? screens.USER_PROFILE
            : screens.PUBLIC_PROFILES
          : screens.EXPERTS_PROFILE,
        {
          ...(isMember ? {params: +user?.id} : {expertUserId: +user?.id}),
        },
      );
    },
    [navigation.push, loggedInUserId],
  );

  React.useEffect(() => {
    if (shouldRefresh) {
      refetch?.();
    }
  }, [shouldRefresh, refetch]);

  useFocusEffect(
    React.useCallback(() => {
      try {
        refetch?.();
      } catch (_) {}
    }, [refetch]),
  );

  return ownershipHistory?.length > 0 ? (
    <View style={{marginVertical: 10}}>
      <Text style={styles.descTitle}>Ownership History</Text>
      <View style={{marginVertical: 5}}>
        {ownershipHistory?.map(ownership => (
          <View
            key={ownership?.id?.toString?.()}
            style={{
              marginVertical: 5,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <TouchAwareButton
              style={{flexDirection: 'row', alignItems: 'center'}}
              onPress={() => onUserPressed(ownership?.user)}>
              <ProfileImage
                url={ownership?.user?.image}
                style={{marginRight: 10}}
              />
              <Text style={styles.provenanceTaggingInputLabel}>
                {ownership?.user?.username} -{' '}
              </Text>
            </TouchAwareButton>

            <Text>
              {dayjs(ownership?.date).isValid()
                ? dayjs(ownership?.date).format('MM/DD/YYYY')
                : ''}
            </Text>
            {ownership?.ownership_status === 'PENDING' ? (
              <Text
                style={{
                  color: colors.grey,
                  textTransform: 'capitalize',
                  fontStyle: 'italic',
                  marginLeft: 10,
                }}>
                {ownership?.ownership_status}
              </Text>
            ) : null}
          </View>
        ))}
      </View>
    </View>
  ) : null;
}
