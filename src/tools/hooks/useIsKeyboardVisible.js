import React from 'react';
import {Keyboard} from 'react-native';

const useIsKeyboardVisible = () => {
  const [isVisible, setIsVisible] = React.useState(false);

  React.useEffect(() => {
    const didShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsVisible(true);
    });

    const didHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsVisible(false);
    });

    return () => {
      didShowListener?.remove();
      didHideListener?.remove();
    };
  }, []);

  return isVisible;
};

export default useIsKeyboardVisible;
