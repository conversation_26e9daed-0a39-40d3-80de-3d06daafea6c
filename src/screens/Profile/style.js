import {StyleSheet, Platform} from 'react-native';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {fontSize} from '../../assets/font';

const isIOS = Platform.OS == 'ios';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
  },
  loginContainer: {
    padding: 20,
    backgroundColor: colors.white,
    flex: 1,
    alignItems: 'center',
  },
  submitButton: {
    backgroundColor: colors.primary,
    height: spacings.xxxl,
    marginTop: spacings.lg,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: spacings.sm,
  },
  buttonText: {
    color: colors.white,
    fontSize: fontSize.md,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  inputCard: {
    marginVertical: spacings.md,
  },
  inputLabel: {
    fontSize: fontSize.md,
    color: colors.black,
    marginBottom: 5,
    fontFamily: 'Inter-Medium',
  },
  inputField: {
    paddingLeft: spacings.md,
    color: colors.black,
    backgroundColor: colors.white,
    borderRadius: spacings.sm,
    fontSize: fontSize.md,
    ...(isIOS ? {padding: 15} : {}),
  },
  passwordWrapper: {
    width: '100%',
  },
  togglePassword: {
    position: 'absolute',
    right: 0,
    height: spacings.xxxl,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacings.md,
  },
  togglePasswordText: {
    fontSize: fontSize.sm,
    color: colors.black,
  },
  forgotPasswordText: {
    paddingVertical: spacings.md,
    fontSize: fontSize.sm,
    color: colors.black,
    textDecorationLine: 'underline',
  },
  forgotPasswordSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacings.sm,
    width: '100%',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
    color: colors.black,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.darkBadge,
  },
  modalView: {
    margin: 20,
    backgroundColor: colors.white,
    padding: spacings.xxxl,
    borderRadius: 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  buttonClose: {
    backgroundColor: colors.primary,
    borderRadius: spacings.sm,
  },
  textStyle: {
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  closeIcon: {
    padding: 10,
    position: 'absolute',
    left: 0,
  },
  errorText: {
    fontSize: 9,
    color: colors.warning,
    width: '100%',
    position: 'absolute',
  },
  errorWrapper: {
    width: '100%',
    marginTop: 5,
  },
});
