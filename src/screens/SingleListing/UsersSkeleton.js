import React from 'react';
import {View, Dimensions} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH - spacings.xl;

export default function UsersSkeleton() {
  return (
    <View
      style={{
        backgroundColor: colors.secondaryBlack,
        padding: spacings.lg,
      }}>
      <SkeletonPlaceholder speed={1000} highlightColor={colors.darkGrey}>
        {new Array(20).fill(100).map((_, index) => (
          <SkeletonPlaceholder.Item key={index.toString()} flexDirection="row">
            <SkeletonPlaceholder.Item
              key={index.toString()}
              width={50}
              height={50}
              borderRadius={25}
              marginRight={15}
              marginBottom={16}
            />
            <SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH / 1.5}
                marginBottom={10}
                height={10}
                borderRadius={10}
              />
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH / 2}
                height={8}
                borderRadius={10}
                marginBottom={10}
              />
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH / 3}
                height={5}
                borderRadius={10}
              />
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        ))}
      </SkeletonPlaceholder>
    </View>
  );
}
