import {AppState} from 'react-native';
import React from 'react';

const defaultOptions = {
  callback: () => null,
  callbackInvocationOn: ['ACTIVE', 'BACKGROUND'],
};

export default function useAppStateSubscription(options = defaultOptions) {
  options = {...defaultOptions, ...options};

  const [appState, setAppState] = React.useState(AppState.currentState);

  React.useEffect(() => {
    function appStateSubscriptionListener(nextAppState) {
      setAppState(nextAppState);
      if (
        options.callbackInvocationOn.includes(nextAppState?.toUpperCase?.())
      ) {
      }
      options.callback();
    }
    AppState.addEventListener('change', appStateSubscriptionListener);
    return () => {
      AppState.removeEventListener('change', appStateSubscriptionListener);
    };
  }, [options.callback, options.callbackInvocationOn]);

  return {state: appState};
}
