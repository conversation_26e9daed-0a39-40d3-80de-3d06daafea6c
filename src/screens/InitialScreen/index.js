import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import colors from '../../assets/colors';
import { fontSize } from '../../assets/font';
import { spacings } from '../../assets/spacing';
import { logo, screens, initialScreen } from '../../assets/strings';

const { width: WIDTH } = Dimensions.get('window');

const Index = ({ navigation }) => {
  return (
    <View style={styles.container}>
      <View style={styles.section1}>
        <Image style={styles.auth8Logo} source={logo.logoSecondary} />
      </View>
      <View style={styles.section2}>

        <View style={styles.defunkdWrapper}>
          {/* <Text style={styles.powered}>{initialScreen.POWERED_BY}</Text> */}
          {/* <Image style={styles.defunkdLogo} source={logo.DefunkdLogo} /> */}
          <Text style={styles.powered}>{""}</Text>
          <Image style={styles.defunkdLogo} />

        </View>

        <TouchableOpacity
          style={styles.button}
          onPress={() => navigation.navigate(screens.REGISTER)}>
          <Text style={styles.buttonText}>{initialScreen.CREATE_ACCOUNT}</Text>
        </TouchableOpacity>
        <View style={styles.loginQuestion}>
          <Text style={styles.question}>
            {initialScreen.ALREADY_HAVE_ACCOUNT}
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate(screens.LOGIN)}>
            <Text style={styles.login}>{initialScreen.SIGN_IN}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.black,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  auth8Logo: {
    height: WIDTH * 0.75,
    width: WIDTH * 0.75,
    resizeMode: 'contain',
  },
  defunkdLogo: {
    height: 30,
    width: WIDTH / 2,
    resizeMode: 'contain',
  },
  section1: {
    width: WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 6 / 10,
    padding: spacings.lg,
  },
  section2: {
    width: WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 4 / 10,
    padding: spacings.lg,
  },
  defunkdWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.xxl,
  },
  button: {
    backgroundColor: colors.primary,
    width: '100%',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: fontSize.lg,
    fontFamily: 'Inter-Regular',
    color: colors.white,
  },
  loginQuestion: {
    flexDirection: 'row',
    justifyContent: 'center',
    /* MODIFIED ID=1
     From
    alignItems: 'center',
    */
    ...(Platform.OS == 'ios' ? {} : { alignItems: 'center' }),
    marginTop: spacings.md,
    height: 50,
  },
  question: {
    color: colors.white,
    fontSize: fontSize.md,
  },
  login: {
    color: colors.secondary,
    textDecorationLine: 'underline',
    paddingHorizontal: spacings.md,
    textAlignVertical: 'center',
    height: 50,
    fontSize: fontSize.md,
    fontFamily: 'Inter-Regular',
  },
  powered: {
    fontSize: fontSize.md,
    color: colors.white,
    margin: spacings.md,
  },
});

export default Index;
