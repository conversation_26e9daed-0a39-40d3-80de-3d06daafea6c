import React, {useEffect, useState, useRef} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  ScrollView,
  Modal,
  Pressable,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Formik, Field} from 'formik';
import * as yup from 'yup';

import CustomInput from '../../components/AuthInput';
import {screens, registerScreen, icons} from '../../assets/strings';
import {styles} from './styles';
import colors from '../../assets/colors';
import Icon from '../../components/Icon';

const RegisterSchema = yup.object().shape({
  first_name: yup.string().required('Your first name is required'),
  last_name: yup.string().required('Your last name is required'),
  email: yup.string().email('Invalid email').required('The email is required'),
  password: yup
    .string()
    .min(8, ({min}) => `Password must be atleast ${min} characters`)
    .required('The password is required.'),
  confirm_password: yup
    .string()
    .oneOf([yup.ref('password'), null], "Password doesn't match")
    .required('Please confirm your password'),
});

const Index = ({navigation, isAccountRegistered, error, loading}) => {
  const isMounted = useRef(true);
  const [passwordHidden, setPasswordHidden] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [credentials, setCredentials] = useState('');

  const togglePassword = () => {
    setPasswordHidden(!passwordHidden);
  };

  useEffect(() => {
    isMounted.current = true;
    return () => {
      setModalVisible(false);
      isMounted.current = false;
    };
  }, []);

  useEffect(() => {
    if (isAccountRegistered === true) {
      setModalVisible(true);
    }
  }, [isAccountRegistered, credentials]);

  return (
    <ScrollView style={styles.srollViewWrapper}>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {}}>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Pressable
              style={styles.closeIcon}
              onPress={() => setModalVisible(false)}>
              <Icon name={icons.CANCEL_X} size={20} tint={colors.black} />
            </Pressable>
            <Text style={styles.modalText}>
              {registerScreen.ACCOUNT_REGISTRATION_TEXT}
            </Text>
            <TouchableOpacity style={[styles.button, styles.buttonClose]}>
              <Text style={styles.textStyle}>
                {registerScreen.PROCEED_TO_LOGIN_BUTTON}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <KeyboardAvoidingView behavior="position">
        <SafeAreaView style={styles.container}>
          <Formik
            validationSchema={RegisterSchema}
            initialValues={{
              first_name: '',
              last_name: '',
              email: '',
              password: '',
              confirm_password: '',
            }}
            onSubmit={async values => {
              if (isMounted.current === true) {
                setCredentials(values);
                // register(values);
              }
            }}>
            {({handleSubmit, isValid}) => (
              <>
                <View style={styles.loginContainer}>
                  <View style={styles.titleWrapper}>
                    <Text style={styles.titleStyle}>
                      {registerScreen.HEADER}
                    </Text>
                  </View>
                  <View style={styles.inputCard}>
                    <Text style={styles.inputLabel}>
                      {registerScreen.FIRST_NAME}
                    </Text>
                    <Field
                      style={styles.inputField}
                      component={CustomInput}
                      name="first_name"
                      placeholder="Your first name here"
                      keyboardType="email-address"
                    />
                  </View>
                  <View style={styles.inputCard}>
                    <Text style={styles.inputLabel}>
                      {registerScreen.LAST_NAME}
                    </Text>
                    <Field
                      style={styles.inputField}
                      component={CustomInput}
                      name="last_name"
                      placeholder="Your last name here"
                      keyboardType="email-address"
                    />
                  </View>
                  <View style={styles.inputCard}>
                    <Text style={styles.inputLabel}>
                      {registerScreen.EMAIL_LABEL}
                    </Text>
                    <Field
                      style={styles.inputField}
                      component={CustomInput}
                      name="email"
                      placeholder="<EMAIL>"
                      keyboardType="email-address"
                    />
                  </View>
                  <View style={styles.inputCard}>
                    <Text style={styles.inputLabel}>
                      {registerScreen.PASSWORD_LABEL}
                    </Text>
                    <View style={styles.passwordWrapper}>
                      <Field
                        style={styles.inputField}
                        component={CustomInput}
                        name="password"
                        placeholder="Re-type Password"
                        keyboardType="default"
                        secureTextEntry={passwordHidden}
                      />
                      <TouchableOpacity
                        style={styles.togglePassword}
                        onPress={togglePassword}>
                        <Text style={styles.togglePasswordText}>
                          {passwordHidden ? 'Show' : 'Hide'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View style={styles.inputCard}>
                    <Text style={styles.inputLabel}>
                      {registerScreen.CONFRIM_PASSWORD_LABEL}
                    </Text>
                    <Field
                      style={styles.inputField}
                      component={CustomInput}
                      name="confirm_password"
                      placeholder="Password"
                      keyboardType="default"
                      secureTextEntry={passwordHidden}
                    />
                  </View>
                  <View style={styles.loginButtonSection}>
                    <TouchableOpacity
                      onPress={() => navigation.navigate(screens.LOGIN)}>
                      <Text style={styles.loginButtonText}>
                        {registerScreen.LOGIN_BUTTON}
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <TouchableOpacity
                    style={styles.submitButton}
                    onPress={handleSubmit}>
                    {loading ? (
                      <ActivityIndicator color={colors.white} />
                    ) : (
                      <Text style={styles.buttonText}>
                        {registerScreen.BUTTON_LABEL}
                      </Text>
                    )}
                  </TouchableOpacity>
                  {error ? (
                    <View style={styles.errorWrapper}>
                      <Text style={styles.errorText}>{error.message}</Text>
                    </View>
                  ) : null}
                </View>
              </>
            )}
          </Formik>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </ScrollView>
  );
};

export default Index;
