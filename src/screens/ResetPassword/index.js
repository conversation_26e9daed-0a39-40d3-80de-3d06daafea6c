import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  Pressable,
  Modal,
  ActivityIndicator,
} from 'react-native';
import {Formik, Field} from 'formik';
import * as yup from 'yup';

import CustomInput from '../../components/AuthInput';
import {styles} from './style';
import {
  screens,
  resetPasswordScreen,
  passwordErrors,
  icons,
} from '../../assets/strings';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {RESET_PASSWORD} from '../../tools/api';
import Icon from '../../components/Icon';
import Layout from '../../layout';

const resetPasswordSchema = yup.object().shape({
  password: yup
    .string()
    .matches(/(?=.*\d)/, passwordErrors.INCLUDE_NUMBER)
    .matches(
      /((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})/,
      passwordErrors.INCLUDE_SPECIAL_CHARACTER,
    )
    .matches(/^(\S+$)/, passwordErrors.NO_SPACES)
    .min(8, passwordErrors.MINIMUM_CHARACTERS)
    .required(passwordErrors.REQUIRED),
  loginpassword: yup
    .string()
    .oneOf([yup.ref('password'), null], "Password doesn't match")
    .required('Please confirm your password'),
});

const Index = ({route, navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const isMounted = useRef(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmHidden, setConfirmHidden] = useState(true);

  const item = route.params.params;

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  const handlePasswordChange = useCallback(async data => {
    setIsLoading(true);
    try {
      const response = await RESET_PASSWORD(data);
      const res = response.data;
      if (res.success === true) {
        setIsLoading(false);
        setModalVisible(true);
      } else {
        setIsLoading(false);
      }
    } catch (e) {
      setError(e.response.data.message);
      setIsLoading(false);
    }
  }, []);

  const gotToLogin = () => {
    if (isMounted.current === true) {
      setModalVisible(false);
      setError(null);
    }
    navigation.navigate(screens.LOGIN);
  };

  const toggleConfirmPassword = () => {
    setConfirmHidden(!confirmHidden);
  };

  return (
    <Layout contentContainerStyle={styles.container}>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {}}>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text style={styles.modalText}>{resetPasswordScreen.SUBTITLE}</Text>
            <Pressable
              style={[styles.button, styles.buttonClose]}
              onPress={gotToLogin}>
              <Text style={styles.textStyle}>
                {resetPasswordScreen.PROCEED_TO_LOGIN_BUTTON}
              </Text>
            </Pressable>
          </View>
        </View>
      </Modal>
      <Formik
        validationSchema={resetPasswordSchema}
        initialValues={{
          code: item.code,
          email: item.email,
          password: '',
        }}
        onSubmit={async values => {
          handlePasswordChange(values);
        }}>
        {({handleSubmit}) => (
          <>
            <View style={styles.loginContainer}>
              <View style={styles.titleWrapper}>
                <Text style={styles.titleStyle}>
                  {resetPasswordScreen.TITLE}
                </Text>
              </View>

              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {resetPasswordScreen.PASSWORD}
                </Text>
                <View>
                  <Field
                    style={styles.inputField}
                    component={CustomPasswordComponent}
                    name="password"
                    placeholder="Password"
                    keyboardType="default"
                    autofocus
                  />
                </View>
              </View>
              <View style={styles.inputCard}>
                <Text style={styles.inputLabel}>
                  {resetPasswordScreen.CONFIRM_PASSWORD}
                </Text>
                <View style={{justifyContent: 'center'}}>
                  <Field
                    style={styles.inputField}
                    component={CustomInput}
                    name="loginpassword"
                    placeholder="Confirm Password"
                    keyboardType="default"
                    secureTextEntry={confirmHidden}
                    autofocus
                  />
                  <TouchableOpacity
                    style={[styles.togglePassword, {marginTop: 22}]}
                    onPress={toggleConfirmPassword}>
                    {confirmHidden ? (
                      <Icon
                        name={icons.EYE_OPEN}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    ) : (
                      <Icon
                        name={icons.EYE_CLOSED}
                        size={fontSize.xxxxl}
                        tint={colors.black}
                      />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmit}>
                {isLoading ? (
                  <ActivityIndicator color={colors.white} />
                ) : (
                  <Text style={styles.buttonText}>
                    {resetPasswordScreen.BUTTON_LABEL}
                  </Text>
                )}
              </TouchableOpacity>
              {error ? (
                <View style={styles.errorWrapper}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              ) : null}
            </View>
          </>
        )}
      </Formik>
    </Layout>
  );
};

function CustomPasswordComponent({...props}) {
  const [passwordHidden, setPasswordHidden] = useState(true);

  const togglePassword = () => {
    setPasswordHidden(!passwordHidden);
  };

  return (
    <View style={styles.passwordWrapper}>
      <CustomInput {...props} secureTextEntry={passwordHidden}>
        <TouchableOpacity
          style={styles.togglePassword}
          onPress={togglePassword}>
          {
            <Icon
              name={passwordHidden ? icons.EYE_OPEN : icons.EYE_CLOSED}
              size={fontSize.xxxxl}
              tint={colors.black}
            />
          }
        </TouchableOpacity>
      </CustomInput>
    </View>
  );
}

export default Index;
