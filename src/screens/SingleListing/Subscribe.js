import React from 'react';
import {Text, View} from 'react-native';
import {useMutation} from '@apollo/react-hooks';
import {
  SUBSCRIBE_TO_POST_UPDATES,
  UNSUBSCRIBE_FROM_POST_UPDATES,
} from '../../apollo/mutations';
import {showToast} from '../../components/Toast';
import locale from '../../assets/locale.json';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import ActivityIndicator from '../../components/ActivityIndicator';
import colors from '../../assets/colors';
import Icon from '../../components/Icon';

function SubscribeToPostUpdates({
  postId = null,
  isPostByMe = false,
  postUpdateSubscription = null,
  setVisible = () => null,
  refetchPost = () => null,
}) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSubscribed, setIsSubscribed] = React.useState(
    !(postUpdateSubscription == null),
  );

  const [subscribeToPostUpdates] = useMutation(SUBSCRIBE_TO_POST_UPDATES, {
    variables: {
      postId: +postId,
      allowOnNewVote: true,
      allowOnNewComment: true,
      allowOnFinalDecision: true,
    },
  });
  const [unsubscribeFromPostUpdates] = useMutation(
    UNSUBSCRIBE_FROM_POST_UPDATES,
    {
      variables: {
        postId: +postId,
      },
    },
  );

  const handleSubscribeToPostUpdates = React.useCallback(async () => {
    try {
      setIsLoading(true);
      const {data} = await (isSubscribed
        ? unsubscribeFromPostUpdates()
        : subscribeToPostUpdates());
      const target =
        data?.[
          isSubscribed ? 'unsubscribeFromPostUpdates' : 'subscribeToPostUpdates'
        ];
      const success = target?.success;
      const code = target?.code;
      let message = '';
      if (success) {
        await refetchPost();
        message = isSubscribed
          ? locale.UnsubscribedFromPostUpdates
          : locale.SuccessfullySubscribedToPostUpdates;
        setVisible(false);
        setIsSubscribed(previousState => !previousState);
      } else {
        if (code === 'ALREADY_SUBSCRIBED_TO_POST') {
          await refetchPost();
          message = locale.SuccessfullySubscribedToPostUpdates;
          setIsSubscribed(true);
        } else if (code === 'NOT_SUBSCRIBED_TO_POST') {
          await refetchPost();
          message = locale.UnsubscribedFromPostUpdates;
          setIsSubscribed(false);
        } else {
          throw new Error();
        }
      }
      setVisible(false);
      showToast({
        type: 'success',
        message: message,
      });
    } catch (_) {
      showToast({message: locale.SomethingWentWrongPleaseTryAgain});
    } finally {
      setIsLoading(false);
    }
  }, [
    subscribeToPostUpdates,
    unsubscribeFromPostUpdates,
    isSubscribed,
    setVisible,
    refetchPost,
  ]);

  return !isPostByMe ? (
    <TouchAwareButton
      containerStyle={{
        marginRight: 10,
        marginVertical: 5,
      }}
      scaleAnimationThreshold={isSubscribed ? 0.9 : 1.1}
      onPress={handleSubscribeToPostUpdates}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: isSubscribed ? colors.grey2 : colors.grey3,
        borderRadius: 10,
        marginLeft: 10,
        minWidth: 80,
        paddingHorizontal: 10,
      }}
      disabled={isLoading}>
      {isLoading ? (
        <ActivityIndicator size={20} style={{marginRight: 5}} />
      ) : (
        <Icon
          name={isSubscribed ? 'bell2Muted' : 'bell2'}
          style={{marginRight: 5}}
        />
      )}

      <Text
        style={{
          fontFamily: 'Inter-Bold',
          fontSize: 14,
          paddingVertical: 5,
        }}>
        {!isSubscribed ? locale.Subscribe : locale.Unsubscribe}
      </Text>
    </TouchAwareButton>
  ) : (
    <View style={{marginRight: 10}} />
  );
}

export default SubscribeToPostUpdates;
