export default {
  chevron: require('../../assets/svg/chevron.svg'),
  logo: require('../../assets/svg/svg-logo.svg'),
  minus: require('../../assets/svg/minus.svg'),
  plus: require('../../assets/svg/plus.svg'),
  front_image: require('../../assets/svg/front_image.svg'),
  closeup_front_image: require('../../assets/svg/closeup_front_image.svg'),
  back_image: require('../../assets/svg/back_image.svg'),
  front_tag_image: require('../../assets/svg/front_tag_image.svg'),
  back_tag_image: require('../../assets/svg/back_tag_image.svg'),
  copyright_image: require('../../assets/svg/copyright_image.svg'),
  closeup_back_image: require('../../assets/svg/closeup_back_image.svg'),
  lower_hem_stitching_image: require('../../assets/svg/lower_hem_stitching_image.svg'),
  extra: require('../../assets/svg/extra.svg'),
  arm_hem_stitching_image: require('../../assets/svg/arm_hem_stitching_image.svg'),
  messenger_unread: require('../../assets/svg/messenger_unread.svg'),
  messenger_read: require('../../assets/svg/messenger_read.svg'),
  magnifying_glass: require('../../assets/svg/magnifying_glass.svg'),
  notifications_read: require('../../assets/svg/notifications_read.svg'),
  notifications_unread: require('../../assets/svg/notifications_unread.svg'),
  favicon: require('../../assets/svg/favicon.svg'),
  dash: require('../../assets/svg/dash.svg'),
  cancel_x: require('../../assets/svg/cancel_x.svg'),
  cancel_x_2: require('../../assets/svg/cancel_x_2.svg'),
  pass_icon: require('../../assets/svg/pass_icon.svg'),
  pass_icon_2: require('../../assets/svg/pass_icon_2.svg'),
  people_outline: require('../../assets/svg/people_outline.svg'),
  price_tag: require('../../assets/svg/price_tag.svg'),
  how_it_works: require('../../assets/svg/how_it_works.svg'),
  send: require('../../assets/svg/send.svg'),
  checked_radio: require('../../assets/svg/checked_radio.svg'),
  unchecked_radio: require('../../assets/svg/unchecked_radio.svg'),
  our_experts: require('../../assets/svg/our_experts.svg'),
  add: require('../../assets/svg/add.svg'),
  authenticate_icon: require('../../assets/svg/authenticate_icon.svg'),
  comment_icon: require('../../assets/svg/comment_icon.svg'),
  community_icon: require('../../assets/svg/community_icon.svg'),
  credit_card: require('../../assets/svg/credit_card.svg'),
  draw_menu: require('../../assets/svg/draw_menu.svg'),
  edit_icon: require('../../assets/svg/edit_icon.svg'),
  elipsis_horizontal: require('../../assets/svg/elipsis_horizontal.svg'),
  elipsis_vertical: require('../../assets/svg/elipsis_vertical.svg'),
  home_icon: require('../../assets/svg/home_icon.svg'),
  logout: require('../../assets/svg/logout.svg'),
  open_eye: require('../../assets/svg/open_eye.svg'),
  person_icon: require('../../assets/svg/person_icon.svg'),
  questionmark_circle: require('../../assets/svg/questionmark_circle.svg'),
  right_photo_icon: require('../../assets/svg/right_photo_icon.svg'),
  search_icon: require('../../assets/svg/search_icon.svg'),
  share_icon: require('../../assets/svg/share_icon.svg'),
  success_check_icon: require('../../assets/svg/success_check_icon.svg'),
  trash_icon: require('../../assets/svg/trash_icon.svg'),
  wallet: require('../../assets/svg/wallet.svg'),
  wrong_photo_icon: require('../../assets/svg/wrong_photo_icon.svg'),
  arrow_right: require('../../assets/svg/arrow_right.svg'),
  arrow_left: require('../../assets/svg/arrow_left.svg'),
  eye_open: require('../../assets/svg/eye_open.svg'),
  eye_closed: require('../../assets/svg/eye_closed.svg'),
  password_correct_icon: require('../../assets/svg/password_correct_checkmark_circle.svg'),
  password_false_icon: require('../../assets/svg/password_false_checkmark_circle.svg'),
  chevron_down: require('../../assets/svg/chevron_down.svg'),
  upload_photo: require('../../assets/svg/upload_photo.svg'),
  take_photo: require('../../assets/svg/take_photo.svg'),
  featured_icon: require('../../assets/svg/featured_icon.svg'),
  reload: require('../../assets/svg/reload.svg'),
  noConnection: require('../../assets/svg/no_connection.svg'),
  nftCertification: require('../../assets/svg/nft_certification.svg'),
  expertCertified: require('../../assets/svg/expert_certification.svg'),
  backNavigation: require('../../assets/svg/back-navigation.svg'),
  delete: require('../../assets/svg/delete.svg'),
  blockUser: require('../../assets/svg/blockUser.svg'),
  contactUs: require('../../assets/svg/contactUs.svg'),
  lock: require('../../assets/svg/lock.svg'),
  nftEmblem: require('../../assets/svg/nft_emblem.svg'),
  expertAuthenticationEmblem: require('../../assets/svg/expert_authentication_emblem.svg'),
  checkmark: require('../../assets/svg/checkmark.svg'),
  chatBubble: require('../../assets/svg/chatBubble.svg'),
  copyToClipboard: require('../../assets/svg/copy-to-clipboard.svg'),
  like: require('../../assets/svg/like.svg'),
  likeFilled: require('../../assets/svg/likeFilled.svg'),
  threeDotsVertical: require('../../assets/svg/three-dots-vertical.svg'),
  bell: require('../../assets/svg/bell.svg'),
  faq: require('../../assets/svg/faq.svg'),
  bell2: require('../../assets/svg/bell2.svg'),
  bell2Muted: require('../../assets/svg/bell2Muted.svg'),
  share: require('../../assets/svg/share.svg'),
  qr_code: require('../../assets/svg/qr_code.svg'),
  ai_lens: require('../../assets/svg/ai_lens.svg'),
};
