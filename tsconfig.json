{"compilerOptions": {"allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "typeRoots": ["index.d.ts"], "types": ["node", "jest"]}, "noImplicitAny": false, "include": ["**/*.ts", "**/*.tsx", "AppProviders.js", "src/components/ImageViewer/Photo.js"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js", "**/*.test.tsx", "**/*.test.ts", "**/*.spec.tsx", "**/*.spec.ts"]}