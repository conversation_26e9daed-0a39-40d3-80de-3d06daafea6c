import React from 'react';
import {Text, View} from 'react-native';
import Toast, {BaseToast} from 'react-native-toast-message';

import ActivityIndicator from '../components/ActivityIndicator';

export default {
  error: ({text1, ...rest}) => (
    <BaseToast
      {...rest}
      style={{
        borderLeftColor: 'tomato',
        borderRadius: 50,
        height: 50,
        marginTop: 10,
      }}
      contentContainerStyle={{paddingHorizontal: 15}}
      text1Style={{
        fontSize: 14,
        fontWeight: '400',
        fontFamily: 'Inter-Medium',
      }}
      text1={text1}
      text1NumberOfLines={2}
      onTrailingIconPress={() => Toast.hide()}
    />
  ),
  success: ({text1, ...rest}) => (
    <BaseToast
      {...rest}
      style={{
        borderLeftColor: 'green',
        borderRadius: 50,
        height: 50,
        marginTop: 10,
      }}
      contentContainerStyle={{paddingHorizontal: 15}}
      text1Style={{
        fontSize: 14,
        fontWeight: '400',
        fontFamily: 'Inter-Medium',
      }}
      text1={text1}
      text1NumberOfLines={2}
      onTrailingIconPress={() => Toast.hide()}
    />
  ),
  loading: ({text1, activityIndicatorProps = {}}) => (
    <View
      style={{
        backgroundColor: 'white',
        borderRadius: 20,
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,
        paddingHorizontal: 20,
        shadowOffset: {width: -2, height: 4},
        shadowColor: '#171717',
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 5,
        justifyContent: 'space-between',
        marginTop: 10,
      }}>
      {text1?.length > 0 ? (
        <Text style={{fontFamily: 'Inter-Medium'}}>{text1}</Text>
      ) : null}
      <ActivityIndicator {...activityIndicatorProps} />
    </View>
  ),
};
