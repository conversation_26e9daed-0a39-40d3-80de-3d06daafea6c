import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  StatusBar,
  TouchableOpacity,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedScrollHandler,
  interpolate,
} from 'react-native-reanimated';
import {SafeAreaView, useSafeAreaInsets} from 'react-native-safe-area-context';
import {connect, useDispatch} from 'react-redux';

import IconButton from '../../components/Icon';
import {useWindowDimensions} from '../../tools/hooks';
import locale from '../../assets/locale.json';
import colors from '../../assets/colors';
import Logo from '../../assets/Images/logo-secondary.png';
import BadgesGroup from '../../assets/Images/badges-group.png';
import scale from '../../tools/scale';
import {handleOnBoarding} from '../../redux/actions/auth';

const slides = [
  {
    key: 2,
    title: '',
    description:
      'Join a community of people passionate about vintage t-shirts.',
  },
  {
    key: 3,
    title: '',
    description:
      'Submit your t-shirt and get free crowd-sourced feedback or have it authenticated by one of our experts.',
  },
  {
    key: 4,
    title: '',
    description:
      'Get involved by voting on the legitimacy of submissions and appraising their value. Submit a tag or search through our ever-expanding brand database.',
  },
  {
    key: 5,
    title: '',
    description:
      'Level up by voting regularly on authenticity and improve your accuracy score over time. We have 5 badge levels and you can even work toward joining our expert panel.',
  },
  {
    key: 6,
    title: '',
    description:
      'Explore your feed, comment, follow friends, and DM other users. Welcome to a pioneering social-based authentication platform.',
  },
];

function OnBoarding({onBoarding}) {
  const {
    window: {width, height},
  } = useWindowDimensions();
  const {bottom} = useSafeAreaInsets();

  const IMAGE_SIZE_LARGE = scale.moderateScale(width * 0.7);
  const IMAGE_SIZE_SMALL = scale.moderateScale(width * 0.7);

  const dispatch = useDispatch();

  const [currentIndex, setCurrentIndex] = React.useState(0);

  const scrollRef = React.useRef();

  const translateX = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler(event => {
    const scrollX = event.contentOffset.x;
    translateX.value = scrollX;
  });
  const animatedSize = useAnimatedStyle(() => {
    const PERSISTING_SIZE = IMAGE_SIZE_SMALL;
    const MAX_SIZE = IMAGE_SIZE_LARGE;

    const interpolatedSize =
      translateX.value <= width
        ? interpolate(translateX.value, [0, width], [MAX_SIZE, PERSISTING_SIZE])
        : translateX.value > 3 * width && translateX.value <= 5 * width
        ? interpolate(
            translateX.value,
            [3 * width, 4 * width],
            [PERSISTING_SIZE, MAX_SIZE],
          )
        : PERSISTING_SIZE;
    const PERSISTING_HEIGHT = 0;
    const interpolatedPositionX = PERSISTING_HEIGHT;

    const PERSISTING_LEFT = width / 2 - IMAGE_SIZE_SMALL / 2;
    const MAX_LEFT = width / 2 - IMAGE_SIZE_LARGE / 2;

    const interpolatedPositionY =
      translateX.value <= width
        ? interpolate(translateX.value, [0, width], [MAX_LEFT, PERSISTING_LEFT])
        : translateX.value > 3 * width && translateX.value <= 5 * width
        ? interpolate(
            translateX.value,
            [3 * width, 4 * width],
            [PERSISTING_LEFT, MAX_LEFT],
          )
        : PERSISTING_LEFT;

    return {
      top: interpolatedPositionX,
      width: interpolatedSize,
      height: interpolatedSize,
      left: interpolatedPositionY,
    };
  }, [translateX, height, width]);

  const handleArrowPress = React.useCallback(
    (isNext = true) => {
      if (isNext) {
        if (currentIndex <= slides.length - 2) {
          scrollRef.current?.scrollTo?.({
            x: width * (currentIndex + 1),
          });
          setCurrentIndex(previousIndex => previousIndex + 1);
        }
      } else {
        if (currentIndex !== 0) {
          scrollRef.current?.scrollTo?.({
            x: width * (currentIndex - 1),
          });
          setCurrentIndex(previousIndex => previousIndex - 1);
        }
      }
    },
    [currentIndex, width],
  );

  const completeOnboarding = React.useCallback(() => {
    dispatch(onBoarding());
  }, [dispatch, onBoarding]);

  const handleNext = React.useCallback(() => {
    if (currentIndex === slides.length - 1) {
      completeOnboarding();
    } else {
      handleArrowPress(true);
    }
  }, [completeOnboarding, handleArrowPress, currentIndex, slides.length]);

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: colors.black}}>
      <StatusBar backgroundColor={colors.black} barStyle="light-content" />
      <Animated.ScrollView
        ref={scrollRef}
        style={{flex: 1}}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={scrollHandler}
        onMomentumScrollEnd={evt => {
          let xOffset = evt.nativeEvent.contentOffset.x;
          let contentWidth = evt.nativeEvent.layoutMeasurement.width;
          let value = xOffset / contentWidth;
          if (currentIndex !== value) {
            setCurrentIndex(parseInt(value));
          }
        }}>
        {slides?.map((item, index) => (
          <PageView
            item={item}
            index={index}
            translateX={translateX}
            key={item?.key}
          />
        ))}
      </Animated.ScrollView>
      <Animated.Image
        source={currentIndex == 3 ? BadgesGroup : Logo}
        style={[
          {
            position: 'absolute',
            flex: 1,
            alignSelf: 'center',
            width: '100%',
            zIndex: -1,
            marginTop: Platform.OS === 'ios' ? 50 : 0,
          },
          animatedSize,
        ]}
        resizeMode="contain"
      />

      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom,
          width: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Text
          style={[styles.skipText, {padding: 10, alignSelf: 'auto'}]}
          onPress={completeOnboarding}>
          {locale.Skip}
        </Text>
      </TouchableOpacity>

      <View
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          bottom: bottom + 50,
          paddingHorizontal: 20,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={() => handleArrowPress(false)}
            disabled={currentIndex === 0}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              opacity: currentIndex === 0 ? 0 : 1,
              paddingVertical: 10,
              paddingRight: 10,
            }}>
            <IconButton
              name="arrow_left"
              style={{
                marginRight: 5,
              }}
              tint={colors.white}
              size={15}
            />
            <Text
              style={{
                fontSize: 15,
                color: colors.white,
                fontFamily: 'Inter-SemiBold',
              }}>
              Prev
            </Text>
          </TouchableOpacity>

          <View style={styles.dotsContainer}>
            {new Array(slides.length)?.fill(100)?.map((_, index) => (
              <View
                key={index?.toString()}
                style={[
                  styles.dot,
                  {opacity: currentIndex === index ? 1 : 0.5},
                ]}
              />
            ))}
          </View>
          <TouchableOpacity
            onPress={handleNext}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 10,
              paddingLeft: 10,
            }}>
            <Text
              style={{
                fontSize: 15,
                color: colors.white,
                marginRight: 5,
                fontFamily: 'Inter-SemiBold',
              }}>
              Next
            </Text>
            <IconButton name="arrow_right" size={15} tint={colors.white} />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

function PageView({item, index = 0, translateX}) {
  const {
    window: {width, height},
  } = useWindowDimensions();

  const animatedScale = useAnimatedStyle(() => {
    const interpolatedScale = interpolate(
      translateX.value,
      [(index - 1) * width, index * width, (index + 1) * width],
      [0.8, 1, 0.8],
    );
    return {transform: [{scale: interpolatedScale}]};
  });

  return (
    <Animated.View
      style={[
        styles.slide,
        {
          width,
          height,
          zIndex: 100000,
        },
        animatedScale,
      ]}
      key={item?.key}>
      <Text style={[styles.description]} allowFontScaling={false}>
        {item.description}
      </Text>
      {item?.background ? (
        <Animated.Image
          style={[{width}, styles.background]}
          source={item?.background}
          resizeMode="contain"
        />
      ) : null}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  description: {
    textAlign: 'center',
    color: colors.white,
    fontFamily: 'Inter-SemiBold',
    fontSize: scale.moderateScale(21),
    marginTop: 100,
    lineHeight: 26,
    paddingHorizontal: 20,
  },
  skipText: {
    fontSize: 14,
    color: colors.white,
    textAlign: 'center',
    fontFamily: 'Inter-SemiBold',
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: -1,
    transform: [{translateY: -scale.moderateScale(50)}],
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 8,
    backgroundColor: colors.white,
    marginHorizontal: 3,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const mapDispatchToProps = {
  onBoarding: handleOnBoarding,
};

export default connect(null, mapDispatchToProps)(OnBoarding);
