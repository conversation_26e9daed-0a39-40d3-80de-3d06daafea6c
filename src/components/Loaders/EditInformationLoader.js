/* eslint-disable prettier/prettier */
import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const EditInformationLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.3}
          marginBottom={10}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={20}
          width={WIDTH * 0.9}
          height={50}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.4}
          marginBottom={10}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={20}
          width={WIDTH * 0.9}
          height={50}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.4}
          marginBottom={10}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={20}
          width={WIDTH * 0.9}
          height={50}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.3}
          marginBottom={10}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={20}
          width={WIDTH * 0.9}
          height={50}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.4}
          marginBottom={10}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={20}
          width={WIDTH * 0.9}
          height={50}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          width={WIDTH * 0.3}
          marginBottom={10}
          height={10}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          marginBottom={20}
          width={WIDTH * 0.9}
          height={50}
          borderRadius={4}
        />
        <SkeletonPlaceholder.Item
          margmarginTop={30}
          width={WIDTH * 0.9}
          height={50}
          borderRadius={4}
        />
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    padding: spacings.lg,
  },
});

export default EditInformationLoader;
