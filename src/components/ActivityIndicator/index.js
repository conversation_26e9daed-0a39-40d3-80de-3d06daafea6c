import React from 'react';

import <PERSON><PERSON> from '../<PERSON><PERSON>';
import colors from '../../assets/colors';

export default function ActivityIndicator({
  style = {},
  size = 25,
  color = colors.primary,
}) {
  return (
    <Lottie
      name="activityIndicatorPrimary"
      style={[
        {
          width: size,
          height: size,
          marginLeft: 'auto',
          marginRight: 'auto',
        },
        style,
      ]}
      colorFilters={[
        {
          keypath: 'Shape Layer 1',
          color: color,
        },
      ]}
    />
  );
}
