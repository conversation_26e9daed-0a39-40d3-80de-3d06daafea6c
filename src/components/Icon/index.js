import React from 'react';
import {TouchableOpacity, View} from 'react-native';

import IconRegistry from './IconRegistry';

export default function Icon({
  name,
  size = 20,
  tint = 'black',
  backgroundColor = 'transparent',
  style = {},
  clickable = false,
  onPress = () => null,
  containerStyle = {},
}) {
  let IconName = null;

  if (name && typeof name === 'string' && !!IconRegistry[name]?.default) {
    IconName = React.createElement(IconRegistry[name].default, {
      height: size,
      width: size,
      style: {color: tint, backgroundColor, ...style},
    });
  }

  return clickable ? (
    <TouchableOpacity {...{onPress}} style={containerStyle}>
      {IconName}
    </TouchableOpacity>
  ) : (
    <View style={containerStyle}>{IconName}</View>
  );
}
