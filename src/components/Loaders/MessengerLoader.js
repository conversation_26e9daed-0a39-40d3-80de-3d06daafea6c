import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');

const MessengerLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder>
        {new Array(10).fill(1).map((_, index) => (
          <React.Fragment key={index}>
            <SkeletonPlaceholder.Item
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between">
              <SkeletonPlaceholder.Item
                flexDirection="row"
                marginVertical={20}
                alignItems="center">
                <SkeletonPlaceholder.Item
                  marginBottom={10}
                  marginRight={10}
                  width={50}
                  height={50}
                  borderRadius={50}
                />
                <SkeletonPlaceholder.Item>
                  <SkeletonPlaceholder.Item
                    marginBottom={10}
                    width={60}
                    height={10}
                    borderRadius={4}
                  />
                  <SkeletonPlaceholder.Item
                    marginBottom={10}
                    width={80}
                    height={10}
                    borderRadius={4}
                  />
                  <SkeletonPlaceholder.Item
                    marginBottom={10}
                    width={60}
                    height={10}
                    borderRadius={4}
                  />
                </SkeletonPlaceholder.Item>
              </SkeletonPlaceholder.Item>
              <SkeletonPlaceholder.Item
                marginBottom={10}
                marginRight={10}
                width={8}
                height={40}
                borderRadius={4}
              />
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              width={WIDTH}
              height={1}
              borderRadius={4}
            />
          </React.Fragment>
        ))}
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
});

export default MessengerLoader;
