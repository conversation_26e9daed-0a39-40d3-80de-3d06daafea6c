import React, {useCallback} from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  Dimensions,
  TextInput,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';

import AllBrandsLoader from '../../components/Loaders/AllBrandsLoader';
import colors from '../../assets/colors';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import {screens, images} from '../../assets/strings';
import QueryManager from '../../components/QueryManager';
import locale from '../../assets/locale.json';
import RefreshControl from '../../components/RefreshControl';
import ActivityIndicator from '../../components/ActivityIndicator';
import FastImage from '../../components/FastImage';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {usePaginatedBrands} from '../../tools/hooks';
import {useInteractionManager} from '../../tools/hooks';
import Icon from '../../components/Icon';

const {width} = Dimensions.get('window');
const WIDTH = width - spacings.lg;
const CARD_WIDTH = WIDTH / 2.2;
const LIMIT = 20;

const AllBrands = () => {
  const {isInteractionsComplete} = useInteractionManager({
    allowedPlatforms: ['android'],
  });
  const [term, setTerm] = React.useState('');

  const [refreshing, setRefreshing] = React.useState(false);
  const {
    data,
    handleFetchMore,
    loading,
    error,
    refetch,
    isFetchingMore,
    resetCursorRef,
    isSearchLoading,
  } = usePaginatedBrands({
    limit: LIMIT,
    searchTerm: term,
    notifyOnNetworkStatusChange: false,
  });

  const onRefresh = useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await refetch?.();
        resetCursorRef();
      } catch (error) {
      } finally {
        setRefreshing(false);
      }
    },
    [refetch, resetCursorRef],
  );

  return (
    <View style={styles.container}>
      <QueryManager
        {...{
          data: !(data == null) && isInteractionsComplete,
          error,
          refetch,
          loading: loading || !isInteractionsComplete,
        }}>
        <QueryManager.Data>
          <View style={styles.searchBarWrapper}>
            <Icon
              name="search_icon"
              size={fontSize.xxl}
              tint={colors.darkGrey}
            />
            <TextInput
              value={term}
              onChangeText={setTerm}
              selectionColor={colors.white}
              placeholder="Search"
              autoCapitalize="sentences"
              autoCorrect={false}
              returnKeyType="search"
              style={styles.searchBar}
              placeholderTextColor={colors.darkGrey}
            />
            {isSearchLoading ? (
              <ActivityIndicator
                size={20}
                style={{
                  position: 'absolute',
                  right: 5,
                }}
              />
            ) : null}
          </View>
          <BrandsList
            list={data?.getBrands?.data?.edges}
            handleFetchMore={handleFetchMore}
            isFetchingMore={isFetchingMore}
            onRefresh={onRefresh}
            refreshing={refreshing}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <AllBrandsLoader />
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale['SomethingWentWrong.']}
        />
      </QueryManager>
    </View>
  );
};

function renderItem({item}) {
  return <BrandItem item={item} />;
}

function keyExtractor(item) {
  return item.id.toString();
}

function BrandsList({
  list,
  isFetchingMore = false,
  handleFetchMore = () => null,
  refreshing = false,
  onRefresh = () => null,
}) {
  return (
    <FlatList
      data={list}
      renderItem={renderItem}
      numColumns={2}
      columnWrapperStyle={styles.contentContainer}
      keyExtractor={keyExtractor}
      ListFooterComponent={
        <>
          {isFetchingMore ? (
            <View style={{marginVertical: 10}}>
              <ActivityIndicator />
            </View>
          ) : null}
          <View style={{margin: spacings.lg}} />
        </>
      }
      onEndReachedThreshold={0.2}
      onEndReached={handleFetchMore}
      scrollToOverflowEnabled={true}
      scrollEventThrottle={1900}
      refreshControl={<RefreshControl {...{onRefresh, refreshing}} />}
      contentContainerStyle={{paddingBottom: 50}}
      initialNumToRender={10}
    />
  );
}

const BrandItem = React.memo(function ({item = null}) {
  const navigation = useNavigation();
  return (
    <TouchAwareButton
      style={styles.card}
      onPress={() =>
        navigation.push(screens.SINGLE_BRAND_SCREEN, {
          params: item,
        })
      }>
      <TouchAwareButton.View style={styles.imageWrapper}>
        <FastImage
          source={
            item?.image_url?.length ? {uri: item?.image_url} : images.logo1
          }
          style={styles.imageStyle}
        />
      </TouchAwareButton.View>
      <Text
        style={[
          styles.titleStyle,
          {fontFamily: 'Inter-Medium', width: CARD_WIDTH - 10},
        ]}
        ellipsizeMode="clip"
        numberOfLines={2}>
        {item?.name}
      </Text>
    </TouchAwareButton>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  imageWrapper: {
    height: CARD_WIDTH / 1.8,
    width: CARD_WIDTH,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacings.md,
  },
  imageStyle: {
    height: '100%',
    width: '100%',
    resizeMode: 'cover',
    borderRadius: 5,
    backgroundColor: colors.black,
  },
  titleStyle: {
    fontSize: fontSize.md,
    color: colors.black,
    fontWeight: 'bold',
  },
  contentContainer: {
    justifyContent: 'space-between',
    paddingHorizontal: spacings.lg,
  },
  card: {
    marginTop: spacings.lg,
  },
  errorContainerText: {
    fontSize: fontSize.lg,
    color: colors.black,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
  },
  searchBarWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.lightGrey,
    borderRadius: 10,
    paddingHorizontal: 10,
    margin: 20,
    marginBottom: 10,
  },
  searchBar: {
    height: spacings.xxl,
    paddingHorizontal: spacings.sm,
    fontSize: fontSize.md,
    color: colors.secondaryBlack,
    width: '85%',
  },
});

export default AllBrands;
