import React, {useState} from 'react';
import {Pressable, TouchableOpacity, FlatList, Text, View} from 'react-native';
import {useQuery, useLazyQuery} from '@apollo/client';
import {useFocusEffect} from '@react-navigation/native';

import HomeListingsLoader from '../../components/Loaders/HomeListingsLoader';
import Icon from '../../components/Icon';
import PublicProfilesHeader from './PublicProfilesHeader';
import UserActivityCard from '../../components/UserActivityCard';
import {
  GET_USER_FEED_ACTIVITY,
  GET_OTHER_USER_PROFILES,
  GET_ALL_POSTS,
} from '../../apollo/queries';
import {styles} from './styles';
import colors from '../../assets/colors';
import {screens, icons} from '../../assets/strings';
import {fontSize} from '../../assets/font';
import {spacings} from '../../assets/spacing';
import RefreshControl from '../../components/RefreshControl';
import QueryManager from '../../components/QueryManager';
import locale from '../../assets/locale.json';
import BottomSheet from '../../components/BottomSheet';
import ProfileLoader from '../../components/Loaders/ProfileLoader';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import {DefaultEmptyComponent} from '../../components/ListEmpty';
import InfoModal from '../../components/Modal/InfoModal';
import {APOLLO_CACHE_IDS} from '../../assets/strings';
import ActivityCard from '../../components/ActivityCard';

const LIMIT = 100;

const filterData = [
  {id: 1, name: 'Newest First', value: null},
  {id: 2, name: 'Voted Only | Fail', value: 'FAKE'},
  {id: 3, name: 'Voted Only | Pass', value: 'REAL'},
  {id: 4, name: 'Posted Only', value: null},
];

const PublicProfiles = ({route, navigation}) => {
  const scrollRef = React.useRef();

  const userId = route?.params?.params;
  const [
    userNotFoundModalVisible,
    setUserNotFoundModalVisible,
  ] = React.useState(false);

  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = React.useState(filterData[0]);

  const showPostsOnly = selectedFilter?.id == 4;

  const [getUserFeedActivity, {data, loading, error, refetch}] = useLazyQuery(
    GET_USER_FEED_ACTIVITY,
    {
      variables: {
        userId: userId,
        limit: LIMIT,
        filters: {
          ...(selectedFilter?.id == 3 || selectedFilter?.id == 2
            ? {decision: selectedFilter?.value}
            : {}),
        },
      },
    },
  );
  const [
    getUserPosts,
    {
      data: postsData,
      loading: postsLoading,
      error: postsError,
      refetch: refetchUserPosts,
    },
  ] = useLazyQuery(GET_ALL_POSTS, {
    variables: {
      limit: LIMIT,
      filters: {
        user_id: +userId,
      },
      cacheId: APOLLO_CACHE_IDS.getPosts_POSTS_BY_USER,
    },
  });

  const {
    data: userData,
    loading: userProfileLoading,
    error: userProfileError,
    refetch: refetchUserProfile,
  } = useQuery(GET_OTHER_USER_PROFILES, {
    variables: {
      id: userId,
    },
  });

  const isUserNotFound = React.useMemo(
    () =>
      (!(userData?.getUserProfile?.success == null) &&
        !userData?.getUserProfile?.success) ||
      userId == null,
    [userData, userId],
  );

  React.useEffect(() => {
    if (isUserNotFound) {
      setUserNotFoundModalVisible(true);
    }
  }, [isUserNotFound]);

  const handleSelection = item => {
    setSelectedFilter(item);
  };

  const fullRefetch = React.useCallback(
    () =>
      Promise.all([
        refetch(),
        selectedFilter.id == 4 ? refetchUserPosts() : refetchUserProfile(),
      ]),
    [refetch, refetchUserProfile, selectedFilter, refetchUserPosts],
  );

  const onRefresh = React.useCallback(
    async (enableRefreshControl = true) => {
      try {
        setRefreshing(enableRefreshControl);
        await fullRefetch();
      } catch (_) {
      } finally {
        setRefreshing(false);
      }
    },
    [fullRefetch],
  );

  React.useEffect(() => {
    (async function () {
      try {
        const {data: _data} = await (selectedFilter?.id == 4
          ? getUserPosts()
          : getUserFeedActivity());
      } catch (_) {}
    })();
  }, [selectedFilter, getUserPosts, getUserFeedActivity]);

  useFocusEffect(
    React.useCallback(() => {
      try {
        if (!scrollRef.current) {
          fullRefetch();
        }
      } catch (_) {}
    }, [fullRefetch]),
  );

  const handleUserNotFoundNavigation = React.useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.reset({
        index: 1,
        routes: [{name: screens.DRAW_NAVIGATOR}],
      });
    }
    setUserNotFoundModalVisible(false);
  }, [navigation]);

  const renderItem = React.useCallback(
    ({item}) => {
      return (
        <TouchAwareButton
          style={styles.cardsGroup}
          onPress={() =>
            navigation.push(screens.SINGLE_LISTING, {
              params: showPostsOnly ? item?.id : item?.post?.id,
            })
          }>
          {showPostsOnly ? (
            <ActivityCard item={item} />
          ) : (
            <UserActivityCard item={item} />
          )}
        </TouchAwareButton>
      );
    },
    [navigation.push, showPostsOnly],
  );
  const extractKey = React.useCallback(item => item?.id?.toString?.(), []);

  const lists = React.useMemo(
    () =>
      showPostsOnly
        ? postsData?.getPosts?.data?.edges
        : data?.getUserFeedActivity?.data?.edges,
    [
      data?.getUserFeedActivity?.data?.edges,
      postsData?.getPosts?.data?.edges,
      showPostsOnly,
    ],
  );

  return (
    <View style={styles.container}>
      <QueryManager
        data={(showPostsOnly ? postsData : data) || userData}
        error={userProfileError || (showPostsOnly ? postsError : error)}
        loading={userProfileLoading || (showPostsOnly ? postsLoading : loading)}
        refetch={fullRefetch}>
        <QueryManager.Data>
          <FlatList
            data={lists}
            columnWrapperStyle={styles.contentContainer}
            renderItem={renderItem}
            keyExtractor={extractKey}
            numColumns={2}
            ListHeaderComponent={
              <Header
                userId={userId}
                userData={userData?.getUserProfile?.data}
                selectedFilter={selectedFilter}
                handleSelection={handleSelection}
                refetch={refetchUserProfile}
              />
            }
            ListEmptyComponent={
              Array.isArray(lists) ? (
                <View style={styles.emptyView}>
                  <DefaultEmptyComponent
                    text={
                      showPostsOnly
                        ? locale.UserHasNotPostedYet
                        : locale.UserHasNoActivityYet
                    }
                    lottieStyle={{width: 100, height: 100, alignSelf: 'center'}}
                  />
                </View>
              ) : (showPostsOnly ? postsLoading : loading) ? (
                <HomeListingsLoader />
              ) : null
            }
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            onScroll={e => {
              scrollRef.current = e?.nativeEvent?.contentOffset?.y;
            }}
            initialNumToRender={10}
          />
        </QueryManager.Data>
        <QueryManager.Loading>
          <ProfileLoader />
          <View style={{marginVertical: 10}}>
            <HomeListingsLoader />
          </View>
        </QueryManager.Loading>
        <QueryManager.Error
          renderDefaultErrorComponent
          customErrorText={locale.SomethingWentWrongPleaseTryAgain}
          style={{marginVertical: 10}}
          errorLottieStyle={{width: 50, height: 50}}
        />
      </QueryManager>
      <InfoModal
        delayInMs={100}
        setVisible={handleUserNotFoundNavigation}
        popUp={{
          state: userNotFoundModalVisible,
          isError: true,
          data: {
            title: locale.AccountDoesNotExists,
            description: locale.UserNotFound,
          },
        }}
      />
    </View>
  );
};

function Header({
  userId,
  userData = null,
  selectedFilter,
  handleSelection = () => null,
  refetch = () => null,
}) {
  const [sheetVisible, setSheetVisible] = React.useState(false);

  const username = userData?.username;

  const handleRowSelection = item => {
    handleSelection(item);
    setSheetVisible(false);
  };

  return (
    <>
      <PublicProfilesHeader id={userId} data={userData} refetch={refetch} />
      <View style={styles.userActivityTitleWrapper}>
        <Text style={styles.userActivityTitle}>{username}'s Activity</Text>
        <TouchableOpacity
          style={styles.profileFilterButton}
          onPress={() => setSheetVisible(true)}>
          <Text style={styles.profileFilterButtonText}>
            {selectedFilter?.name}
          </Text>
          <Icon
            name={icons.CHEVRON_DOWN}
            size={fontSize.sm}
            tint={colors.darkGrey}
          />
        </TouchableOpacity>
      </View>
      <BottomSheet
        automaticallyAdjustHeight
        visible={sheetVisible}
        setVisible={setSheetVisible}>
        <View style={{padding: 20}}>
          <Text
            style={{
              color: colors.black,
              fontSize: fontSize.xl,
              fontWeight: 'bold',
            }}>
            Showing
          </Text>
          {filterData?.map(item => (
            <Pressable
              key={item?.id}
              style={{
                paddingVertical: spacings.md,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
              onPress={handleRowSelection.bind(null, item)}>
              <Text style={{color: colors.black, fontSize: fontSize.lg}}>
                {item?.name}
              </Text>
              <Icon
                name={
                  icons[
                    selectedFilter?.id == item?.id
                      ? 'CHECKED_RADIO'
                      : 'UNCHECKED_RADIO'
                  ]
                }
                size={20}
                tint={
                  selectedFilter?.id == item?.id ? colors.primary : colors.grey
                }
              />
            </Pressable>
          ))}
        </View>
      </BottomSheet>
    </>
  );
}

export default PublicProfiles;
