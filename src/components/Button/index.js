import React from 'react';
import {Text, StyleSheet, TouchableOpacity} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withSequence,
  Easing,
  useDerivedValue,
  withTiming,
} from 'react-native-reanimated';

import colors from '../../assets/colors';
import Color from 'color';
import ActivityIndicator from '../../components/ActivityIndicator';

const SPRING_CONFIG = {
  damping: 800,
  stiffness: 100,
  mass: 0.001,
};

const Button = ({
  children = null,
  text = '',
  subText = '',
  disabled = false,
  isLoading = false,
  onPress = () => null,
  onLongPress = () => {},
  disableDisableColor = false,
  indicatorSize = 25,
  indicatorColor = colors.white,
  disableTapAnimation = false,
  containerStyle = {},
  style: _style = {},
  textStyle: _textStyle = {},
  subTextStyle: _subTextStyle = {},
  indicatorStyle = {},
  scaleAnimationThreshold = 0.95,
}) => {
  const scale = useSharedValue(1);

  const scaleTraversed = useDerivedValue(() => {
    const value = withTiming(scale.value, {
      duration: 160,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
    return value;
  });
  const transform = useAnimatedStyle(() => ({
    transform: [{scale: scaleTraversed.value}],
  }));

  const style = {
    ...styles.buttonStyle,
    ..._style,
  };

  const handleOnPress = React.useCallback(() => {
    if (!disableTapAnimation) {
      scale.value = withSequence(
        withSpring(scaleAnimationThreshold, SPRING_CONFIG),
        withSpring(1),
      );
    }
    onPress();
  }, [onPress, scaleAnimationThreshold]);

  return (
    <Animated.View style={[transform, containerStyle]}>
      <TouchableOpacity
        style={[
          styles.buttonStyle,
          style,
          {
            ...(disabled
              ? {
                  backgroundColor: Color(style.backgroundColor)
                    .darken(disableDisableColor ? 0 : 0.3)
                    .hex(),
                  opacity: 0.8,
                }
              : {}),
          },
        ]}
        disabled={disabled}
        onPress={handleOnPress}
        activeOpacity={1}
        onLongPress={onLongPress}>
        {isLoading ? (
          <ActivityIndicator
            size={indicatorSize}
            color={indicatorColor}
            style={[indicatorStyle]}
          />
        ) : (
          children ?? (
            <>
              <Text style={[styles.text, _textStyle]}>{text}</Text>
              {subText ? (
                <Text style={[styles.subText, _subTextStyle]}>{subText}</Text>
              ) : null}
            </>
          )
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  text: {
    fontSize: 17,
    textAlign: 'center',
    color: colors.white,
    fontFamily: 'Inter-Medium',
  },
  subText: {
    fontSize: 13,
    textAlign: 'center',
    color: colors.white,
  },
  buttonStyle: {
    backgroundColor: colors.primary,
    marginHorizontal: 'auto',
    minWidth: 100,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    minHeight: 40,
  },
});

export default Button;
