// thunks.js
import axios from 'axios';
import {API_AI_PREDICT_URL, API_AI_PREDICT_URL_DEV, API_AI_PREDICT_EXPERT_URL, API_AI_PREDICT_EXPERT_URL_DEV} from '@env';
import { uploadImagesRequest, uploadImagesSuccess, uploadImagesFailure } from './../redux/actions/aiUploadMedia';

// Helper function to get a Blob from a remote image URL
const fetchImageAsBlob = async (url) => {
  const response = await axios.get(url, { responseType: 'blob' });
  return response.data;
};


export const uploadImages = (imageUrls, type) => {
  return async (dispatch) => {
    dispatch(uploadImagesRequest());
    try {
      const formData = new FormData();

      // Fetch each image as Blob and append to formData
      for (let i = 0; i < imageUrls.length; i++) {
        const blob = await fetchImageAsBlob(imageUrls[i]);
        formData.append('images[]', {
          uri: imageUrls[i], // Some servers accept just the URI
          type: blob.type || 'image/jpeg',
          name: `image_${i}.jpg`,
          data: blob,
        });
      }


      const URL = '';
      if(type == 0) { // Normal AI
        URL = process.env.NODE_ENV === 'development' ? API_AI_PREDICT_URL_DEV : API_AI_PREDICT_URL
      } else { // AI WITH EXPERT
        URL = process.env.NODE_ENV === 'development' ? API_AI_PREDICT_EXPERT_URL_DEV : API_AI_PREDICT_EXPERT_URL
      }

      const response = await axios.post(URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status !== 200) {
        throw new Error('Upload failed!');
      }

      dispatch(uploadImagesSuccess());
    } catch (error) {
      dispatch(uploadImagesFailure(error.message));
    }
  };
};
