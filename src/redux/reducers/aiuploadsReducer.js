import actionTypes from '../actionTypes';

const initialState = {
  uploading: false,
  error: null,
};

const uploadReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.UPLOAD_AI_IMAGES_REQUEST:
      return {
        ...state,
        uploading: true,
        error: null,
      };
    case actionTypes.UPLOAD_AI_IMAGES_SUCCESS:
      return {
        ...state,
        uploading: false,
        error: null,
      };
    case actionTypes.UPLOAD_AI_IMAGES_FAILURE:
      return {
        ...state,
        uploading: false,
        error: action.payload,
      };
    default:
      return state;
  }
};

export default uploadReducer;
