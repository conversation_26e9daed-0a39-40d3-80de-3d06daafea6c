import React from 'react';
import {StyleSheet} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';

import AllActivity from '../screens/AllActivity';
import FollowingFeed from '../screens/FollowingFeed';
import colors from '../assets/colors';
import {screens, navLabels} from '../assets/strings';
import {fontSize} from '../assets/font';
import {useUser, useUserPushNotificationSettings} from '../tools/hooks';

const TopTabs = createMaterialTopTabNavigator();

const MaterialTopNavigator = () => {
  useUser({focused: true});
  useUserPushNotificationSettings({focused: true});

  return (
    <TopTabs.Navigator
      initialRouteName={screens.ALL_ACTIVITY}
      screenOptions={{
        swipeEnabled: false,
        tabBarActiveTintColor: colors.black,
        tabBarIndicatorStyle: styles.indicator,
        tabBarStyle: styles.barStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
      }}>
      <TopTabs.Screen
        name={screens.ALL_ACTIVITY}
        component={AllActivity}
        options={{
          tabBarLabel: navLabels.ALL_ACTIVITY,
        }}
      />
      <TopTabs.Screen
        name={screens.FOLLOWING_FEED}
        component={FollowingFeed}
        options={{tabBarLabel: navLabels.FOLLOWING_FEED}}
      />
    </TopTabs.Navigator>
  );
};

const styles = StyleSheet.create({
  indicator: {
    backgroundColor: colors.primary,
    borderRadius: 25,
  },
  barStyle: {
    backgroundColor: colors.white,
    elevation: 1,
    borderTopWidth: 3,
    borderTopColor: colors.lightGrey,
  },
  tabBarLabelStyle: {
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
    fontWeight: '700',
  },
});

export default MaterialTopNavigator;
