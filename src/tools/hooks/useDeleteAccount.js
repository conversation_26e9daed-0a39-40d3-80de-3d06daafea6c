import React from 'react';
import {Platform} from 'react-native';
import {useDispatch} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import FastImage from 'react-native-fast-image';
import {useNetInfo} from '@react-native-community/netinfo';
import {useApolloClient, useMutation} from '@apollo/client';
import DeviceInfo from 'react-native-device-info';

import {Logout} from '../../redux/actions/auth';
import {persistent} from '../../assets/strings';
import {DEACTIVATE_DEVICE_FOR_NOTIFICATIONS} from '../../apollo/mutations';
import { SEND_DELETE_ACCOUNT } from '../api';

const OPTIONS = {onError: () => null};

export default function useDeleteAccount(options = OPTIONS) {
  const {onError} = {...OPTIONS, ...options};
  const [isDeleteLoading, setIsDeleteLoading] = React.useState(false);

  const {isInternetReachable} = useNetInfo();

  const client = useApolloClient();
  const persistor = client?.persistor;
  const dispatch = useDispatch();

  const [unSyncDevice] = useMutation(DEACTIVATE_DEVICE_FOR_NOTIFICATIONS);

  const handleDelete = React.useCallback(async () => {
    setIsDeleteLoading(true)
    try {
      if (!isInternetReachable) {
        throw new Error('No Internet');
      }

      try {
        
        const deviceId = DeviceInfo.getDeviceId();
        await unSyncDevice({variables: {deviceId}});

        const response = await SEND_DELETE_ACCOUNT()
        const res = response?.data;
        console.log("res", res?.message);
      } catch (_) {}


      await messaging().deleteToken();
      const asyncStorageKeys = await AsyncStorage.getAllKeys();
      if (asyncStorageKeys.length > 0) {
        if (Platform.OS === 'android') {
          await AsyncStorage.clear();
        }
        if (Platform.OS === 'ios') {
          await AsyncStorage.multiRemove(asyncStorageKeys);
        }
      }
      await AsyncStorage.setItem(persistent.CAN_FORCE_LOGOUT, '0');

      await client.cache.reset();
      await client.clearStore();
      await persistor.purge();
      await persistor.persist();

      await FastImage.clearDiskCache();
      await FastImage.clearMemoryCache();

      await AsyncStorage.setItem(persistent.CAN_FORCE_LOGOUT, '1');

      dispatch(Logout());
    } catch (error) {
      await AsyncStorage.setItem(persistent.CAN_FORCE_LOGOUT, '1');
      onError?.(error);
    } finally {
      setIsDeleteLoading(false);
    }
  }, [dispatch, onError, client, persistor, isInternetReachable]);

  return {deleteAccount: handleDelete, isDeleteLoading, setIsDeleteLoading};
}
