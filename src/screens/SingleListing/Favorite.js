import React from 'react';
import {Text, View, Platform} from 'react-native';
import {useMutation} from '@apollo/client';
import {useNavigation} from '@react-navigation/native';

import {LIKE_POST, UNLIKE_POST} from '../../apollo/mutations';
import TouchAwareButton from '../../components/Button/TouchAwareButton';
import Icon from '../../components/Icon';
import colors from '../../assets/colors';
import {formatDigits} from '../../tools/utils';
import locale from '../../assets/locale.json';
import {screens} from '../../assets/strings';

function Favorite({
  postId = null,
  isLiked: _isLiked = false,
  numberOfLikes: _numberOfLikes = 0,
}) {
  const navigation = useNavigation();

  const [isLiked, setIsLiked] = React.useState(_isLiked);
  const [numberOfLikes, setNumberOfLikes] = React.useState(_numberOfLikes ?? 0);

  const likeDebounceRef = React.useRef();
  const lastIsLikedStateRef = React.useRef(_isLiked);
  const lastNumberOfLikesStateRef = React.useRef(_numberOfLikes ?? 0);

  const [likePost] = useMutation(LIKE_POST, {
    variables: {
      postId,
    },
  });

  const [unlikePost] = useMutation(UNLIKE_POST, {
    variables: {
      postId,
    },
  });

  const likeUnlikePost = React.useCallback(
    async (toBeLiked = true, errorCB = () => null) => {
      try {
        const {data} = await (toBeLiked ? likePost() : unlikePost());

        const response = data?.[toBeLiked ? 'likePost' : 'unlikePost'];
        const isSuccess = response?.success;
        const code = response?.code;

        if (!isSuccess) {
          if (code === 'POST_ALREADY_LIKED') {
            setIsLiked(true);
            lastIsLikedStateRef.current = true;
          } else if (code === 'POST_NOT_LIKED') {
            setIsLiked(false);
            lastIsLikedStateRef.current = false;
          } else {
            errorCB();
          }
        }
      } catch (_) {
        errorCB();
      }
    },
    [likePost, unlikePost],
  );

  const handleLikeUnlike = React.useCallback(() => {
    let lastState = false;
    setIsLiked(preState => {
      lastState = preState;
      setNumberOfLikes(previousLikes =>
        lastState ? previousLikes - 1 : previousLikes + 1,
      );
      return !lastState;
    });

    lastIsLikedStateRef.current = !lastState;
    lastNumberOfLikesStateRef.current = lastState
      ? lastNumberOfLikesStateRef.current - 1
      : lastNumberOfLikesStateRef.current + 1;

    if (likeDebounceRef.current) {
      clearTimeout(likeDebounceRef.current);
    }
    likeDebounceRef.current = setTimeout(() => {
      likeUnlikePost(!lastState, () => {
        setIsLiked(lastState);
        lastIsLikedStateRef.current = lastState;

        setNumberOfLikes(previousLikes =>
          lastState ? previousLikes.current + 1 : previousLikes.current - 1,
        );
        lastNumberOfLikesStateRef.current = lastState
          ? previousLikes + 1
          : previousLikes - 1;
      });
    }, 300);
  }, [likeUnlikePost]);

  const handlePostLikes = React.useCallback(
    _numberOfLikes => {
      navigation.push(screens.POST_LIKES, {
        numberOfLikes: _numberOfLikes,
        postId,
      });
    },
    [navigation.push, postId],
  );

  React.useEffect(() => {
    if (_isLiked !== lastIsLikedStateRef.current) {
      setIsLiked(_isLiked);
      lastIsLikedStateRef.current = _isLiked;
    }
  }, [_isLiked]);

  React.useEffect(() => {
    if (_numberOfLikes !== lastNumberOfLikesStateRef.current) {
      setNumberOfLikes(_numberOfLikes);
      lastNumberOfLikesStateRef.current = _numberOfLikes;
    }
  }, [_numberOfLikes]);

  React.useEffect(
    () => () => {
      clearTimeout(likeDebounceRef.current);
    },
    [],
  );

  return (
    <>
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
          marginVertical: 5,
        }}>
        <TouchAwareButton
          scaleAnimationThreshold={isLiked ? 0.9 : 1.1}
          onPress={handleLikeUnlike}
          style={{
            paddingRight: 10,
          }}>
          <Icon
            name={isLiked ? 'likeFilled' : 'like'}
            tint={isLiked ? colors.red : colors.transparent}
            size={30}
          />
        </TouchAwareButton>
      </View>
      <TouchAwareButton
        style={{
          backgroundColor: colors.grey3,
          borderRadius: 10,
          justifyContent: 'center',
        }}
        scaleAnimationThreshold={isLiked ? 0.9 : 1.1}
        onPress={() => handlePostLikes(numberOfLikes)}>
        <Text
          style={{
            fontFamily: 'Inter-Bold',
            fontSize: 14,
            paddingHorizontal: 10,
            paddingVertical: 5,
          }}>
          {formatDigits(numberOfLikes) ?? ''}{' '}
          {`${locale.Like}${numberOfLikes > 1 ? 's' : ''}`}
        </Text>
      </TouchAwareButton>
    </>
  );
}

export default Favorite;
