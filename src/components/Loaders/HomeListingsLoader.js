import React from 'react';
import {StyleSheet, Dimensions, View} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';

const {width: WIDTH} = Dimensions.get('window');
const CARD_WIDTH = WIDTH / 2 - spacings.xl;

const AllActivityLoader = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder speed={1000}>
        {new Array(5)?.fill(100)?.map((_, index) => (
          <SkeletonPlaceholder.Item
            key={index?.toString?.()}
            flexDirection="row"
            justifyContent="space-between">
            <SkeletonPlaceholder.Item
              alignItems="center"
              justifyContent="center">
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH}
                marginBottom={6}
                height={CARD_WIDTH / 1.5}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item>
                <SkeletonPlaceholder.Item
                  marginBottom={6}
                  width={CARD_WIDTH}
                  height={10}
                  borderRadius={4}
                />
                <SkeletonPlaceholder.Item
                  alignItems="center"
                  flexDirection="row"
                  marginBottom={6}>
                  <SkeletonPlaceholder.Item
                    marginRight={10}
                    width={20}
                    height={20}
                    borderRadius={50}
                  />
                  <SkeletonPlaceholder.Item
                    width={CARD_WIDTH / 3}
                    height={15}
                    borderRadius={4}
                  />
                </SkeletonPlaceholder.Item>
                <SkeletonPlaceholder.Item
                  marginBottom={20}
                  width={CARD_WIDTH / 3}
                  height={10}
                  borderRadius={4}
                />
              </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder.Item>
            <SkeletonPlaceholder.Item
              alignItems="center"
              justifyContent="center">
              <SkeletonPlaceholder.Item
                width={CARD_WIDTH}
                marginBottom={6}
                height={CARD_WIDTH / 1.5}
                borderRadius={4}
              />
              <SkeletonPlaceholder.Item>
                <SkeletonPlaceholder.Item
                  marginBottom={6}
                  width={CARD_WIDTH}
                  height={10}
                  borderRadius={4}
                />
                <SkeletonPlaceholder.Item
                  alignItems="center"
                  flexDirection="row"
                  marginBottom={6}>
                  <SkeletonPlaceholder.Item
                    marginRight={10}
                    width={20}
                    height={20}
                    borderRadius={50}
                  />
                  <SkeletonPlaceholder.Item
                    width={CARD_WIDTH / 3}
                    height={15}
                    borderRadius={4}
                  />
                </SkeletonPlaceholder.Item>
                <SkeletonPlaceholder.Item
                  marginBottom={20}
                  width={CARD_WIDTH / 3}
                  height={10}
                  borderRadius={4}
                />
              </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder.Item>
        ))}
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: spacings.lg,
    paddingBottom: spacings.lg,
  },
});

export default AllActivityLoader;
