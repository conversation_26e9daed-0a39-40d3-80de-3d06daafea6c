import React, {useLayoutEffect} from 'react';
import {StyleSheet, Pressable} from 'react-native';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import {DrawerActions} from '@react-navigation/native';

import Icon from '../../components/Icon';
import Transactions from './Transactions';
import CreditCards from './CreditCards';
import colors from '../../assets/colors';
import {spacings} from '../../assets/spacing';
import {screens, icons, navLabels} from '../../assets/strings';
import {fontSize} from '../../assets/font';

const TopTabs = createMaterialTopTabNavigator();

const Index = ({navigation}) => {
  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: 'Billing',
      headerTintColor: '#fff',
      headerRight: null,
      headerLeft: () => (
        <Pressable
          style={styles.leftIconWrapper}
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}>
          <Icon
            name={icons.ARROW_LEFT}
            tint={colors.white}
            size={fontSize.xxxl}
          />
        </Pressable>
      ),
    });
  }, []);

  return (
    <TopTabs.Navigator
      initialRouteName={screens.TRANSACTIONS}
      screenOptions={{
        swipeEnabled: false,
        tabBarActiveTintColor: colors.black,
        tabBarIndicatorStyle: styles.indicator,
        tabBarStyle: styles.barStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
      }}>
      <TopTabs.Screen
        name={screens.TRANSACTIONS}
        component={Transactions}
        options={{
          tabBarLabel: navLabels.TRANSACTIONS,
        }}
      />
      <TopTabs.Screen
        name={screens.CREDIT_CARDS}
        component={CreditCards}
        options={{tabBarLabel: navLabels.CREDIT_CARDS}}
      />
    </TopTabs.Navigator>
  );
};

const styles = StyleSheet.create({
  indicator: {
    backgroundColor: colors.primary,
    borderRadius: 25,
  },
  barStyle: {
    backgroundColor: colors.white,
    elevation: 1,
  },
  tabBarLabelStyle: {
    fontSize: fontSize.sm,
    textTransform: 'capitalize',
    fontFamily: 'Inter-Medium',
  },
  leftIconWrapper: {
    paddingHorizontal: spacings.lg,
    paddingVertical: spacings.sm,
  },
});

export default Index;
