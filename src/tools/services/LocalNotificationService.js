import {Platform} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import notifee, {AndroidColor} from '@notifee/react-native';
import {navigationRef} from '../../navigators/NavigationService';
import {
  mapNotificationScreens,
  shouldDisableForegroundNotification,
} from './NotificationNavigationService';
import {persistent} from '../../assets/strings';

const channels = {
  default: 'default',
  public: 'public',
};

const isIOS = Platform.OS === 'ios';

const defaultOptions = {
  showNotification: {
    android: {
      channelId: channels.default,
      largeIcon: require('../../assets/Images/logo-only.png'),
      smallIcon: 'ic_primary',
      sound: 'default',
      color: '#327BE3',
      vibrationPattern: [500, 500],
      lights: [AndroidColor.RED, 300, 600],
    },
    ios: {
      sound: 'default',
      categoryId: channels.default,
      foregroundPresentationOptions: {sound: false},
    },
  },
};

class LocalNotificationService {
  constructor() {
    notifee.createChannel({
      id: channels.default,
      name: 'Default Channel',
    });
    if (isIOS) {
      notifee.setNotificationCategories([
        {
          id: channels.default,
          actions: [
            {
              id: 'default',
              foreground: true,
              title: 'View',
            },
          ],
        },
      ]);
    }
  }

  async onRegisterListener(fcmToken) {
    if (__DEV__) {
      console.log('[LocalNotificationService]: onRegisterListener', fcmToken);
    }
    if (fcmToken?.length) {
      try {
        await Promise.all([
          AsyncStorage.setItem(persistent.FCM_TOKEN, fcmToken),
        ]);
      } catch (error) {
        if (__DEV__) {
          console.error('Error onRegisterListener', error);
        }
      }
    }
  }

  async onNotificationListener(notification) {
    if (__DEV__) {
      console.log(
        '[LocalNotificationService]: onNotificationListener',
        JSON.stringify(notification, null, 2),
      );
    }

    const actualNotification = notification?.notification;

    const notificationProperties = {
      title: actualNotification?.title ?? '',
      body: actualNotification?.body ?? '',
      android: {
        ...(actualNotification?.android ?? {}),
        pressAction: {
          id: 'default',
        },
      },
      ios: actualNotification?.ios ?? {},
      data: notification?.data ?? {},
    };

    const userId = notification?.data?.receiverUserId;
    const type = notification?.data?.type;
    try {
      if (
        type === 'MASS_NOTIFICATION' ||
        type?.startsWith?.('SUBSCRIPTION@') ||
        +(await AsyncStorage.getItem(persistent.USER_ID)) === +userId
      ) {
        const shouldDisableNotification = await shouldDisableForegroundNotification(
          notification,
          {
            userId: notification?.data?.fromUserId,
          },
        );
        if (!shouldDisableNotification) {
          this.showNotification(notificationProperties);
        }
      }
    } catch (error) {
      if (__DEV__) {
        console.log('Notification was received but encountered error', error);
      }
    }
  }

  async onNotificationOpenedListener(notification) {
    if (__DEV__) {
      console.log(
        '[LocalNotificationService]: onNotificationOpenedListener',
        JSON.stringify(notification, null, 2),
      );
    }
    const navigationData = mapNotificationScreens(notification?.data);

    if (!navigationRef.current?.navigate) {
      await AsyncStorage.setItem(
        persistent.TEMPORARY_NAVIGATION_STORE,
        JSON.stringify(navigationData),
      );
    } else {
      navigationRef.current?.navigate?.(
        mapNotificationScreens(notification?.data),
      );
    }
  }

  showNotification({
    id = null,
    title = '',
    body = '',
    android = {},
    ios = {},
    data = {},
  }) {
    return notifee.displayNotification({
      ...(id == null ? {} : {id}),
      title,
      body,
      android: {
        ...defaultOptions.showNotification.android,
        ...android,
      },
      ios: {
        ...defaultOptions.showNotification.ios,
        ...ios,
      },
      data,
    });
  }

  cancelNotification(id) {
    return notifee.cancelNotification(id);
  }

  cancelAllLocalNotifications() {
    return notifee.cancelAllNotifications();
  }
}

export const localNotificationService = new LocalNotificationService();
